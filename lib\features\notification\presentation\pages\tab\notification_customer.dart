import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:golderhr/core/extensions/l10n_extension.dart';
import 'package:golderhr/core/extensions/responsive_extension.dart';

import '../../../../../shared/widgets/responsive_spacer.dart';
import '../../cubit/notification_cubit.dart';
import '../../cubit/notification_state.dart';
import '../../widgets/empty_notification_widget.dart';
import '../../widgets/notification_card.dart';

class NotificationCustomerTab extends StatelessWidget {
  const NotificationCustomerTab({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<NotificationCubit, NotificationState>(
      builder: (context, state) {
        if (state is NotificationLoaded) {
          final customerNotifications = state.customerNotifications;

          if (customerNotifications.isEmpty) {
            return EmptyNotificationWidget(
              message: context.l10n.notificationEmptyFromCustomer,
            );
          }

          return ListView.separated(
            padding: context.responsive.padding(all: 16.0),
            itemCount: customerNotifications.length,
            separatorBuilder: (_, __) => ResponsiveSpacer(
              mobileSize: 12,
              tabletSize: 14,
              mobileLandscapeSize: 12,
              tabletLandscapeSize: 14,
            ),
            itemBuilder: (context, index) {
              final notif = customerNotifications[index];
              return NotificationCard(
                notification: notif,

                onMarkAsRead: () {
                  if (!notif.isRead) {
                    context.read<NotificationCubit>().markAsRead(notif.id);
                  }
                },
              );
            },
          );
        }
        return const SizedBox.shrink();
      },
    );
  }
}
