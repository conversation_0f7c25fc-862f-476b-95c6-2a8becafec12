import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../core/extensions/l10n_extension.dart';
import '../../../../core/extensions/calendar_l10n_extension.dart';
import '../../../../core/extensions/responsive_extension.dart';
import '../../../../shared/theme/app_colors.dart';
import '../../domain/entities/calendar_event.dart';
import '../cubit/calendar_cubit.dart';
import '../cubit/calendar_state.dart';
import 'calendar_grid_widget.dart';
import 'event_list_widget.dart';

/// Widget hiển thị calendar theo tháng
class MonthViewWidget extends StatelessWidget {
  const MonthViewWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<CalendarCubit, CalendarState>(
      builder: (context, state) {
        return SingleChildScrollView(
          padding: context.responsive.padding(horizontal: 16, vertical: 8),
          child: Column(
            children: [
              _buildCalendarCard(context),
              Si<PERSON><PERSON><PERSON>(height: context.rh(16)),
              _buildSelectedDateEvents(context, state),
            ],
          ),
        );
      },
    );
  }

  Widget _buildCalendarCard(BuildContext context) {
    return Container(
      padding: context.responsive.padding(all: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.04),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          _buildDayHeaders(context),
          SizedBox(height: context.rh(8)),
          const CalendarGridWidget(),
        ],
      ),
    );
  }

  Widget _buildDayHeaders(BuildContext context) {
    final l10n = context.l10n;
    final dayAbbreviations = l10n.calendarDayAbbreviations;

    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceAround,
      children: dayAbbreviations.map((day) {
        return Expanded(
          child: Container(
            padding: context.responsive.padding(vertical: 8),
            child: Text(
              day,
              textAlign: TextAlign.center,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                fontWeight: FontWeight.w600,
                color: AppColors.textSecondary,
                letterSpacing: 0.5,
              ),
            ),
          ),
        );
      }).toList(),
    );
  }

  Widget _buildSelectedDateEvents(BuildContext context, CalendarState state) {
    if (state is! CalendarEventsLoaded) {
      return const SizedBox.shrink();
    }

    final cubit = context.read<CalendarCubit>();
    final selectedDate = cubit.selectedDate;
    final eventsForDate = cubit.getEventsForDate(selectedDate);

    if (eventsForDate.isEmpty) {
      return _buildNoEventsCard(context, selectedDate);
    }

    return _buildEventsCard(context, selectedDate, eventsForDate);
  }

  Widget _buildNoEventsCard(BuildContext context, DateTime selectedDate) {
    return Container(
      width: double.infinity,
      padding: context.responsive.padding(all: 24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.04),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          Icon(
            Icons.event_busy_outlined,
            size: context.rf(48),
            color: AppColors.textSecondary.withOpacity(0.5),
          ),
          SizedBox(height: context.rh(12)),
          Text(
            "No events found",
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: AppColors.textSecondary,
              fontWeight: FontWeight.w500,
            ),
          ),
          SizedBox(height: context.rh(4)),
          Text(
            _getDateString(context, selectedDate),
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: AppColors.textSecondary.withOpacity(0.7),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEventsCard(
    BuildContext context,
    DateTime selectedDate,
    List<CalendarEvent> events,
  ) {
    return Container(
      padding: context.responsive.padding(all: 20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.04),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: EdgeInsets.all(context.rw(8)),
                decoration: BoxDecoration(
                  color: AppColors.primaryBlue.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  Icons.event_note,
                  color: AppColors.primaryBlue,
                  size: context.rf(20),
                ),
              ),
              SizedBox(width: context.rw(12)),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Events on ${_getDateString(context, selectedDate)}',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        color: AppColors.textPrimary,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Text(
                      '${events.length} ${events.length == 1 ? "event" : "events"}',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: AppColors.textSecondary,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          SizedBox(height: context.rh(16)),
          EventListWidget(events: events),
        ],
      ),
    );
  }

  String _getDateString(BuildContext context, DateTime date) {
    final l10n = context.l10n;
    final monthNames = l10n.calendarShortMonthNames;
    return '${date.day} ${monthNames[date.month - 1]} ${date.year}';
  }
}
