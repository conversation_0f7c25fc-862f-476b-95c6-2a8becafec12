import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:golderhr/core/extensions/app_theme_extension.dart';
import 'package:golderhr/core/extensions/l10n_extension.dart';
import 'package:golderhr/core/extensions/responsive_extension.dart';

import 'package:golderhr/shared/widgets/show_custom_snackBar.dart';
import 'package:iconsax/iconsax.dart';

import '../cubit/department_cubit.dart';
import '../widgets/create_department_dialog.dart';
import '../widgets/edit_department_dialog.dart';
import '../widgets/department_search_widget.dart';
import '../widgets/department_card_widget.dart';
import '../widgets/department_empty_state_widget.dart';
import '../widgets/department_delete_confirmation_dialog.dart';
import '../../domain/entities/department_entity.dart';

class DepartmentManagementPage extends StatefulWidget {
  const DepartmentManagementPage({super.key});

  @override
  State<DepartmentManagementPage> createState() =>
      _DepartmentManagementPageState();
}

class _DepartmentManagementPageState extends State<DepartmentManagementPage> {
  final TextEditingController _searchController = TextEditingController();
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _loadInitialData();
    _setupScrollListener();
  }

  void _loadInitialData() {
    context.read<DepartmentCubit>().loadDepartments();
  }

  void _setupScrollListener() {
    _scrollController.addListener(() {
      if (_scrollController.position.pixels >=
          _scrollController.position.maxScrollExtent * 0.8) {
        context.read<DepartmentCubit>().loadMoreDepartments(
          search: _searchController.text.trim().isEmpty
              ? null
              : _searchController.text.trim(),
        );
      }
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = context.lightTheme;
    final responsive = context.responsive;
    final l10n = context.l10n;

    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Department Management',
          style: theme.textTheme.displaySmall!.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        centerTitle: true,
        actions: [
          IconButton(
            onPressed: () => _showCreateDepartmentDialog(context),
            icon: const Icon(Iconsax.add),
            tooltip: 'Add Department',
          ),
        ],
      ),
      body: BlocConsumer<DepartmentCubit, DepartmentState>(
        listener: (context, state) {
          if (state.hasError) {
            showTopSnackBar(
              context,
              title: l10n.error,
              message: state.error!,
              isError: true,
            );
            context.read<DepartmentCubit>().clearMessages();
          } else if (state.hasSuccess) {
            showTopSnackBar(
              context,
              title: l10n.success,
              message: state.successMessage!,
              isError: false,
            );
            context.read<DepartmentCubit>().clearMessages();
          }
        },
        builder: (context, state) {
          return Column(
            children: [
              // Search Section
              DepartmentSearchWidget(
                searchController: _searchController,
                onSearchChanged: () => setState(() {}),
              ),

              // Departments List
              Expanded(
                child: state.isLoading && state.departments.isEmpty
                    ? const Center(child: CircularProgressIndicator())
                    : state.departments.isEmpty
                    ? DepartmentEmptyStateWidget(
                        onCreateDepartment: () =>
                            _showCreateDepartmentDialog(context),
                      )
                    : RefreshIndicator(
                        onRefresh: () =>
                            context.read<DepartmentCubit>().refreshDepartments(
                              search: _searchController.text.trim().isEmpty
                                  ? null
                                  : _searchController.text.trim(),
                            ),
                        child: ListView.builder(
                          controller: _scrollController,
                          padding: responsive.padding(all: 16),
                          itemCount:
                              state.departments.length +
                              (state.isLoading ? 1 : 0),
                          itemBuilder: (context, index) {
                            if (index >= state.departments.length) {
                              return const Center(
                                child: Padding(
                                  padding: EdgeInsets.all(16),
                                  child: CircularProgressIndicator(),
                                ),
                              );
                            }

                            final department = state.departments[index];
                            return DepartmentCardWidget(
                              department: department,
                              onDepartmentAction: (action) =>
                                  _handleDepartmentAction(
                                    context,
                                    department,
                                    action,
                                  ),
                            );
                          },
                        ),
                      ),
              ),
            ],
          );
        },
      ),
    );
  }

  void _handleDepartmentAction(
    BuildContext context,
    DepartmentEntity department,
    String action,
  ) {
    switch (action) {
      case 'edit':
        _showEditDepartmentDialog(context, department);
        break;
      case 'toggle_status':
        context.read<DepartmentCubit>().toggleExistingDepartmentStatus(
          department.id,
        );
        break;
      case 'delete':
        _showDeleteConfirmation(context, department);
        break;
      case 'restore':
        context.read<DepartmentCubit>().restoreExistingDepartment(
          department.id,
        );
        break;
    }
  }

  void _showCreateDepartmentDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => const CreateDepartmentDialog(),
    );
  }

  void _showEditDepartmentDialog(
    BuildContext context,
    DepartmentEntity department,
  ) {
    showDialog(
      context: context,
      builder: (context) => EditDepartmentDialog(department: department),
    );
  }

  void _showDeleteConfirmation(
    BuildContext context,
    DepartmentEntity department,
  ) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) =>
          DepartmentDeleteConfirmationDialog(department: department),
    );
  }
}
