import 'package:flutter/material.dart';
import '../../../../core/extensions/responsive_extension.dart';
import '../../../../shared/theme/app_colors.dart';
import '../../domain/entities/calendar_event.dart';

/// Widget hiển thị thông tin event dưới dạng card
class EventCardWidget extends StatelessWidget {
  final CalendarEvent event;
  final bool showDate;
  final VoidCallback? onTap;

  const EventCardWidget({
    super.key,
    required this.event,
    this.showDate = false,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap ?? () => _showEventOptions(context),
      child: Container(
        padding: context.responsive.padding(all: 12),
        decoration: BoxDecoration(
          color: event.color.withOpacity(0.08),
          borderRadius: BorderRadius.circular(10),
          border: Border(left: BorderSide(color: event.color, width: 4)),
        ),
        child: Row(
          children: [
            _buildEventIcon(context),
            SizedBox(width: context.rw(12)),
            Expanded(child: _buildEventInfo(context)),
            _buildEventTime(context),
          ],
        ),
      ),
    );
  }

  Widget _buildEventIcon(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(context.rw(8)),
      decoration: BoxDecoration(
        color: event.color.withOpacity(0.15),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Icon(event.type.icon, color: event.color, size: context.rf(18)),
    );
  }

  Widget _buildEventInfo(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          event.title,
          style: Theme.of(context).textTheme.bodyLarge?.copyWith(
            fontWeight: FontWeight.w600,
            color: AppColors.textPrimary,
          ),
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
        if (event.description.isNotEmpty) ...[
          SizedBox(height: context.rh(2)),
          Text(
            event.description,
            style: Theme.of(
              context,
            ).textTheme.bodySmall?.copyWith(color: AppColors.textSecondary),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ],
        if (event.location != null && event.location!.isNotEmpty) ...[
          SizedBox(height: context.rh(2)),
          Row(
            children: [
              Icon(
                Icons.location_on_outlined,
                size: context.rf(12),
                color: AppColors.textSecondary,
              ),
              SizedBox(width: context.rw(4)),
              Expanded(
                child: Text(
                  event.location!,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: AppColors.textSecondary,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
        ],
        if (showDate) ...[
          SizedBox(height: context.rh(2)),
          Text(
            _getDateString(event.date),
            style: Theme.of(
              context,
            ).textTheme.bodySmall?.copyWith(color: AppColors.textSecondary),
          ),
        ],
      ],
    );
  }

  Widget _buildEventTime(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.end,
      children: [
        Container(
          padding: EdgeInsets.symmetric(
            horizontal: context.rw(8),
            vertical: context.rh(4),
          ),
          decoration: BoxDecoration(
            color: event.color.withOpacity(0.1),
            borderRadius: BorderRadius.circular(6),
          ),
          child: Text(
            event.time,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: event.color,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
        if (event.isAllDay) ...[
          SizedBox(height: context.rh(4)),
          Container(
            padding: EdgeInsets.symmetric(
              horizontal: context.rw(6),
              vertical: context.rh(2),
            ),
            decoration: BoxDecoration(
              color: AppColors.info.withOpacity(0.1),
              borderRadius: BorderRadius.circular(4),
            ),
            child: Text(
              'All Day',
              style: Theme.of(context).textTheme.labelSmall?.copyWith(
                color: AppColors.info,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
        if (event.isRecurring) ...[
          SizedBox(height: context.rh(4)),
          Icon(
            Icons.repeat,
            size: context.rf(14),
            color: AppColors.textSecondary,
          ),
        ],
      ],
    );
  }

  void _showEventOptions(BuildContext context) {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => Container(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: Colors.grey.withOpacity(0.3),
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            const SizedBox(height: 20),
            Text(
              event.title,
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ListTile(
              leading: const Icon(Icons.edit_outlined),
              title: const Text('Edit Event'),
              onTap: () {
                Navigator.pop(context);
                // TODO: Implement edit event
              },
            ),
            ListTile(
              leading: const Icon(Icons.delete_outline, color: Colors.red),
              title: const Text(
                'Delete Event',
                style: TextStyle(color: Colors.red),
              ),
              onTap: () {
                Navigator.pop(context);
                // TODO: Implement delete event
              },
            ),
            const SizedBox(height: 16),
          ],
        ),
      ),
    );
  }

  String _getDateString(DateTime date) {
    const monthNames = [
      'Jan',
      'Feb',
      'Mar',
      'Apr',
      'May',
      'Jun',
      'Jul',
      'Aug',
      'Sep',
      'Oct',
      'Nov',
      'Dec',
    ];
    return '${date.day} ${monthNames[date.month - 1]}';
  }
}

/// Widget hiển thị event card với style compact
class CompactEventCardWidget extends StatelessWidget {
  final CalendarEvent event;
  final VoidCallback? onTap;

  const CompactEventCardWidget({super.key, required this.event, this.onTap});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: context.responsive.padding(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          color: event.color.withOpacity(0.1),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: event.color.withOpacity(0.3), width: 1),
        ),
        child: Row(
          children: [
            Container(
              width: context.rw(4),
              height: context.rw(4),
              decoration: BoxDecoration(
                color: event.color,
                shape: BoxShape.circle,
              ),
            ),
            SizedBox(width: context.rw(8)),
            Expanded(
              child: Text(
                event.title,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: AppColors.textPrimary,
                  fontWeight: FontWeight.w500,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
            Text(
              event.time,
              style: Theme.of(
                context,
              ).textTheme.labelSmall?.copyWith(color: AppColors.textSecondary),
            ),
          ],
        ),
      ),
    );
  }
}
