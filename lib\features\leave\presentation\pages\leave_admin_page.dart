import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:golderhr/core/extensions/l10n_extension.dart';
import '../../../../injection_container.dart' as di;
import '../../../attendance/presentation/widgets/loading_widget.dart';
import '../../../../shared/widgets/show_custom_snackBar.dart';
import '../cubit/leave_admin_cubit.dart';
import '../widgets/leave_admin_filter_widget.dart';
import '../widgets/leave_admin_list_widget.dart';

class LeaveAdminPage extends StatelessWidget {
  const LeaveAdminPage({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => di.sl<LeaveAdminCubit>()..loadAllRequests(),
      child: const LeaveAdminView(),
    );
  }
}

class LeaveAdminView extends StatefulWidget {
  const LeaveAdminView({super.key});

  @override
  State<LeaveAdminView> createState() => _LeaveAdminViewState();
}

class _LeaveAdminViewState extends State<LeaveAdminView> {
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent * 0.8) {
      context.read<LeaveAdminCubit>().loadMore();
    }
  }

  @override
  Widget build(BuildContext context) {
    final l10n = context.l10n;

    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: Text(
          'Leave Management',
          style: const TextStyle(fontWeight: FontWeight.w700, fontSize: 20),
        ),
        backgroundColor: Colors.white,
        foregroundColor: Colors.black87,
        elevation: 0,
        shadowColor: Colors.black12,
        surfaceTintColor: Colors.transparent,
        actions: [
          IconButton(
            icon: Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.blue.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: const Icon(
                Icons.refresh_rounded,
                color: Colors.blue,
                size: 20,
              ),
            ),
            onPressed: () {
              context.read<LeaveAdminCubit>().refresh();
            },
          ),
          const SizedBox(width: 16),
        ],
      ),
      body: BlocConsumer<LeaveAdminCubit, LeaveAdminState>(
        listener: (context, state) {
          if (state.errorMessage != null) {
            showTopSnackBar(
              context,
              title: 'Error',
              message: state.errorMessage!,
              isError: true,
            );
            context.read<LeaveAdminCubit>().clearMessages();
          }

          if (state.successMessage != null) {
            showTopSnackBar(
              context,
              title: 'Success',
              message: state.successMessage!,
              isError: false,
            );
            context.read<LeaveAdminCubit>().clearMessages();
          }
        },
        builder: (context, state) {
          if (state.isLoading && state.requests.isEmpty) {
            return const LoadingWidget();
          }

          return RefreshIndicator(
            onRefresh: () async {
              context.read<LeaveAdminCubit>().refresh();
            },
            color: Colors.blue,
            backgroundColor: Colors.white,
            child: Column(
              children: [
                // Filter Section
                Container(
                  color: Colors.white,
                  child: const LeaveAdminFilterWidget(),
                ),

                // Requests List
                Expanded(
                  child: state.requests.isEmpty
                      ? _buildEmptyState(context)
                      : LeaveAdminListWidget(
                          requests: state.requests,
                          scrollController: _scrollController,
                          isLoadingMore: state.isLoadingMore,
                          isProcessing: state.isProcessing,
                        ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    final l10n = context.l10n;

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              color: Colors.blue.withValues(alpha: 0.1),
              shape: BoxShape.circle,
            ),
            child: Icon(
              Icons.assignment_outlined,
              size: 64,
              color: Colors.blue[400],
            ),
          ),
          const SizedBox(height: 24),
          Text(
            'No Leave Requests Found',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              color: Colors.grey[700],
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 12),
          Text(
            'Try changing the filter or check back later',
            style: Theme.of(
              context,
            ).textTheme.bodyMedium?.copyWith(color: Colors.grey[500]),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 32),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
            decoration: BoxDecoration(
              color: Colors.blue.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(20),
              border: Border.all(color: Colors.blue.withValues(alpha: 0.2)),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  Icons.info_outline_rounded,
                  size: 16,
                  color: Colors.blue[600],
                ),
                const SizedBox(width: 8),
                Text(
                  'Pull down to refresh',
                  style: TextStyle(
                    color: Colors.blue[600],
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
