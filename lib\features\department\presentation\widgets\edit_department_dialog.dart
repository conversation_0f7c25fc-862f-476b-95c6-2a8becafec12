import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:golderhr/core/extensions/app_theme_extension.dart';
import 'package:golderhr/core/extensions/l10n_extension.dart';
import 'package:golderhr/core/extensions/responsive_extension.dart';
import 'package:golderhr/shared/theme/app_text_styles.dart';
import 'package:iconsax/iconsax.dart';

import '../cubit/department_cubit.dart';
import '../../domain/entities/department_entity.dart';

class EditDepartmentDialog extends StatefulWidget {
  final DepartmentEntity department;

  const EditDepartmentDialog({super.key, required this.department});

  @override
  State<EditDepartmentDialog> createState() => _EditDepartmentDialogState();
}

class _EditDepartmentDialogState extends State<EditDepartmentDialog> {
  final _formKey = GlobalKey<FormState>();
  late TextEditingController _nameController;
  late TextEditingController _descriptionController;
  late TextEditingController _codeController;

  String? _selectedParentId;
  List<DepartmentEntity> _parentDepartments = [];

  @override
  void initState() {
    super.initState();
    _initializeControllers();
    _loadParentDepartments();
  }

  void _initializeControllers() {
    _nameController = TextEditingController(text: widget.department.name);
    _descriptionController = TextEditingController(
      text: widget.department.description ?? '',
    );
    _codeController = TextEditingController(text: widget.department.code ?? '');
    _selectedParentId = widget.department.parentId;
  }

  void _loadParentDepartments() {
    context.read<DepartmentCubit>().loadDepartmentsForDropdown();
  }

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    _codeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = context.lightTheme;
    final responsive = context.responsive;
    final l10n = context.l10n;

    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(responsive.defaultRadius),
      ),
      child: Container(
        width: responsive.adaptiveValue<double>(
          mobile: responsive.widthPercentage(90),
          tablet: responsive.widthPercentage(70),
          mobileLandscape: responsive.widthPercentage(80),
          tabletLandscape: responsive.widthPercentage(60),
        ),
        constraints: BoxConstraints(
          maxWidth: responsive.adaptiveValue<double>(
            mobile: 500,
            tablet: 600,
            mobileLandscape: 550,
            tabletLandscape: 650,
          ),
          maxHeight: responsive.heightPercentage(85),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header
            Container(
              padding: responsive.padding(all: 20),
              decoration: BoxDecoration(
                color: theme.primaryColor,
                borderRadius: BorderRadius.vertical(
                  top: Radius.circular(responsive.defaultRadius),
                ),
              ),
              child: Row(
                children: [
                  CircleAvatar(
                    radius: responsive.adaptiveValue<double>(
                      mobile: 18,
                      tablet: 20,
                      mobileLandscape: 19,
                      tabletLandscape: 22,
                    ),
                    backgroundColor: Colors.white.withValues(alpha: 0.2),
                    child: Icon(
                      Iconsax.building,
                      color: Colors.white,
                      size: responsive.adaptiveValue<double>(
                        mobile: 18,
                        tablet: 20,
                        mobileLandscape: 19,
                        tabletLandscape: 22,
                      ),
                    ),
                  ),
                  SizedBox(width: responsive.scaleWidth(16)),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Edit Department',
                          style: AppTextStyle.bold(
                            context,
                            size: 18,
                            color: Colors.white,
                          ),
                        ),
                        Text(
                          'Update department information',
                          style: AppTextStyle.regular(
                            context,
                            size: 14,
                            color: Colors.white.withValues(alpha: 0.9),
                          ),
                        ),
                      ],
                    ),
                  ),
                  IconButton(
                    onPressed: () => Navigator.pop(context),
                    icon: const Icon(Icons.close, color: Colors.white),
                  ),
                ],
              ),
            ),

            // Form Content
            Expanded(
              child: BlocConsumer<DepartmentCubit, DepartmentState>(
                listener: (context, state) {
                  if (!state.isProcessing &&
                      !state.hasError &&
                      state.hasSuccess) {
                    Navigator.pop(context);
                  }

                  // Update parent departments list
                  if (state.dropdownDepartments.isNotEmpty) {
                    setState(() {
                      _parentDepartments = state.dropdownDepartments
                          .where(
                            (dept) => dept.id != widget.department.id,
                          ) // Exclude self
                          .toList();
                    });
                  }
                },
                builder: (context, state) {
                  if (state.isProcessing) {
                    return const Center(child: CircularProgressIndicator());
                  }

                  return SingleChildScrollView(
                    padding: responsive.responsivePadding,
                    child: Form(
                      key: _formKey,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Basic Information Section
                          _buildSectionHeader(
                            'Basic Information',
                            Iconsax.building,
                          ),
                          SizedBox(height: responsive.defaultSpacing),

                          _buildTextField(
                            controller: _nameController,
                            label: 'Department Name *',
                            icon: Iconsax.building,
                            validator: (value) =>
                                _validateDepartmentName(value, context),
                          ),

                          SizedBox(height: responsive.defaultSpacing),

                          _buildTextField(
                            controller: _descriptionController,
                            label: 'Description',
                            icon: Iconsax.document_text,
                            maxLines: 3,
                          ),

                          SizedBox(height: responsive.defaultSpacing),

                          _buildTextField(
                            controller: _codeController,
                            label: 'Department Code',
                            icon: Iconsax.code,
                            validator: (value) =>
                                _validateDepartmentCode(value, context),
                          ),

                          SizedBox(height: responsive.defaultSpacing * 1.5),

                          // Hierarchy Section
                          _buildSectionHeader(
                            'Hierarchy',
                            Iconsax.hierarchy_square_2,
                          ),
                          SizedBox(height: responsive.defaultSpacing),

                          // Parent Department Dropdown
                          DropdownButtonFormField<String>(
                            value: _selectedParentId,
                            decoration: InputDecoration(
                              labelText: 'Parent Department',
                              labelStyle: AppTextStyle.regular(
                                context,
                                size: 12,
                              ),
                              prefixIcon: Icon(Iconsax.hierarchy_square_2),
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(
                                  responsive.defaultRadius,
                                ),
                              ),
                              filled: true,
                              fillColor: Colors.grey.shade50,
                              contentPadding: responsive.padding(
                                horizontal: 16,
                                vertical: 12,
                              ),
                            ),
                            hint: const Text(
                              'Select parent department (optional)',
                            ),
                            items: [
                              const DropdownMenuItem<String>(
                                value: null,
                                child: Text('No Parent (Top Level)'),
                              ),
                              ..._parentDepartments.map((dept) {
                                return DropdownMenuItem<String>(
                                  value: dept.id,
                                  child: Text(dept.name),
                                );
                              }),
                            ],
                            onChanged: (value) {
                              setState(() {
                                _selectedParentId = value;
                              });
                            },
                          ),

                          SizedBox(height: responsive.defaultSpacing),

                          // Department Status
                          Container(
                            padding: responsive.padding(all: 12),
                            decoration: BoxDecoration(
                              color: Colors.grey.shade50,
                              borderRadius: BorderRadius.circular(
                                responsive.defaultRadius,
                              ),
                              border: Border.all(color: Colors.grey.shade300),
                            ),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'Department Status',
                                  style: AppTextStyle.bold(
                                    context,
                                    size: 14,
                                    color: Colors.grey[700],
                                  ),
                                ),
                                SizedBox(height: responsive.scaleHeight(8)),
                                _buildStatusRow(
                                  'Active',
                                  widget.department.isActive,
                                ),
                                _buildStatusRow(
                                  'Disabled',
                                  widget.department.isDisabled,
                                ),
                                _buildStatusRow(
                                  'Deleted',
                                  widget.department.isDeleted,
                                ),
                                if (widget.department.createdAt != null)
                                  _buildDetailRow(
                                    'Created',
                                    _formatDate(widget.department.createdAt!),
                                  ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  );
                },
              ),
            ),

            // Action Buttons
            Container(
              padding: responsive.padding(all: 20),
              decoration: BoxDecoration(
                color: Colors.grey.shade50,
                borderRadius: BorderRadius.vertical(
                  bottom: Radius.circular(responsive.defaultRadius),
                ),
              ),
              child: Row(
                children: [
                  Expanded(
                    child: OutlinedButton(
                      onPressed: () => Navigator.pop(context),
                      style: OutlinedButton.styleFrom(
                        padding: responsive.padding(vertical: 16),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(
                            responsive.defaultRadius,
                          ),
                        ),
                      ),
                      child: Text(
                        l10n.cancel,
                        style: AppTextStyle.medium(context, size: 14),
                      ),
                    ),
                  ),
                  SizedBox(width: responsive.scaleWidth(16)),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: _updateDepartment,
                      style: ElevatedButton.styleFrom(
                        padding: responsive.padding(vertical: 16),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(
                            responsive.defaultRadius,
                          ),
                        ),
                      ),
                      child: Text(
                        'Update Department',
                        style: AppTextStyle.medium(
                          context,
                          size: 14,
                          color: Colors.white,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionHeader(String title, IconData icon) {
    final responsive = context.responsive;
    return Row(
      children: [
        Icon(icon, size: 18, color: Colors.grey[700]),
        SizedBox(width: responsive.scaleWidth(8)),
        Text(
          title,
          style: AppTextStyle.bold(context, size: 16, color: Colors.black87),
        ),
      ],
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required IconData icon,
    String? Function(String?)? validator,
    int maxLines = 1,
  }) {
    final responsive = context.responsive;
    return TextFormField(
      controller: controller,
      validator: validator,
      maxLines: maxLines,
      style: AppTextStyle.regular(context, size: 14),
      decoration: InputDecoration(
        labelText: label,
        labelStyle: AppTextStyle.regular(context, size: 12),
        prefixIcon: Icon(icon),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(responsive.defaultRadius),
        ),
        filled: true,
        fillColor: Colors.grey.shade50,
        contentPadding: responsive.padding(horizontal: 16, vertical: 12),
      ),
    );
  }

  Widget _buildStatusRow(String label, bool value) {
    final responsive = context.responsive;
    return Padding(
      padding: responsive.padding(bottom: 4),
      child: Row(
        children: [
          SizedBox(
            width: responsive.scaleWidth(80),
            child: Text(
              '$label:',
              style: AppTextStyle.regular(
                context,
                size: 12,
                color: Colors.grey[600],
              ),
            ),
          ),
          Icon(
            value ? Iconsax.tick_circle : Iconsax.close_circle,
            size: 16,
            color: value ? Colors.green : Colors.red,
          ),
          SizedBox(width: responsive.scaleWidth(4)),
          Text(
            value ? 'Yes' : 'No',
            style: AppTextStyle.regular(
              context,
              size: 12,
              color: value ? Colors.green : Colors.red,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    final responsive = context.responsive;
    return Padding(
      padding: responsive.padding(bottom: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: responsive.scaleWidth(80),
            child: Text(
              '$label:',
              style: AppTextStyle.regular(
                context,
                size: 12,
                color: Colors.grey[600],
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: AppTextStyle.regular(
                context,
                size: 12,
                color: Colors.grey[800],
              ),
            ),
          ),
        ],
      ),
    );
  }

  String? _validateDepartmentName(String? value, BuildContext context) {
    if (value == null || value.trim().isEmpty) {
      return 'Department name is required';
    }
    if (value.trim().length < 2) {
      return 'Department name must be at least 2 characters';
    }
    if (value.trim().length > 100) {
      return 'Department name must be less than 100 characters';
    }
    return null;
  }

  String? _validateDepartmentCode(String? value, BuildContext context) {
    if (value != null && value.trim().isNotEmpty) {
      if (value.trim().length < 2) {
        return 'Department code must be at least 2 characters';
      }
      if (value.trim().length > 10) {
        return 'Department code must be less than 10 characters';
      }
      if (!RegExp(r'^[a-zA-Z0-9\-_]+$').hasMatch(value.trim())) {
        return 'Department code contains invalid characters';
      }
    }
    return null;
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  void _updateDepartment() {
    if (_formKey.currentState!.validate()) {
      context.read<DepartmentCubit>().updateExistingDepartment(
        departmentId: widget.department.id,
        name: _nameController.text.trim() != widget.department.name
            ? _nameController.text.trim()
            : null,
        description:
            _descriptionController.text.trim() !=
                (widget.department.description ?? '')
            ? (_descriptionController.text.trim().isEmpty
                  ? null
                  : _descriptionController.text.trim())
            : null,
        code: _codeController.text.trim() != (widget.department.code ?? '')
            ? (_codeController.text.trim().isEmpty
                  ? null
                  : _codeController.text.trim())
            : null,
        parentId: _selectedParentId != widget.department.parentId
            ? _selectedParentId
            : null,
      );
    }
  }
}
