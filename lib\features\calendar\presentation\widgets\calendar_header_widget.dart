import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../core/extensions/l10n_extension.dart';
import '../../../../core/extensions/calendar_l10n_extension.dart';
import '../../../../core/extensions/responsive_extension.dart';
import '../../../../shared/theme/app_colors.dart';
import '../cubit/calendar_cubit.dart';
import '../cubit/calendar_state.dart';

/// Widget header cho Calendar với thống kê và navigation
class CalendarHeaderWidget extends StatelessWidget {
  const CalendarHeaderWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<CalendarCubit, CalendarState>(
      builder: (context, state) {
        final cubit = context.read<CalendarCubit>();
        final currentMonth = cubit.currentMonth;

        return Container(
          margin: context.responsive.padding(horizontal: 16),
          padding: context.responsive.padding(horizontal: 20, vertical: 24),
          decoration: BoxDecoration(
            gradient: const LinearGradient(
              colors: [AppColors.primaryBlue, AppColors.primaryGreen],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            borderRadius: BorderRadius.circular(20),
            boxShadow: [
              BoxShadow(
                color: AppColors.primaryBlue.withOpacity(0.3),
                blurRadius: 15,
                offset: const Offset(0, 8),
              ),
            ],
          ),
          child: Column(
            children: [
              _buildMonthNavigation(context, cubit, currentMonth),
              SizedBox(height: context.rh(20)),
              _buildStatCards(context, state),
            ],
          ),
        );
      },
    );
  }

  Widget _buildMonthNavigation(
    BuildContext context,
    CalendarCubit cubit,
    DateTime currentMonth,
  ) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        _buildNavigationButton(
          context,
          icon: Icons.chevron_left,
          onPressed: () => cubit.previousMonth(),
        ),
        Text(
          _getMonthYearString(context, currentMonth),
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        _buildNavigationButton(
          context,
          icon: Icons.chevron_right,
          onPressed: () => cubit.nextMonth(),
        ),
      ],
    );
  }

  Widget _buildNavigationButton(
    BuildContext context, {
    required IconData icon,
    required VoidCallback onPressed,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: IconButton(
        onPressed: onPressed,
        icon: Icon(icon, color: Colors.white, size: 28),
        padding: EdgeInsets.all(context.rw(8)),
      ),
    );
  }

  Widget _buildStatCards(BuildContext context, CalendarState state) {
    // Lấy thống kê từ state hoặc sử dụng giá trị mặc định
    String eventsCount = "12";
    String meetingsCount = "8";
    String holidaysCount = "3";

    if (state is CalendarEventsLoaded && state.summary != null) {
      final summary = state.summary!;
      eventsCount = summary.totalEvents.toString();
      meetingsCount = summary.totalMeetings.toString();
      holidaysCount = summary.totalHolidays.toString();
    }

    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceAround,
      children: [
        _buildStatCard(
          context,
          title: "Events",
          count: eventsCount,
          icon: Icons.event_available_outlined,
        ),
        _buildStatCard(
          context,
          title: "Meetings",
          count: meetingsCount,
          icon: Icons.people_alt_outlined,
        ),
        _buildStatCard(
          context,
          title: "Holidays",
          count: holidaysCount,
          icon: Icons.celebration_outlined,
        ),
      ],
    );
  }

  Widget _buildStatCard(
    BuildContext context, {
    required String title,
    required String count,
    required IconData icon,
  }) {
    return Expanded(
      child: Container(
        margin: EdgeInsets.symmetric(horizontal: context.rw(4)),
        padding: context.responsive.padding(all: 12),
        decoration: BoxDecoration(
          color: Colors.white.withOpacity(0.15),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.white.withOpacity(0.2), width: 1),
        ),
        child: Column(
          children: [
            Container(
              padding: EdgeInsets.all(context.rw(8)),
              decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(icon, color: Colors.white, size: context.rf(20)),
            ),
            SizedBox(height: context.rh(8)),
            Text(
              count,
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: context.rh(2)),
            Text(
              title,
              style: Theme.of(context).textTheme.labelSmall?.copyWith(
                color: Colors.white.withOpacity(0.9),
              ),
              textAlign: TextAlign.center,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }

  String _getMonthYearString(BuildContext context, DateTime date) {
    final l10n = context.l10n;
    final monthNames = l10n.calendarFullMonthNames;
    return '${monthNames[date.month - 1]} ${date.year}';
  }
}
