import 'package:dartz/dartz.dart';
import '../../../../core/error/failures.dart';
import '../entities/calendar_event.dart';
import '../entities/calendar_summary.dart';

/// Repository interface cho Calendar feature
/// <PERSON><PERSON><PERSON> nghĩa các phương thức để tương tác với dữ liệu calendar
abstract class CalendarRepository {
  /// Lấy tất cả events
  Future<Either<Failure, List<CalendarEvent>>> getAllEvents();

  /// Lấy events theo ngày
  Future<Either<Failure, List<CalendarEvent>>> getEventsByDate(DateTime date);

  /// Lấy events trong khoảng thời gian
  Future<Either<Failure, List<CalendarEvent>>> getEventsByDateRange(
    DateTime startDate,
    DateTime endDate,
  );

  /// Lấy events theo tháng
  Future<Either<Failure, List<CalendarEvent>>> getEventsByMonth(
    int year,
    int month,
  );

  /// Lấy events theo tuần
  Future<Either<Failure, List<CalendarEvent>>> getEventsByWeek(
    DateTime startOfWeek,
  );

  /// Lấy events sắp tới
  Future<Either<Failure, List<CalendarEvent>>> getUpcomingEvents({
    int limit = 10,
  });

  /// Lấy events theo loại
  Future<Either<Failure, List<CalendarEvent>>> getEventsByType(EventType type);

  /// Lấy event theo ID
  Future<Either<Failure, CalendarEvent>> getEventById(String id);

  /// Thêm event mới
  Future<Either<Failure, CalendarEvent>> addEvent(CalendarEvent event);

  /// Cập nhật event
  Future<Either<Failure, CalendarEvent>> updateEvent(CalendarEvent event);

  /// Xóa event
  Future<Either<Failure, void>> deleteEvent(String id);

  /// Xóa nhiều events
  Future<Either<Failure, void>> deleteEvents(List<String> ids);

  /// Tìm kiếm events
  Future<Either<Failure, List<CalendarEvent>>> searchEvents(String query);

  /// Lấy thống kê calendar
  Future<Either<Failure, CalendarSummary>> getCalendarSummary();

  /// Lấy thống kê theo tháng
  Future<Either<Failure, CalendarSummary>> getMonthlySummary(
    int year,
    int month,
  );

  /// Đồng bộ events từ server
  Future<Either<Failure, List<CalendarEvent>>> syncEvents();

  /// Lưu events vào cache local
  Future<Either<Failure, void>> cacheEvents(List<CalendarEvent> events);

  /// Lấy events từ cache local
  Future<Either<Failure, List<CalendarEvent>>> getCachedEvents();

  /// Xóa cache
  Future<Either<Failure, void>> clearCache();

  /// Kiểm tra xem có events trong ngày không
  Future<Either<Failure, bool>> hasEventsOnDate(DateTime date);

  /// Lấy số lượng events theo loại
  Future<Either<Failure, Map<EventType, int>>> getEventCountByType();

  /// Export events ra file
  Future<Either<Failure, String>> exportEvents({
    DateTime? startDate,
    DateTime? endDate,
    List<EventType>? types,
  });

  /// Import events từ file
  Future<Either<Failure, List<CalendarEvent>>> importEvents(String filePath);
}
