import 'package:flutter/material.dart';
import '../../../../core/extensions/responsive_extension.dart';
import '../../../../shared/theme/app_colors.dart';
import '../../domain/entities/calendar_event.dart';
import 'event_card_widget.dart';

/// Widget hiển thị danh sách events
class EventListWidget extends StatelessWidget {
  final List<CalendarEvent> events;
  final bool isScrollable;
  final int? maxItems;

  const EventListWidget({
    super.key,
    required this.events,
    this.isScrollable = false,
    this.maxItems,
  });

  @override
  Widget build(BuildContext context) {
    if (events.isEmpty) {
      return _buildEmptyState(context);
    }

    final displayEvents = maxItems != null
        ? events.take(maxItems!).toList()
        : events;

    if (isScrollable) {
      return _buildScrollableList(context, displayEvents);
    }

    return _buildStaticList(context, displayEvents);
  }

  Widget _buildEmptyState(BuildContext context) {
    return Container(
      padding: context.responsive.padding(all: 24),
      child: Column(
        children: [
          Icon(
            Icons.event_busy_outlined,
            size: context.rf(48),
            color: AppColors.textSecondary.withOpacity(0.5),
          ),
          SizedBox(height: context.rh(12)),
          Text(
            'No events found',
            style: Theme.of(
              context,
            ).textTheme.bodyMedium?.copyWith(color: AppColors.textSecondary),
          ),
        ],
      ),
    );
  }

  Widget _buildScrollableList(
    BuildContext context,
    List<CalendarEvent> displayEvents,
  ) {
    return SizedBox(
      height: context.rh(300), // Fixed height for scrollable list
      child: ListView.separated(
        itemCount: displayEvents.length,
        separatorBuilder: (context, index) => SizedBox(height: context.rh(8)),
        itemBuilder: (context, index) {
          return EventCardWidget(event: displayEvents[index]);
        },
      ),
    );
  }

  Widget _buildStaticList(
    BuildContext context,
    List<CalendarEvent> displayEvents,
  ) {
    return Column(
      children: displayEvents.asMap().entries.map((entry) {
        final index = entry.key;
        final event = entry.value;

        return Column(
          children: [
            EventCardWidget(event: event),
            if (index < displayEvents.length - 1)
              SizedBox(height: context.rh(8)),
          ],
        );
      }).toList(),
    );
  }
}

/// Widget hiển thị danh sách events theo nhóm ngày
class GroupedEventListWidget extends StatelessWidget {
  final Map<DateTime, List<CalendarEvent>> groupedEvents;
  final bool showDateHeaders;

  const GroupedEventListWidget({
    super.key,
    required this.groupedEvents,
    this.showDateHeaders = true,
  });

  @override
  Widget build(BuildContext context) {
    if (groupedEvents.isEmpty) {
      return EventListWidget(events: const []);
    }

    final sortedDates = groupedEvents.keys.toList()
      ..sort((a, b) => a.compareTo(b));

    return ListView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: sortedDates.length,
      itemBuilder: (context, index) {
        final date = sortedDates[index];
        final events = groupedEvents[date]!;

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (showDateHeaders) ...[
              _buildDateHeader(context, date),
              SizedBox(height: context.rh(8)),
            ],
            EventListWidget(events: events),
            if (index < sortedDates.length - 1)
              SizedBox(height: context.rh(16)),
          ],
        );
      },
    );
  }

  Widget _buildDateHeader(BuildContext context, DateTime date) {
    return Container(
      padding: context.responsive.padding(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: AppColors.primaryBlue.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Text(
        _getFriendlyDateString(context, date),
        style: Theme.of(context).textTheme.titleSmall?.copyWith(
          color: AppColors.primaryBlue,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  String _getFriendlyDateString(BuildContext context, DateTime date) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final tomorrow = DateTime(now.year, now.month, now.day + 1);
    final checkDate = DateTime(date.year, date.month, date.day);

    if (checkDate == today) {
      return 'Today, ${_getShortDateString(date)}';
    } else if (checkDate == tomorrow) {
      return 'Tomorrow, ${_getShortDateString(date)}';
    }

    return _getShortDateString(date);
  }

  String _getShortDateString(DateTime date) {
    const monthNames = [
      'Jan',
      'Feb',
      'Mar',
      'Apr',
      'May',
      'Jun',
      'Jul',
      'Aug',
      'Sep',
      'Oct',
      'Nov',
      'Dec',
    ];
    return '${date.day} ${monthNames[date.month - 1]}';
  }
}
