{"@@locale": "vi", "acceptTerms": "Tôi đồng ý với điều khoản và điều kiện", "authForgotPassword": "<PERSON><PERSON><PERSON><PERSON> mật k<PERSON>u", "authNext": "<PERSON><PERSON><PERSON><PERSON> theo", "authResetPassword": "Đặt lại mật khẩu", "authSubTitleForgotPassword": "Nhập email của bạn để đặt lại mật khẩu.", "authSubTitleResetPassword": "<PERSON><PERSON><PERSON><PERSON> mật khẩu mới của bạn.", "authSubTitleVerifyOtp": "<PERSON><PERSON><PERSON><PERSON> mã 4 chữ số đư<PERSON><PERSON> g<PERSON><PERSON> đến email c<PERSON><PERSON> bạn.", "authTitleResetPassword": "Đặt lại mật khẩu", "authVerifyOtp": "Xác minh OTP", "changePassword": "<PERSON><PERSON><PERSON> mật kh<PERSON>u", "contactInfo": "<PERSON>h<PERSON>ng tin liên hệ", "detectFaceAnalyzingSecurity": "<PERSON><PERSON> phân tích bảo mật...", "detectFaceAttendanceCompleted": "<PERSON><PERSON> hoàn thành điểm danh", "detectFaceCaptureImage": "<PERSON><PERSON><PERSON> Ảnh", "detectFaceCaptureToStart": "<PERSON><PERSON><PERSON>nh để bắt đầu", "detectFaceCheckIn": "<PERSON><PERSON><PERSON>", "detectFaceCheckInError": "<PERSON><PERSON><PERSON> công thất bại: {error}", "detectFaceCheckInSuccess": "<PERSON><PERSON><PERSON> công thành công lúc", "detectFaceCheckOut": "<PERSON><PERSON><PERSON> xu<PERSON>", "detectFaceCurrentLocation": "<PERSON><PERSON> trí hiện tại", "detectFaceFaceAnalysis": "<PERSON><PERSON> tích khu<PERSON>n mặt", "detectFaceFaceAnalysisDescription": "Sử dụng ML để xác định vị trí, các đặc điểm trên khuôn mặt và đảm bảo mắt mở.", "detectFaceFaceNotFound": "<PERSON><PERSON><PERSON><PERSON> tìm thấy khuôn mặt trong ảnh.", "detectFaceFacesDetected": "<PERSON><PERSON><PERSON> thấy {count} khu<PERSON>n mặt", "detectFaceGettingLocation": "<PERSON><PERSON> l<PERSON>y vị trí...", "detectFaceImageCapturedReady": "Ảnh đã đư<PERSON><PERSON> ch<PERSON>p. Sẵn sàng chấm công.", "detectFaceImageNotCaptured": "<PERSON><PERSON> hủy ch<PERSON><PERSON>.", "detectFaceLocationPermissionDenied": "<PERSON><PERSON><PERSON>n t<PERSON>y cập vị trí đã bị từ chối.", "detectFaceLocationPermissionPermanentlyDenied": "<PERSON><PERSON><PERSON><PERSON> vị trí bị từ chối vĩnh viễn.", "detectFaceLocationVerification": "<PERSON><PERSON><PERSON>h vị trí", "detectFaceLocationVerificationDescription": "<PERSON><PERSON><PERSON> bảo vị trí chấm công nằm trong khu vự<PERSON> hợp lệ của công ty.", "detectFaceOpeningCamera": "<PERSON><PERSON> mở camera...", "detectFaceProcessingCheckIn": "<PERSON><PERSON> x<PERSON> lý chấm công...", "detectFaceProcessingCheckOut": "<PERSON><PERSON> xử lý đăng xuất...", "detectFaceReadyForCheckIn": "Sẵn sàng để chấm công", "detectFaceReadyForCheckOut": "Sẵn sàng để đăng xuất", "detectFaceRetakeImage": "<PERSON><PERSON><PERSON>", "detectFaceSecureCheckInSystem": "<PERSON><PERSON> thống chấm công bảo mật", "detectFaceSecureCheckInTitle": "<PERSON><PERSON><PERSON>", "detectFaceSecurityInfoNotice": "<PERSON><PERSON> thống sử dụng nhiều lớp phân tích để đảm bảo tính minh bạch và công bằng.", "detectFaceSecurityInfoTitle": "<PERSON><PERSON><PERSON>ng Tin <PERSON>", "detectFaceSystemStatus": "TRẠNG THÁI HỆ THỐNG", "detectFaceUpdateLocationTooltip": "<PERSON><PERSON><PERSON> nh<PERSON>t vị trí", "detectFaceSecurityInfoTooltip": "<PERSON><PERSON><PERSON><PERSON> tin bảo mật", "detectFaceUnderstood": "<PERSON><PERSON> hiểu", "editProfile": "Chỉnh s<PERSON><PERSON> hồ sơ", "error": "Lỗi", "home": "Trang chủ", "homeErrorFunction": "Lỗi cấu hình chức năng", "homeFaceRecognition": "<PERSON><PERSON><PERSON><PERSON> di<PERSON>n khuôn mặt", "homeGoodAfternoon": "<PERSON><PERSON><PERSON> bu<PERSON>i chiều", "homeGoodEvening": "<PERSON><PERSON><PERSON> bu<PERSON>i tối", "homeGoodMorning": "<PERSON><PERSON><PERSON> bu<PERSON><PERSON> sáng", "homeLeave": "Nghỉ phép", "homeNotificationAndUpdate": "Thông báo & Cập nhật", "homeOvertime": "Tăng ca", "homeTodayAttendance": "<PERSON><PERSON><PERSON><PERSON> danh hôm nay", "homeWorkHours": "<PERSON><PERSON><PERSON> làm", "loginEmail": "Email", "loginForgotPassword": "<PERSON>uên mật khẩu?", "loginHintEmail": "Nhập email c<PERSON><PERSON> bạn", "loginHintPassword": "<PERSON><PERSON><PERSON><PERSON> mật kh<PERSON>u của bạn", "loginNoAccount": "<PERSON><PERSON><PERSON>ng có tài k<PERSON>n?", "loginPassword": "<PERSON><PERSON><PERSON>", "loginPasswordMinLength": "<PERSON><PERSON>t kh<PERSON>u ph<PERSON>i có ít nhất 6 ký tự", "loginPleaseEnterEmail": "<PERSON><PERSON> lòng nhập email", "loginPleaseEnterFullName": "<PERSON><PERSON> lòng nhập họ tên của bạn", "loginPleaseEnterPassword": "<PERSON><PERSON> lòng nhập mật kh<PERSON>u", "loginRememberMe": "<PERSON><PERSON> nhớ tôi", "loginSignIn": "<PERSON><PERSON><PERSON>", "loginSignUp": "<PERSON><PERSON><PERSON> ký", "loginSuccess": "<PERSON><PERSON><PERSON> nh<PERSON>p thành công", "loginSubtitle": "<PERSON><PERSON><PERSON> vui được gặp lại bạn. <PERSON><PERSON><PERSON> đăng nhập để tiếp tục.", "loginTitle": "Chào mừng quay lại!", "logout": "<PERSON><PERSON><PERSON> xu<PERSON>", "logOutSuccess": "<PERSON><PERSON><PERSON> xuất thành công", "moreAllFeatures": "<PERSON><PERSON><PERSON> c<PERSON> chức n<PERSON>ng", "moreAttendance": "<PERSON><PERSON><PERSON><PERSON>nh", "moreCRM": "Khách hàng & Kinh doanh (CRM)", "moreHRM": "<PERSON><PERSON><PERSON><PERSON> lý nhân sự (HRM)", "moreLeave": "Nghỉ phép", "moreOverTime": "<PERSON><PERSON><PERSON> thêm gi<PERSON>", "moreQR": "<PERSON><PERSON>t QR", "moreSetting": "Cài đặt", "moreSupport": "Hỗ trợ", "moreTeam": "Đ<PERSON><PERSON>", "moreTraining": "<PERSON><PERSON><PERSON> t<PERSON>o", "moreUtility": "Tiện ích & Cài đặt", "moreRecruitment": " <PERSON><PERSON><PERSON><PERSON> d<PERSON>", "moreCalendar": "<PERSON><PERSON><PERSON> l<PERSON> vi<PERSON>", "navigationBarCustomer": "<PERSON><PERSON><PERSON><PERSON>", "navigationBarHome": "Trang chủ", "navigationBarMore": "<PERSON><PERSON><PERSON><PERSON>", "navigationBarNotify": "<PERSON><PERSON><PERSON><PERSON> báo", "navigationBarWork": "<PERSON><PERSON><PERSON> vi<PERSON><PERSON>", "notificationAll": "<PERSON><PERSON><PERSON> c<PERSON>", "notificationAllRead": "<PERSON>ất cả đã đọc", "notificationCategory": "<PERSON><PERSON> lo<PERSON>", "notificationCustomers": "<PERSON><PERSON><PERSON><PERSON>", "notificationDetail": "<PERSON> tiết thông báo", "notificationEmpty": "Bạn không có thông báo quan trọng nào", "notificationEmptyFromCustomer": "No notifications from customers yet", "notificationImportant": "<PERSON><PERSON> Trọng ", "notificationLevel": "<PERSON><PERSON><PERSON>", "notificationNoNewUpdates": "Chưa có gì mới", "notificationPlaceholder": "<PERSON><PERSON><PERSON> cả thông báo của bạn sẽ hiện tại đây", "notificationReceivedTime": "<PERSON><PERSON><PERSON><PERSON> gian <PERSON>h<PERSON>n", "notificationTittle": "<PERSON><PERSON><PERSON><PERSON> báo", "notificationUnread": "<PERSON><PERSON><PERSON>", "notificationViewAll": "Bạn đã xem hết tất cả thông báo", "otpNotReceived": "OTP không <PERSON>n", "otpVerifySuccessfully": "<PERSON><PERSON><PERSON> minh OTP thành công", "passwordResetSuccessfully": "Đặt lại mật khẩu thành công!", "pleaseEnterConfirmPassword": "<PERSON><PERSON> lòng nhập mật khẩu xác nhận", "profile": "<PERSON><PERSON> sơ", "profileDepartment": "Phòng ban", "profileJobInfo": "Thông tin công việc", "profilePhone": "<PERSON><PERSON> đi<PERSON>n tho<PERSON>i", "profilePosition": "<PERSON><PERSON><PERSON> v<PERSON>", "professionalInformation": "<PERSON>h<PERSON>ng tin nghề nghiệp", "fullName": "Họ và tên", "registerConfirmPassword": "<PERSON><PERSON><PERSON>n mật kh<PERSON>u", "registerEmail": "Email", "registerFullName": "<PERSON><PERSON> tên", "registerPassword": "<PERSON><PERSON><PERSON>", "registerSubTitleSignUp": "<PERSON><PERSON><PERSON><PERSON> thông tin để tạo tài k<PERSON>.", "registerSuccess": "<PERSON><PERSON><PERSON> ký thành công", "registerTitleSignUp": "<PERSON><PERSON><PERSON> t<PERSON>", "registerHaveAccount": "Đã có tài k<PERSON>n?", "resendCode": "<PERSON><PERSON><PERSON> lại mã", "retry": "<PERSON><PERSON><PERSON> lại", "save": "<PERSON><PERSON><PERSON>", "setting": "Cài đặt", "settingAccount": "<PERSON><PERSON><PERSON>", "settingAddImage": "Thê<PERSON> Ảnh", "settingApp": "<PERSON><PERSON><PERSON> ch<PERSON> dụng", "settingAutoCheckOut": "<PERSON><PERSON><PERSON> xuất tự động", "settingBiometricLogin": "<PERSON><PERSON><PERSON> nhập bằng vân tay", "settingCancel": "<PERSON><PERSON><PERSON>", "settingCheckInAndSecurity": "<PERSON><PERSON><PERSON> công & b<PERSON><PERSON> mật", "settingCheckUpDate": "<PERSON><PERSON><PERSON> tra cập nh<PERSON>t", "settingChooseAnEmployeeFromList": "<PERSON><PERSON><PERSON> một nhân viên từ danh sách", "settingChooseEmployee": "<PERSON><PERSON><PERSON> nhân viên", "settingChooseImageALibrary": "<PERSON><PERSON><PERSON>nh từ thư viện", "settingConfirmLogOut": "Bạn có chắc chắn muốn đăng xuất?", "settingConfirmPassword": "<PERSON><PERSON><PERSON>n mật kh<PERSON>u", "settingContactSupport": "<PERSON><PERSON><PERSON> hệ hỗ trợ", "settingCurrentPassword": "<PERSON><PERSON><PERSON><PERSON> hiện tại", "settingEmailSupport": "Email hỗ trợ", "settingError": "Đã xảy ra lỗi vui lòng thử lại sau!", "settingFaceRecognition": "<PERSON><PERSON><PERSON><PERSON> di<PERSON>n khuôn mặt", "settingHelpCenter": "Trung tâm trợ giúp", "settingIntroduce": "<PERSON><PERSON><PERSON><PERSON> thi<PERSON>u", "settingLanguage": "<PERSON><PERSON><PERSON>", "settingLatestVersion": "<PERSON><PERSON><PERSON> bản mới nh<PERSON>t", "settingMessageErrorEmail": "<PERSON><PERSON><PERSON><PERSON> tìm thấy <PERSON>ng dụng email nào trên thiết bị.", "settingMessageErrorPhone": "<PERSON><PERSON><PERSON>ng thể mở ứng dụng gọi điện.", "settingMessageErrorSocial": "<PERSON><PERSON><PERSON><PERSON> thể mở liên kết này.", "settingMessageSupport": "Tin nhắn hỗ trợ", "settingNewPassword": "<PERSON><PERSON><PERSON> mới", "settingPhoneSupport": "<PERSON><PERSON> điện thoại hỗ trợ", "settingSubChangePass": "Chỉnh sửa mật kh<PERSON>u", "settingSubEnableAutoCheckOut": "<PERSON><PERSON>t đăng xuất tự động", "settingSubEnableFaceRecognition": "<PERSON><PERSON><PERSON> nhận diện khuôn mặt", "settingSubGetHelp": "<PERSON><PERSON>ậ<PERSON> gi<PERSON>p và hỗ trợ", "settingSubTitleProfile": "Chỉnh sửa thông tin cá nhân", "settingSubUseBiometricLogin": "Sử dụng đăng nhập bằng vân tay", "settingSupport": "Hỗ trợ", "settingTheme": "<PERSON><PERSON><PERSON>", "settingTitleProfile": "Thông tin cá nhân", "settingUploadImage": "Upload <PERSON>nh", "settingUploadImageSuccess": "Upload <PERSON><PERSON> thành công", "settingVersion": "<PERSON><PERSON><PERSON>", "success": "<PERSON><PERSON><PERSON><PERSON> công", "thisWeek": "<PERSON><PERSON><PERSON>", "today": "<PERSON><PERSON><PERSON> nay", "initializing": "<PERSON><PERSON> khởi tạo", "refreshingStatus": "<PERSON><PERSON> làm mới trạng thái...", "errorLoadingAttendanceStatus": "Lỗi tải trạng thái chấm công.", "allSessionsCompleted": "<PERSON><PERSON><PERSON> cả các phiên đã hoàn thành.", "noFurtherActionsAvailable": "<PERSON><PERSON><PERSON><PERSON> còn hành động nào khác.", "locationServicesDisabled": "<PERSON><PERSON><PERSON> vụ định vị đã bị tắt.", "detectFaceFaceFoundCount": "<PERSON><PERSON> phát hiện khuôn mặt ({count})", "errorDetectingFaces": "Lỗi khi phát hiện khuôn mặt. {e}", "notReadyForAction": "<PERSON><PERSON> thống chưa sẵn sàng để thực hiện hành động. {e}", "errorGettingLocation": "Lỗi lấy vị trí: {error}", "errorCapturingImage": "Lỗi chụp ảnh: {error}", "checkIn": "<PERSON><PERSON><PERSON> công", "imageCleared": "<PERSON><PERSON><PERSON> ảnh đã đư<PERSON>c x<PERSON>a.", "statusUpdated": "<PERSON>r<PERSON><PERSON> thái đã đư<PERSON><PERSON> cậ<PERSON> nh<PERSON>.", "lastCheckOut": "<PERSON><PERSON><PERSON> chấm công ra gần nhất", "lastCheckIn": "<PERSON><PERSON><PERSON> chấm công vào gần nhất", "totalCheckIns": "Tổng số lần chấm công vào", "totalCheckOuts": "Tổng số lần chấm công ra", "notCurrentlyWorking": "<PERSON><PERSON><PERSON> không làm việc", "working": "<PERSON><PERSON> làm vi<PERSON>c", "loading": "<PERSON><PERSON> tả<PERSON>...", "retryInitialization": "Thử khởi động lại", "noActionAvailable": "<PERSON><PERSON><PERSON><PERSON> có hành động nào", "startNewSession": "<PERSON><PERSON><PERSON> đ<PERSON>u phiên mới", "notification": "<PERSON><PERSON><PERSON><PERSON> báo", "errorLoadingData": "Lỗi tải dữ liệu", "detectFaceConfirmCheckOut": "<PERSON><PERSON> phát hiện khuôn mặt. <PERSON><PERSON><PERSON> nhận chấm công ra?", "detectFaceConfirmCheckIn": "<PERSON>ã phát hiện khuôn mặt. <PERSON><PERSON><PERSON> nhận chấm công vào?", "clearImage": "<PERSON><PERSON><PERSON>", "checkOut": "Check out", "unexpectedErrorPleaseRetry": "<PERSON><PERSON> xảy ra lỗi. <PERSON><PERSON> lòng thử lại.", "noFaceDetectedInImage": "<PERSON>h<PERSON><PERSON> phát hiện khuôn mặt trong ảnh.", "pleaseCaptureImage": "<PERSON><PERSON> lòng ch<PERSON><PERSON> h<PERSON>nh <PERSON>.", "pleaseWaitForLocation": "<PERSON>ui lòng đợi lấy dữ liệu vị trí...", "attendance": "<PERSON><PERSON><PERSON><PERSON>nh", "thisMonth": "<PERSON><PERSON><PERSON><PERSON>", "unexpectedError": "<PERSON><PERSON> xảy ra lỗi không mong muốn.", "todaysSummary": "<PERSON><PERSON><PERSON> kết hôm nay", "checkedIn": "Đã vào ca", "notCheckedIn": "Chưa vào ca", "totalHours": "Tổng giờ", "overtime": "Tăng ca", "weeklySummary": "<PERSON><PERSON><PERSON> kết tu<PERSON>n", "workDays": "<PERSON><PERSON><PERSON> l<PERSON> vi<PERSON>c", "lateArrivals": "<PERSON><PERSON> tr<PERSON>", "weeklyPerformance": "<PERSON><PERSON><PERSON> t<PERSON>", "monthlySummary": "<PERSON><PERSON><PERSON> kết tháng", "daysOff": "Ngày nghỉ", "fullMonthHistory": "<PERSON><PERSON><PERSON> sử cả tháng", "date": "<PERSON><PERSON><PERSON>", "inShort": "Vào", "outShort": "Ra", "hoursShort": "Giờ", "viewFullHistory": "<PERSON><PERSON> l<PERSON>ch sử đ<PERSON>y đủ", "selectDateToViewDetails": "<PERSON><PERSON><PERSON> ngày để xem chi tiết", "errorOccurred": "Đ<PERSON> xảy ra lỗi", "overtimeRequest": "<PERSON><PERSON><PERSON> c<PERSON>u làm thêm giờ", "overtimeSummary": "<PERSON><PERSON><PERSON> tắt làm thêm giờ", "overtimeDetails": "<PERSON> tiết làm thêm giờ", "newRequest": "<PERSON><PERSON><PERSON> c<PERSON>u mới", "myOvertime": "<PERSON><PERSON><PERSON> thêm gi<PERSON> của tôi", "pending": "<PERSON>ờ <PERSON>", "approved": "Đ<PERSON>", "rejected": "<PERSON><PERSON> từ chối", "startTime": "<PERSON><PERSON><PERSON> b<PERSON><PERSON> đầu", "endTime": "<PERSON><PERSON><PERSON> kết thúc", "reason": "Lý do", "overtimeType": "<PERSON><PERSON><PERSON> làm thêm giờ", "regularOvertime": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "weekendOvertime": "<PERSON><PERSON><PERSON><PERSON> tu<PERSON>n", "holidayOvertime": "<PERSON><PERSON><PERSON>", "submitRequest": "<PERSON><PERSON><PERSON> yêu c<PERSON>u", "submitting": "<PERSON><PERSON> g<PERSON>...", "select": "<PERSON><PERSON><PERSON>", "hour": "Giờ", "minute": "<PERSON><PERSON><PERSON>", "period": "<PERSON><PERSON><PERSON><PERSON>", "selected": "<PERSON><PERSON> ch<PERSON>n", "pleaseSelectAllFields": "<PERSON><PERSON> lòng chọn tất cả các trư<PERSON><PERSON> bắt buộc", "endTimeMustBeAfterStartTime": "<PERSON><PERSON><PERSON> kết thúc phải sau giờ bắt đầu", "cannotSelectPastDates": "<PERSON><PERSON><PERSON><PERSON> thể chọn ngày đã qua cho yêu cầu làm thêm giờ", "cannotSelectPastTime": "<PERSON><PERSON><PERSON><PERSON> thể chọn giờ đã qua cho làm thêm giờ hôm nay", "minimumOvertimeDuration": "Thời gian làm thêm giờ phải ít nhất 30 phút", "maximumOvertimeDuration": "Thời gian làm thêm giờ không đư<PERSON><PERSON> quá 12 tiếng", "reasonableWorkingHours": "<PERSON><PERSON><PERSON> làm thêm nên từ 6:00 sáng đến 11:00 tối", "filterByStatus": "<PERSON><PERSON><PERSON> theo trạng thái", "allStatus": "<PERSON><PERSON><PERSON> cả trạng thái", "noOvertimeRequests": "<PERSON><PERSON><PERSON><PERSON> có yêu cầu làm thêm giờ", "submitYourFirstOvertimeRequest": "<PERSON><PERSON><PERSON> yêu cầu làm thêm giờ đầu tiên của bạn bằng tab Yêu cầu mới", "loadMore": "<PERSON><PERSON><PERSON>ê<PERSON>", "rejectionReason": "<PERSON>ý do từ chối", "selectApprover": "<PERSON><PERSON><PERSON><PERSON>", "noApproversAvailable": "<PERSON><PERSON><PERSON><PERSON> có <PERSON>", "all": "<PERSON><PERSON><PERSON> c<PERSON>", "totalRequests": "Tổng số đơn", "approve": "<PERSON><PERSON><PERSON><PERSON>", "reject": "<PERSON><PERSON> chối", "rejectRequest": "<PERSON><PERSON> chối đơn", "rejectionReasonDescription": "<PERSON>ui lòng cung cấp lý do rõ ràng để từ chối đơn làm thêm giờ này. Điều này sẽ giúp nhân viên hiểu đư<PERSON><PERSON> quyết định.", "enterRejectionReason": "<PERSON><PERSON><PERSON><PERSON> lý do từ chối...", "rejectionReasonRequired": "Lý do từ chối là bắt buộc", "rejectionReasonTooShort": "Lý do từ chối phải có ít nhất 10 ký tự", "rejectionReasonTooLong": "Lý do từ chối không đ<PERSON><PERSON><PERSON> v<PERSON><PERSON><PERSON> quá 500 ký tự", "overtimeReasonRequired": "L<PERSON> do làm thêm giờ là bắt buộc", "overtimeReasonTooShort": "Lý do phải có ít nhất 10 ký tự", "overtimeReasonTooLong": "Lý do không đ<PERSON><PERSON><PERSON> v<PERSON><PERSON><PERSON> quá 500 ký tự", "approvedBy": "<PERSON><PERSON><PERSON><PERSON> bởi", "rejectedBy": "<PERSON><PERSON> từ chối bởi", "time": "<PERSON><PERSON><PERSON><PERSON> gian", "duration": "<PERSON><PERSON><PERSON><PERSON> gian", "cancel": "<PERSON><PERSON><PERSON>", "createRequest": "<PERSON><PERSON><PERSON>", "loadingOvertimeHistory": "<PERSON><PERSON> tải lịch sử làm thêm giờ...", "overtimeHistory": "<PERSON><PERSON><PERSON> sử làm thêm giờ", "viewAll": "<PERSON><PERSON> t<PERSON>t cả", "recentRequests": "<PERSON><PERSON><PERSON> c<PERSON>u gần đây", "calendar": "<PERSON><PERSON><PERSON>", "calendarPageTitle": "<PERSON><PERSON><PERSON>", "calendarTabMonth": "<PERSON><PERSON><PERSON><PERSON>", "calendarTabWeek": "<PERSON><PERSON><PERSON>", "calendarTabAgenda": "<PERSON><PERSON><PERSON> tr<PERSON>", "calendarHeaderEvents": "<PERSON><PERSON> kiện", "calendarHeaderMeetings": "<PERSON><PERSON><PERSON><PERSON>", "calendarHeaderHolidays": "<PERSON><PERSON><PERSON>", "calendarWeekViewTitle": "<PERSON><PERSON> theo t<PERSON>", "calendarWeekEventsTitle": "<PERSON><PERSON> kiện trong tuần", "calendarAddEventDialogTitle": "<PERSON><PERSON><PERSON><PERSON> s<PERSON> kiện", "calendarEventTitleHint": "<PERSON>i<PERSON><PERSON> đề sự kiện", "calendarTimeHint": "<PERSON><PERSON><PERSON><PERSON> gian", "calendarEventTypeHint": "Lo<PERSON>i sự kiện", "calendarAddEventButton": "<PERSON><PERSON><PERSON><PERSON> s<PERSON> kiện", "calendarEventsOnDate": "<PERSON><PERSON> kiện ngày {date}", "calendarDayAbbreviationsSun": "CN", "calendarDayAbbreviationsMon": "T2", "calendarDayAbbreviationsTue": "T3", "calendarDayAbbreviationsWed": "T4", "calendarDayAbbreviationsThu": "T5", "calendarDayAbbreviationsFri": "T6", "calendarDayAbbreviationsSat": "T7", "calendarFullMonthNamesJan": "Tháng 1", "calendarFullMonthNamesFeb": "Tháng 2", "calendarFullMonthNamesMar": "Tháng 3", "calendarFullMonthNamesApr": "Tháng 4", "calendarFullMonthNamesMay": "Tháng 5", "calendarFullMonthNamesJun": "Tháng 6", "calendarFullMonthNamesJul": "Tháng 7", "calendarFullMonthNamesAug": "Tháng 8", "calendarFullMonthNamesSep": "Tháng 9", "calendarFullMonthNamesOct": "Tháng 10", "calendarFullMonthNamesNov": "Tháng 11", "calendarFullMonthNamesDec": "Tháng 12", "calendarShortMonthNamesJan": "Th1", "calendarShortMonthNamesFeb": "Th2", "calendarShortMonthNamesMar": "Th3", "calendarShortMonthNamesApr": "Th4", "calendarShortMonthNamesMay": "Th5", "calendarShortMonthNamesJun": "Th6", "calendarShortMonthNamesJul": "Th7", "calendarShortMonthNamesAug": "Th8", "calendarShortMonthNamesSep": "Th9", "calendarShortMonthNamesOct": "Th10", "calendarShortMonthNamesNov": "Th11", "calendarShortMonthNamesDec": "Th12", "calendarSampleStatEventsCount": "12", "calendarSampleStatMeetingsCount": "8", "calendarSampleStatHolidaysCount": "3", "tomorrow": "<PERSON><PERSON><PERSON> mai", "noEventsForThisWeek": "<PERSON><PERSON><PERSON><PERSON> có sự kiện nào trong tuần này", "noUpcomingEvents": "<PERSON><PERSON><PERSON><PERSON> có sự kiện sắp tới", "addEvent": "<PERSON><PERSON><PERSON><PERSON> s<PERSON> kiện", "editEvent": "<PERSON><PERSON><PERSON> s<PERSON> kiện", "deleteEvent": "<PERSON><PERSON><PERSON> s<PERSON> kiện", "eventDetails": "<PERSON> tiết sự kiện", "eventTitle": "<PERSON>i<PERSON><PERSON> đề sự kiện", "eventDescription": "<PERSON><PERSON> t<PERSON> sự kiện", "eventDate": "<PERSON><PERSON><PERSON> kiện", "eventTime": "<PERSON><PERSON><PERSON><PERSON> gian sự kiện", "eventType": "Lo<PERSON>i sự kiện", "eventLocation": "<PERSON><PERSON><PERSON>", "allDay": "<PERSON><PERSON>", "recurring": "Lặp lại", "attendees": "<PERSON><PERSON><PERSON><PERSON> tham gia", "meeting": "<PERSON><PERSON><PERSON><PERSON>", "leave": "Nghỉ phép", "holiday": "<PERSON><PERSON><PERSON>", "training": "<PERSON><PERSON><PERSON> t<PERSON>o", "event": "<PERSON><PERSON> kiện", "searchEvents": "<PERSON><PERSON><PERSON> ki<PERSON>m sự kiện", "noEventsFound": "<PERSON><PERSON><PERSON><PERSON> tìm thấy sự kiện nào", "loadingEvents": "<PERSON><PERSON> tải sự kiện...", "addingEvent": "<PERSON><PERSON> thêm sự kiện...", "updatingEvent": "<PERSON><PERSON> cập nhật sự kiện...", "deletingEvent": "<PERSON><PERSON> x<PERSON>a sự kiện...", "eventAddedSuccessfully": "<PERSON><PERSON><PERSON><PERSON> sự kiện thành công", "eventUpdatedSuccessfully": "<PERSON><PERSON><PERSON> nh<PERSON>t sự kiện thành công", "eventDeletedSuccessfully": "<PERSON><PERSON><PERSON> sự kiện thành công", "failedToAddEvent": "<PERSON><PERSON><PERSON><PERSON> sự kiện thất bại", "failedToUpdateEvent": "<PERSON><PERSON><PERSON> nhật sự kiện thất bại", "failedToDeleteEvent": "<PERSON><PERSON><PERSON> sự kiện thất bại", "confirmDeleteEvent": "Bạn có chắc chắn muốn xóa sự kiện này?", "confirmDeleteEvents": "Bạn có chắc chắn muốn xóa {count} sự kiện?", "selectEventType": "<PERSON><PERSON><PERSON> lo<PERSON>i sự kiện", "selectDate": "<PERSON><PERSON><PERSON>", "selectTime": "<PERSON><PERSON><PERSON> thời gian", "edit": "<PERSON><PERSON><PERSON>", "delete": "Xóa", "refresh": "<PERSON><PERSON><PERSON>", "sync": "<PERSON><PERSON><PERSON> bộ", "export": "<PERSON><PERSON><PERSON>", "import": "<PERSON><PERSON><PERSON><PERSON>", "filter": "<PERSON><PERSON><PERSON>", "clear": "Xóa", "apply": "<PERSON><PERSON>", "close": "Đ<PERSON><PERSON>", "sessionDetails": "<PERSON> ti<PERSON>t p<PERSON>ên", "sessionNumber": "<PERSON><PERSON>n {number}", "multipleSessionsDetails": "<PERSON> tiết các lần chấm công ({count})", "sessionCompleted": "<PERSON><PERSON><PERSON> th<PERSON>", "sessionInProgress": "<PERSON><PERSON> th<PERSON> hi<PERSON>n", "location": "<PERSON><PERSON> trí", "unknownLocation": "<PERSON><PERSON> trí không xác đ<PERSON>nh", "statusOnTime": "<PERSON><PERSON><PERSON> gi<PERSON>", "statusLate": "<PERSON><PERSON> tr<PERSON>", "statusAbsent": "Vắng mặt", "statusOnLeave": "Nghỉ phép", "statusWeekend": "<PERSON><PERSON><PERSON><PERSON> tu<PERSON>n", "statusNoRecord": "<PERSON><PERSON><PERSON><PERSON> có dữ liệu", "sunday": "<PERSON>ủ <PERSON>h<PERSON>", "monday": "<PERSON><PERSON><PERSON> hai", "tuesday": "<PERSON><PERSON><PERSON> ba", "wednesday": "<PERSON><PERSON><PERSON> tư", "thursday": "<PERSON><PERSON><PERSON> năm", "friday": "<PERSON><PERSON><PERSON> s<PERSON>u", "saturday": "<PERSON><PERSON><PERSON> b<PERSON><PERSON>", "january": "Tháng 1", "february": "Tháng 2", "march": "Tháng 3", "april": "Tháng 4", "may": "Tháng 5", "june": "Tháng 6", "july": "Tháng 7", "august": "Tháng 8", "september": "Tháng 9", "october": "Tháng 10", "november": "Tháng 11", "december": "Tháng 12", "createOvertimeRequest": "<PERSON><PERSON><PERSON> yêu cầu làm thêm giờ", "overtimeDate": "<PERSON><PERSON><PERSON> làm thêm", "approver": "<PERSON><PERSON><PERSON><PERSON>", "pleaseSelectDate": "<PERSON><PERSON> lòng chọn ng<PERSON>y", "pleaseSelectStartTime": "<PERSON><PERSON> lòng chọn giờ bắt đầu", "pleaseSelectEndTime": "<PERSON><PERSON> lòng chọn gi<PERSON> kết thúc", "pleaseEnterReason": "<PERSON><PERSON> lòng nh<PERSON>p lý do", "pleaseSelectApprover": "<PERSON><PERSON> lòng chọn ng<PERSON><PERSON>", "overtimeRequestSubmitted": "<PERSON><PERSON><PERSON> yêu cầu làm thêm giờ thành công", "failedToSubmitRequest": "<PERSON><PERSON><PERSON> yêu cầu thất bại", "loadingOvertimeRequests": "<PERSON><PERSON> tải yêu cầu làm thêm giờ...", "overtimeRequestDetails": "<PERSON> tiết yêu cầu làm thêm giờ", "requestDate": "<PERSON><PERSON><PERSON> c<PERSON>u", "hours": "giờ", "minutes": "<PERSON><PERSON><PERSON><PERSON>", "pendingApproval": "<PERSON><PERSON> phê <PERSON>", "submittedOn": "Đã gửi vào", "fillDetailsToSubmit": "<PERSON><PERSON><PERSON>n thông tin chi tiết bên dưới để gửi yêu cầu làm thêm giờ", "@_LEAVE_FEATURE": {}, "leaveHistory": "<PERSON><PERSON><PERSON> sử nghỉ phép", "leaveRequest": "<PERSON><PERSON><PERSON> cầu nghỉ phép", "leaveRequestDetails": "<PERSON> tiết yêu cầu nghỉ phép", "myLeave": "Nghỉ phép của tôi", "submitLeaveRequest": "<PERSON><PERSON><PERSON> yêu cầu nghỉ phép", "fillLeaveDetailsToSubmit": "<PERSON><PERSON><PERSON>n thông tin chi tiết bên dưới để gửi yêu cầu nghỉ phép của bạn", "remainingLeaveDays": "S<PERSON> ngày nghỉ phép còn lại", "outOfDays": "trên tổng số {totalDays} ngày", "@outOfDays": {"placeholders": {"totalDays": {"type": "int"}}}, "used": "Đã sử dụng", "cancelled": "<PERSON><PERSON> hủy", "days": "ng<PERSON>y", "day": "ng<PERSON>y", "leavePolicy": "<PERSON><PERSON><PERSON> sách nghỉ phép", "recentLeaveRequests": "<PERSON><PERSON><PERSON> cầu nghỉ phép gần đây", "viewAllLeaveHistory": "<PERSON><PERSON> tất cả lịch sử", "noLeaveRequestsYet": "<PERSON><PERSON><PERSON> có yêu cầu nghỉ phép nào", "noFilteredRequests": "<PERSON><PERSON><PERSON><PERSON> có yêu cầu {status}", "@noFilteredRequests": {"placeholders": {"status": {"type": "String"}}}, "yourLeaveRequestsWillAppearHere": "<PERSON><PERSON><PERSON> yêu cầu nghỉ phép của bạn sẽ hiển thị ở đây", "showAll": "<PERSON><PERSON><PERSON> thị tất cả", "editRequest": "Chỉnh sửa yêu cầu", "cancelRequest": "<PERSON><PERSON><PERSON> y<PERSON>u c<PERSON>u", "requestDetails": "<PERSON> tiết yêu cầu", "leaveType": "Loại nghỉ phép", "startDate": "<PERSON><PERSON><PERSON> b<PERSON><PERSON> đ<PERSON>u", "endDate": "<PERSON><PERSON><PERSON> k<PERSON> th<PERSON>c", "status": "<PERSON><PERSON><PERSON><PERSON> thái", "approvedOn": "Đã duyệt vào", "rejectedOn": "Bị từ chối vào", "cancelRequestConfirm": "Bạn có chắc chắn muốn hủy yêu cầu nghỉ phép này không?", "no": "K<PERSON>ô<PERSON>", "yes": "<PERSON><PERSON>", "requestCancelled": "<PERSON><PERSON><PERSON> cầu đã đ<PERSON><PERSON><PERSON> hủy", "editFunctionalityComingSoon": "<PERSON><PERSON><PERSON> năng chỉnh sửa sẽ sớm có", "annualLeave": "Nghỉ phép năm", "sickLeave": "Nghỉ ốm", "personalLeave": "Nghỉ cá nhân", "maternityLeave": "Nghỉ thai sản", "unpaidLeave": "Nghỉ không lương", "annualLeaveDescription": "Nghỉ dưỡng sức, kỳ nghỉ hoặc thời gian cá nhân", "sickLeaveDescription": "<PERSON><PERSON><PERSON><PERSON> bệnh hoặc ốm đau", "personalLeaveDescription": "<PERSON><PERSON><PERSON><PERSON> cá nhân hoặc khẩn cấp", "maternityLeaveDescription": "Nghỉ thai sản hoặc chăm sóc con", "unpaidLeaveDescription": "Nghỉ dài hạn không lương", "dateRange": "<PERSON><PERSON><PERSON><PERSON> thời gian", "durationInfo": "Th<PERSON>i gian: {duration} {durationUnit}", "@durationInfo": {"placeholders": {"duration": {"type": "int"}, "durationUnit": {"type": "String"}}}, "reasonForLeave": "Lý do nghỉ phép", "leaveReasonHint": "<PERSON>ui lòng cung cấp lý do chi tiết cho yêu cầu nghỉ phép của bạn...\n\nVí dụ:\n• Nghỉ dưỡng sức gia đình\n• <PERSON><PERSON><PERSON><PERSON> bệnh\n• Việc cá nhân khẩn cấp", "pleaseProvideReason": "<PERSON><PERSON> lòng cung cấp lý do nghỉ phép", "pleaseFillAllFields": "<PERSON><PERSON> lòng điền tất cả các trư<PERSON><PERSON> bắt buộc", "pleaseSelectStartDate": "<PERSON><PERSON> lòng chọn ngày b<PERSON>t đầu", "pleaseSelectEndDate": "<PERSON><PERSON> lòng chọn ngày kết thúc", "leaveRequestSubmittedSuccess": "<PERSON>ã gửi yêu cầu nghỉ phép thành công!", "failedToLoadLeaveData": "<PERSON><PERSON><PERSON><PERSON> thể tải dữ liệu nghỉ phép: {error}", "@failedToLoadLeaveData": {"placeholders": {"error": {"type": "String"}}}, "failedToSubmitLeaveRequest": "<PERSON><PERSON><PERSON><PERSON> thể gửi yêu cầu nghỉ phép: {error}", "@failedToSubmitLeaveRequest": {"placeholders": {"error": {"type": "String"}}}, "@_ADMIN_USER_MANAGEMENT": {}, "adminUserManagement": "<PERSON><PERSON><PERSON><PERSON> lý ng<PERSON><PERSON> dùng", "adminUsers": "<PERSON><PERSON><PERSON><PERSON> dùng", "adminStatistics": "<PERSON><PERSON><PERSON><PERSON> kê", "adminSettings": "Cài đặt", "addNewUser": "<PERSON><PERSON><PERSON><PERSON> ng<PERSON>ời dùng mới", "createNewUserAccount": "<PERSON><PERSON><PERSON> tài kho<PERSON>n người dùng mới", "createUser": "<PERSON><PERSON><PERSON> dùng", "editUser": "Chỉnh sửa người dùng", "editUserDetails": "Chỉnh sửa thông tin người dùng", "userDetails": "Thông tin người dùng", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "Họ", "email": "Email", "password": "<PERSON><PERSON><PERSON>", "confirmPassword": "<PERSON><PERSON><PERSON>n mật kh<PERSON>u", "phoneNumber": "<PERSON><PERSON> đi<PERSON>n tho<PERSON>i", "role": "<PERSON>ai trò", "department": "Phòng ban", "position": "<PERSON><PERSON><PERSON> v<PERSON>", "organization": "<PERSON><PERSON> chức", "active": "<PERSON><PERSON><PERSON> đ<PERSON>", "inactive": "<PERSON><PERSON><PERSON><PERSON> hoạt động", "disabled": "<PERSON><PERSON> hi<PERSON> h<PERSON>a", "deleted": "Đã xóa", "enabled": "Đ<PERSON> kích ho<PERSON>", "@_ADMIN_ROLES": {}, "roleAdmin": "<PERSON><PERSON><PERSON><PERSON> trị viên", "roleManager": "<PERSON><PERSON><PERSON><PERSON> lý", "roleHR": "<PERSON><PERSON><PERSON> sự", "roleEmployee": "Nhân viên", "roleUser": "<PERSON><PERSON><PERSON><PERSON> dùng", "@_ADMIN_DEPARTMENTS": {}, "departmentIT": "Công nghệ thông tin", "departmentHR": "<PERSON><PERSON><PERSON> sự", "departmentFinance": "<PERSON><PERSON><PERSON>", "departmentMarketing": "Marketing", "departmentOperations": "<PERSON><PERSON><PERSON> h<PERSON>nh", "departmentSales": "<PERSON><PERSON>", "departmentSupport": "Hỗ trợ", "@_ADMIN_ACTIONS": {}, "searchUsers": "<PERSON><PERSON><PERSON> kiếm người dùng...", "filterAndSort": "Lọc & Sắp xếp", "bulkActions": "<PERSON><PERSON> t<PERSON> hàng lo<PERSON>", "selectAll": "<PERSON><PERSON><PERSON> tất cả", "deselectAll": "Bỏ chọn tất cả", "clearSelection": "<PERSON>óa l<PERSON>a ch<PERSON>n", "usersSelected": "<PERSON><PERSON> ch<PERSON>n {count} ng<PERSON><PERSON><PERSON> dùng", "@usersSelected": {"placeholders": {"count": {"type": "int"}}}, "allRoles": "<PERSON><PERSON><PERSON> cả vai trò", "allDepartments": "<PERSON><PERSON><PERSON> cả phòng ban", "sortBy": "<PERSON><PERSON><PERSON> xếp theo", "sortByName": "<PERSON><PERSON><PERSON>", "sortByEmail": "Email", "sortByRole": "<PERSON>ai trò", "sortByDepartment": "Phòng ban", "sortByCreatedDate": "<PERSON><PERSON><PERSON>", "sortByLastLogin": "<PERSON><PERSON><PERSON> đ<PERSON><PERSON>", "sortAscending": "<PERSON><PERSON><PERSON>", "sortDescending": "<PERSON><PERSON><PERSON><PERSON>", "@_ADMIN_USER_ACTIONS": {}, "viewUser": "<PERSON><PERSON> dùng", "editUserAction": "Chỉnh sửa người dùng", "deleteUser": "<PERSON><PERSON><PERSON> ng<PERSON><PERSON> dùng", "restoreUser": "<PERSON><PERSON><PERSON><PERSON> phục người dùng", "disableUser": "<PERSON><PERSON> hiệu hóa người dùng", "enableUser": "<PERSON><PERSON><PERSON> ho<PERSON> ng<PERSON><PERSON> dùng", "resetPassword": "Đặt lại mật khẩu", "toggleUserStatus": "Chuyển đổi trạng thái", "confirmDeleteUser": "Bạn có chắc chắn muốn xóa người dùng này?", "confirmRestoreUser": "Bạn có chắc chắn muốn khôi phục người dùng này?", "confirmDisableUser": "Bạn có chắc chắn muốn vô hiệu hóa người dùng này?", "confirmEnableUser": "Bạn có chắc chắn muốn kích hoạt người dùng này?", "confirmResetPassword": "Bạn có chắc chắn muốn đặt lại mật khẩu của người dùng này?", "@_ADMIN_BULK_ACTIONS": {}, "bulkDelete": "<PERSON><PERSON><PERSON> h<PERSON>", "bulkRestore": "<PERSON><PERSON><PERSON><PERSON> ph<PERSON><PERSON> hàng <PERSON>", "bulkDisable": "<PERSON><PERSON> hi<PERSON> hóa hàng lo<PERSON>", "bulkEnable": "<PERSON><PERSON><PERSON> ho<PERSON> hàng lo<PERSON>t", "confirmBulkDelete": "Bạn có chắc chắn muốn xóa {count} người dùng?", "@confirmBulkDelete": {"placeholders": {"count": {"type": "int"}}}, "confirmBulkRestore": "Bạn có chắc chắn muốn khôi phục {count} người dùng?", "@confirmBulkRestore": {"placeholders": {"count": {"type": "int"}}}, "@_ADMIN_STATISTICS": {}, "userOverview": "T<PERSON>ng quan người dùng", "totalUsers": "Tổng số người dùng", "activeUsers": "Ngư<PERSON><PERSON> dùng hoạt động", "disabledUsers": "<PERSON><PERSON><PERSON><PERSON> dùng bị vô hiệu hóa", "deletedUsers": "Người dùng đã xóa", "roleDistribution": "<PERSON><PERSON> bố vai trò", "analytics": "<PERSON><PERSON> tích", "userGrowth": "Tăng trưởng người dùng", "userActivity": "<PERSON><PERSON><PERSON> động người dùng", "departmentBreakdown": "<PERSON><PERSON> tích theo phòng ban", "@_ADMIN_MESSAGES": {}, "userCreatedSuccessfully": "Tạo người dùng thành công!", "userUpdatedSuccessfully": "<PERSON><PERSON><PERSON> nhật người dùng thành công!", "userDeletedSuccessfully": "<PERSON><PERSON>a người dùng thành công!", "userRestoredSuccessfully": "<PERSON>h<PERSON>i phục người dùng thành công!", "userDisabledSuccessfully": "Vô hiệu hóa người dùng thành công!", "userEnabledSuccessfully": "<PERSON><PERSON><PERSON> ho<PERSON>t người dùng thành công!", "failedToCreateUser": "<PERSON><PERSON><PERSON><PERSON> thể tạo người dùng: {error}", "@failedToCreateUser": {"placeholders": {"error": {"type": "String"}}}, "failedToUpdateUser": "<PERSON><PERSON><PERSON><PERSON> thể cập nhật người dùng: {error}", "@failedToUpdateUser": {"placeholders": {"error": {"type": "String"}}}, "failedToDeleteUser": "<PERSON><PERSON><PERSON><PERSON> thể xóa người dùng: {error}", "@failedToDeleteUser": {"placeholders": {"error": {"type": "String"}}}, "failedToLoadUsers": "<PERSON><PERSON><PERSON><PERSON> thể tải danh sách người dùng: {error}", "@failedToLoadUsers": {"placeholders": {"error": {"type": "String"}}}, "noUsersFound": "<PERSON><PERSON><PERSON><PERSON> tìm thấy người dùng nào", "loadingUsers": "<PERSON><PERSON> tải danh sách người dùng...", "refreshUsers": "<PERSON><PERSON><PERSON> mới danh s<PERSON>ch", "addUser": "<PERSON><PERSON><PERSON><PERSON> ng<PERSON> dùng", "@_ADMIN_VALIDATION": {}, "firstNameRequired": "<PERSON><PERSON><PERSON> là b<PERSON><PERSON> bu<PERSON>c", "lastNameRequired": "<PERSON><PERSON> là bắt buộc", "emailRequired": "<PERSON><PERSON> l<PERSON> b<PERSON> bu<PERSON>c", "emailInvalid": "<PERSON><PERSON> lòng nhập địa chỉ email hợp lệ", "passwordRequired": "<PERSON><PERSON><PERSON> kh<PERSON>u là bắt buộc", "passwordTooShort": "<PERSON><PERSON>t khẩu phải có ít nhất 8 ký tự", "passwordTooWeak": "<PERSON><PERSON><PERSON> kh<PERSON>u phải chứa chữ hoa, chữ thườ<PERSON>, số và ký tự đặc biệt", "phoneInvalid": "<PERSON><PERSON> lòng nhập số điện tho<PERSON>i hợp lệ", "roleRequired": "<PERSON>ai trò là b<PERSON> buộc", "departmentRequired": "Phòng ban là bắt buộc", "@_ADMIN_FORM_LABELS": {}, "requiredField": "<PERSON><PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON> bu<PERSON>c", "optionalField": "<PERSON><PERSON><PERSON><PERSON><PERSON> tù<PERSON>n", "enterFirstName": "<PERSON><PERSON><PERSON><PERSON> tên", "enterLastName": "<PERSON><PERSON><PERSON><PERSON>", "enterEmail": "<PERSON><PERSON><PERSON><PERSON> địa chỉ email", "enterPassword": "<PERSON><PERSON><PERSON><PERSON> mật kh<PERSON>u", "enterPhoneNumber": "<PERSON><PERSON><PERSON><PERSON> số điện thoại", "selectRole": "<PERSON><PERSON><PERSON> vai trò", "selectDepartment": "<PERSON><PERSON><PERSON> phòng ban", "enterPosition": "<PERSON><PERSON><PERSON><PERSON> v<PERSON>", "enterOrganization": "<PERSON><PERSON><PERSON><PERSON> tổ chức", "@_ADMIN_SECTIONS": {}, "personalInformation": "Thông tin cá nhân", "workInformation": "Thông tin công việc", "roleAndStatus": "Vai trò & Trạng thái", "saveChanges": "<PERSON><PERSON><PERSON> thay đổi", "@_ROLE_DEPARTMENT_MANAGEMENT": {}, "roleManagement": "<PERSON><PERSON><PERSON><PERSON> lý vai trò", "departmentManagement": "<PERSON><PERSON><PERSON><PERSON> lý phòng ban", "createNewRole": "Tạo vai trò mới", "editRole": "Chỉnh sửa vai trò", "createNewDepartment": "<PERSON><PERSON>o phòng ban mới", "editDepartment": "Chỉnh sửa phòng ban", "roleName": "<PERSON>ên vai trò", "departmentName": "<PERSON>ên phòng ban", "departmentCode": "Mã phòng ban", "departmentDescription": "<PERSON><PERSON> tả phòng ban", "parentDepartment": "Phòng ban cha", "noParentTopLevel": "<PERSON><PERSON><PERSON><PERSON> có phòng ban cha (<PERSON>ấ<PERSON> cao nhất)", "selectParentDepartment": "<PERSON><PERSON>n phòng ban cha (tù<PERSON> chọn)", "noDepartment": "<PERSON>h<PERSON>ng có phòng ban", "basicInformation": "<PERSON><PERSON><PERSON><PERSON> tin cơ bản", "hierarchy": "<PERSON><PERSON><PERSON> tr<PERSON>c tổ chức", "departmentStatus": "Tr<PERSON>ng thái phòng ban", "enable": "<PERSON><PERSON><PERSON>", "disable": "<PERSON><PERSON> hi<PERSON> h<PERSON>a", "restore": "<PERSON><PERSON><PERSON><PERSON>h<PERSON>c", "deleteRole": "Xóa vai trò", "deleteDepartment": "<PERSON>óa phòng ban", "deleteRoleConfirm": "Bạn có chắc chắn muốn xóa vai trò \"{roleName}\"?", "deleteDepartmentConfirm": "Bạn có chắc chắn muốn xóa phòng ban \"{departmentName}\"?", "roleCreatedSuccessfully": "Tạo vai trò thành công", "roleUpdatedSuccessfully": "<PERSON><PERSON><PERSON> nhật vai trò thành công", "roleDeletedSuccessfully": "<PERSON>ó<PERSON> vai trò thành công", "departmentCreatedSuccessfully": "<PERSON><PERSON>o phòng ban thành công", "departmentUpdatedSuccessfully": "<PERSON><PERSON><PERSON> nhật phòng ban thành công", "departmentDeletedSuccessfully": "Xóa phòng ban thành công", "departmentRestoredSuccessfully": "<PERSON>h<PERSON><PERSON> phục phòng ban thành công", "departmentStatusUpdatedSuccessfully": "<PERSON><PERSON><PERSON> nhật trạng thái phòng ban thành công", "noRolesFound": "<PERSON><PERSON><PERSON><PERSON> tìm thấy vai trò nào", "noDepartmentsFound": "<PERSON><PERSON><PERSON>ng tìm thấy phòng ban nào", "createFirstRole": "Tạo vai trò đầu tiên để bắt đầu", "createFirstDepartment": "T<PERSON><PERSON> phòng ban đầu tiên để bắt đầu", "addRole": "Thê<PERSON> vai trò", "addDepartment": "<PERSON>h<PERSON><PERSON> phòng ban", "updateRole": "<PERSON><PERSON><PERSON> nhật vai trò", "updateDepartment": "<PERSON><PERSON><PERSON> nhật phòng ban", "createRole": "Tạo vai trò", "createDepartment": "<PERSON><PERSON><PERSON> phòng ban", "searchRoles": "T<PERSON>m kiếm vai trò...", "searchDepartments": "<PERSON><PERSON>m kiếm phòng ban...", "includeDeleted": "<PERSON><PERSON> gồm đã xóa", "roleInformation": "Thông tin vai trò", "roleDetails": "<PERSON> tiết vai trò", "created": "Đã tạo", "lastUpdated": "<PERSON><PERSON><PERSON> nh<PERSON><PERSON> lần <PERSON>i", "roleId": "ID vai trò", "departmentId": "ID phòng ban", "enterRoleName": "<PERSON><PERSON><PERSON><PERSON> tên vai trò (ví dụ: <PERSON><PERSON><PERSON><PERSON> lý, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> trình viên)", "enterDepartmentName": "<PERSON><PERSON><PERSON><PERSON> tên phòng ban (ví dụ: <PERSON><PERSON><PERSON> sự, <PERSON><PERSON><PERSON> nghệ thông tin)", "enterDepartmentCode": "<PERSON><PERSON><PERSON><PERSON> mã phòng ban (ví dụ: HR, IT, FIN)", "enterDepartmentDescription": "<PERSON><PERSON><PERSON><PERSON> mô tả phòng ban (tù<PERSON> chọn)", "roleNameRequired": "<PERSON>ên vai trò là bắt buộc", "departmentNameRequired": "<PERSON>ên phòng ban là bắt buộc", "roleNameMinLength": "Tên vai trò phải có ít nhất 2 ký tự", "departmentNameMinLength": "Tên phòng ban phải có ít nhất 2 ký tự", "roleNameMaxLength": "Tên vai trò phải ít hơn 50 ký tự", "departmentNameMaxLength": "Tên phòng ban phải ít hơn 100 ký tự", "departmentCodeMinLength": "Mã phòng ban phải có ít nhất 2 ký tự", "departmentCodeMaxLength": "Mã phòng ban phải ít hơn 10 ký tự", "roleNameInvalidCharacters": "<PERSON>ên vai trò chứa ký tự không hợp lệ", "departmentCodeInvalidCharacters": "Mã phòng ban chứa ký tự không hợp lệ", "roleNameAlreadyExists": "Tên vai trò đã tồn tại", "departmentNameAlreadyExists": "Tên phòng ban đã tồn tại", "departmentCodeAlreadyExists": "Mã phòng ban đã tồn tại", "changingRoleNameWarning": "Thay đổi tên vai trò có thể ảnh hưởng đến người dùng được gán vai trò này.", "roleAndDepartmentInfo": "Tên vai trò và mã phòng ban phải là duy nhất. Chúng sẽ được sử dụng trong toàn bộ hệ thống để kiểm soát quyền truy cập.", "departmentHierarchyInfo": "Tên và mã phòng ban phải là duy nhất. Bạn có thể tạo cấu trúc phân cấp bằng cách chọn phòng ban cha."}