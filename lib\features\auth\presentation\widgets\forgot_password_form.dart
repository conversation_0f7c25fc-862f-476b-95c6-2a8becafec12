import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:golderhr/core/extensions/l10n_extension.dart';
import 'package:golderhr/core/routes/app_routes.dart';
import 'package:golderhr/shared/widgets/show_custom_snackBar.dart';

import '../../../../core/responsive/responsive.dart';
import '../../../../l10n/app_localizations.dart';
import '../../../../shared/theme/app_colors.dart';
import '../../../../shared/utils/validators.dart';
import '../../../../shared/widgets/button_custom.dart';
import '../../../../shared/widgets/text_field_custom.dart';
import '../cubit/auth_cubit.dart';
import '../cubit/auth_state.dart';
import 'field_label.dart';

class ForgotPasswordForm extends StatefulWidget {
  const ForgotPasswordForm({super.key});

  @override
  State<ForgotPasswordForm> createState() => _ForgotPasswordFormState();
}

class _ForgotPasswordFormState extends State<ForgotPasswordForm> {
  final formKey = GlobalKey<FormState>();
  final emailController = TextEditingController();
  final emailFocusNode = FocusNode();

  @override
  void dispose() {
    emailController.dispose();
    emailFocusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final responsive = Responsive.of(context);
    final l10n = AppLocalizations.of(context)!;

    return Form(
      key: formKey,
      autovalidateMode: AutovalidateMode.onUserInteraction,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          FieldLabel(label: l10n.loginEmail),
          SizedBox(height: responsive.heightPercentage(0.8)),
          Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(10),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withAlpha((0.04 * 255).round()),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: TextFieldCustom(
              hintText: l10n.loginHintEmail,
              controller: emailController,
              prefixIcon: Icons.email_outlined,
              obscureText: false,
              validator: (value) => Validators.validateEmail(value, context),
              focusNode: emailFocusNode,
            ),
          ),

          SizedBox(height: responsive.heightPercentage(3.5)),
          _buildSubmitButton(responsive, l10n),
          SizedBox(height: responsive.heightPercentage(1.5)),
          BlocBuilder<AuthCubit, AuthState>(
            builder: (context, state) {
              if (state is AuthError) {
                showTopSnackBar(
                  context,
                  title: context.l10n.error,
                  message: state.message,
                  isError: true,
                );
                return const SizedBox.shrink();
              }
              return const SizedBox.shrink();
            },
          ),
        ],
      ),
    );
  }

  Widget _buildSubmitButton(Responsive responsive, AppLocalizations l10n) {
    return BlocBuilder<AuthCubit, AuthState>(
      builder: (context, state) {
        final isLoading = state is AuthLoading;
        return Container(
          width: double.infinity,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: AppColors.primary.withAlpha((0.3 * 255).round()),
                blurRadius: 12,
                offset: const Offset(0, 6),
              ),
            ],
          ),
          child: ButtonCustom(
            text: l10n.authNext,
            isLoading: isLoading,
            onPressed: isLoading
                ? null
                : () async {
                    if (formKey.currentState!.validate()) {
                      FocusScope.of(context).unfocus();
                      await context.read<AuthCubit>().forgotPassword(
                        emailController.text.trim(),
                      );
                      if (context.read<AuthCubit>().state
                              is ForgotPasswordOtpSent &&
                          mounted) {
                        context.push(AppRoutes.verifyOtp);
                        emailController.clear();
                      }
                    }
                  },
          ),
        );
      },
    );
  }
}
