{"@@locale": "en", "acceptTerms": "I accept the terms and conditions", "authForgotPassword": "Forgot Password", "authNext": "Next", "authResetPassword": "Reset Password", "authSubTitleForgotPassword": "Enter your email to reset your password.", "authSubTitleResetPassword": "Enter your new password.", "authSubTitleVerifyOtp": "Enter the 4-digit code sent to your email.", "authTitleResetPassword": "Reset Password", "authVerifyOtp": "Verify OTP", "changePassword": "Change Password", "contactInfo": "Contact Information", "detectFaceAnalyzingSecurity": "Analyzing security...", "detectFaceAttendanceCompleted": "Attendance completed", "detectFaceCaptureImage": "Capture Image", "detectFaceCaptureToStart": "Capture photo to start", "detectFaceCheckIn": "Check-In", "detectFaceCheckInError": "Check-in failed: {error}", "detectFaceCheckInSuccess": "Check-in successful", "detectFaceCheckOut": "Check-Out", "detectFaceCheckOutError": "Face CheckOut Error {error}", "detectFaceCheckOutSuccess": "Check-out successful", "detectFaceCurrentLocation": "Current location", "detectFaceFaceAnalysis": "Face analysis", "detectFaceFaceAnalysisDescription": "Use ML to detect facial features and ensure eyes are open.", "detectFaceFaceNotFound": "No face found in the image.", "detectFaceFacesDetected": "Detected {count} face(s)", "detectFaceGettingLocation": "Getting location...", "detectFaceImageCapturedReady": "Image captured. Ready to check in.", "detectFaceImageNotCaptured": "Image capture canceled.", "detectFaceLocationNotAvailable": "Face Location Not Available", "detectFaceLocationPermissionDenied": "Location access denied.", "detectFaceLocationPermissionPermanentlyDenied": "Location access permanently denied.", "detectFaceLocationVerification": "Location verification", "detectFaceLocationVerificationDescription": "Ensure check-in location is within the company area.", "detectFaceOpeningCamera": "Opening camera...", "detectFaceProcessingCheckIn": "Processing check-in...", "detectFaceProcessingCheckOut": "Processing check-out...", "detectFaceReadyForCheckIn": "Ready for check-in", "detectFaceReadyForCheckOut": "Ready for check-out", "detectFaceRetakeImage": "Retake", "detectFaceSecureCheckInSystem": "Secure Check-In System", "detectFaceSecureCheckInTitle": "Secure Check-In", "detectFaceSecurityInfoNotice": "The system uses multiple layers of analysis to ensure transparency and fairness", "detectFaceSecurityInfoTitle": "Security Information", "detectFaceSecurityInfoTooltip": "Security information", "detectFaceSystemStatus": "SYSTEM STATUS", "detectFaceUnderstood": "Understood", "detectFaceSystemWillCheck": "System will verify the authenticity of the image", "detectFaceUpdateLocationTooltip": "Update location", "editProfile": "Edit Profile", "error": "Error", "home": "Home", "homeErrorFunction": "Error configuring function", "homeFaceRecognition": "Face Recognition", "homeGoodAfternoon": "Good Afternoon", "homeGoodEvening": "Good Evening", "homeGoodMorning": "Good Morning", "homeLeave": "Leave", "homeNotificationAndUpdate": "Notification & Update", "homeOvertime": "Overtime Hours", "homeTodayAttendance": "Today's Attendance", "homeWorkHours": "Work Hours", "loginEmail": "Email", "loginForgotPassword": "Forgot password?", "loginHintEmail": "Enter your email", "loginHintPassword": "Enter your password", "loginNoAccount": "Don't have an account?", "loginPassword": "Password", "loginPasswordMinLength": "Password must be at least 6 characters", "loginPleaseEnterEmail": "Please enter your email", "loginPleaseEnterFullName": "Please enter your full name", "loginPleaseEnterPassword": "Please enter your password", "loginRememberMe": "Remember me", "loginSignIn": "Sign in", "loginSignUp": "Sign up", "loginSubtitle": "We're happy to see you again. Log in to continue.", "loginSuccess": "Login Successfully", "loginTitle": "Welcome back!", "logout": "Logout", "logOutSuccess": "Login Successfully", "moreAllFeatures": "All Features", "moreAttendance": "Attendance", "moreCalendar": "Calendar", "moreCRM": "CRM Customers", "moreHRM": "HRM Management", "moreLeave": "Leave", "moreOverTime": "Overtime", "moreQR": "QR <PERSON>", "moreSetting": "Setting", "moreSupport": "Support", "moreTeam": "Team", "moreTraining": "Training", "moreRecruitment": "Recruitment", "moreUtility": "Utility & Settings", "navigationBarCustomer": "Customer", "navigationBarHome": "Home", "navigationBarMore": "More", "navigationBarNotify": "Notifications", "navigationBarWork": "Work", "notificationAll": "All", "notificationAllRead": "All Read", "notificationCategory": "Category", "notificationCustomers": "Customers", "notificationDetail": "Notification Detail", "notificationEmpty": "You have no important notifications", "notificationEmptyFromCustomer": "No notifications from customers yet", "notificationImportant": "Important", "notificationLevel": "Level", "notificationNoNewUpdates": "No new updates", "notificationPlaceholder": "All your notifications will appear here", "notificationReceivedTime": "Received Time", "notificationTittle": "Notification", "notificationUnread": "UnRead", "notificationViewAll": "You have viewed all notifications", "onTime": "On Time", "otpNotReceived": "OTP not received", "otpVerifySuccessfully": "OTP verified successfully!", "passwordResetSuccessfully": "Password reset successfully!", "pleaseEnterConfirmPassword": "Please enter your confirm password", "profile": "Profile", "profileDepartment": "Department", "profileJobInfo": "Job Information", "profilePhone": "Phone Number", "profilePosition": "Position", "professionalInformation": "Professional Information", "fullName": "Full Name", "registerConfirmPassword": "Confirm Password", "registerEmail": "Email", "registerFullName": "Full Name", "registerHaveAccount": "Already have an account?", "registerPassword": "Password", "registerPasswordsDoNotMatch": "Passwords do not match", "registerPleaseConfirmPassword": "Please confirm your password", "registerSubTitleSignUp": "Enter your details to create an account.", "registerSuccess": "Registration successful", "registerTitleSignUp": "Create your account", "resendCode": "Resend Code", "retry": "Retry", "save": "Save", "setting": "Setting", "settingAccount": "Account", "settingAddImage": "Add Image", "settingApp": "App Settings", "settingAutoCheckOut": "Auto Check-out", "settingBiometricLogin": "Biometric Login", "settingCancel": "Cancel", "settingCheckInAndSecurity": "Check-in & Security", "settingCheckUpDate": "Check for Updates", "settingChooseAnEmployeeFromList": "Choose an employee from the list", "settingChooseEmployee": "<PERSON><PERSON>loyee", "settingChooseImageALibrary": "Choose an image from the library", "settingConfirmLogOut": "Are you sure you want to log out?", "settingConfirmPassword": "Confirm Password", "settingContactSupport": "Contact Support", "settingCurrentPassword": "Current Password", "settingEmailSupport": "Email Support", "settingError": "An error occurred. Please try again later!", "settingFaceRecognition": "Face Recognition", "settingHelpCenter": "Help Center", "settingIntroduce": "Introduce", "settingLanguage": "Language", "settingLatestVersion": "Latest Version", "settingMessageErrorEmail": "No email app found on the device.", "settingMessageErrorPhone": "Cannot open the phone app.", "settingMessageErrorSocial": "Cannot open this link.", "settingMessageSupport": "Message Support", "settingNewPassword": "New Password", "settingPhoneSupport": "Phone Support", "settingSubChangePass": "Change your password", "settingSubEnableAutoCheckOut": "Enable Auto Check-out", "settingSubEnableFaceRecognition": "Enable Face Recognition", "settingSubGetHelp": "Get help and support", "settingSubTitleProfile": "Edit your personal information", "settingSubUseBiometricLogin": "Use Biometric Login", "settingSupport": "Support", "settingTheme": "Theme", "settingTitleProfile": "Personal Information", "settingUploadImage": "Upload Image", "settingUploadImageSuccess": "Upload image successfully", "settingVersion": "Version", "success": "Success", "thisWeek": "This week", "today": "Today", "initializing": "Initializing", "refreshingStatus": "Refreshing status...", "errorLoadingAttendanceStatus": "Error loading attendance status.", "allSessionsCompleted": "All sessions completed.", "noFurtherActionsAvailable": "No further actions available.", "locationServicesDisabled": "Location services are disabled.", "detectFaceFaceFoundCount": "Face detected ({count})", "errorDetectingFaces": "Error detecting faces.{e}", "notReadyForAction": "System not ready for action. {e}", "checkIn": "Check In", "imageCleared": "Image has been cleared.", "statusUpdated": "Status updated.", "errorGettingLocation": "Error getting location: {error}", "errorCapturingImage": "Error capturing image: {error}", "lastCheckOut": "Last Check-Out", "lastCheckIn": "Last Check-In", "totalCheckIns": "Total Check-Ins", "totalCheckOuts": "Total Check-Outs", "notCurrentlyWorking": "Not currently working", "working": "Working", "loading": "Loading...", "retryInitialization": "Retry initialization", "noActionAvailable": "No action available", "startNewSession": "Start new session", "notification": "Notification", "errorLoadingData": "Error loading data", "detectFaceConfirmCheckOut": "Face detected. Confirm check-out?", "detectFaceConfirmCheckIn": "Face detected. Confirm check-in?", "clearImage": "Clear image", "checkOut": "Check out", "unexpectedErrorPleaseRetry": "Unexpected error occurred. Please retry.", "noFaceDetectedInImage": "No face detected in the image.", "pleaseCaptureImage": "Please capture an image.", "pleaseWaitForLocation": "Please wait for location data...", "attendance": "Attendance", "thisMonth": "Month", "unexpectedError": "An unexpected error occurred.", "todaysSummary": "Today's Summary", "checkedIn": "Checked In", "notCheckedIn": "Not Checked In", "totalHours": "Total Hours", "overtime": "Overtime", "weeklySummary": "Weekly Summary", "workDays": "Work Days", "lateArrivals": "Late Arrivals", "weeklyPerformance": "Weekly Performance", "monthlySummary": "Monthly Summary", "overtimeRequest": "Overtime Request", "overtimeSummary": "Overtime Summary", "overtimeDetails": "Overtime Details", "newRequest": "New Request", "myOvertime": "My Overtime", "pending": "Pending", "approved": "Approved", "rejected": "Rejected", "date": "Date", "startTime": "Start Time", "endTime": "End Time", "reason": "Reason", "overtimeType": "Overtime Type", "regularOvertime": "Regular", "weekendOvertime": "Weekend", "holidayOvertime": "Holiday", "submitRequest": "Submit Request", "submitting": "Submitting...", "select": "Select", "hour": "Hour", "minute": "Minute", "period": "Period", "selected": "Selected", "pleaseSelectAllFields": "Please select all required fields", "endTimeMustBeAfterStartTime": "End time must be after start time", "cannotSelectPastDates": "Cannot select past dates for overtime request", "cannotSelectPastTime": "Cannot select past time for today's overtime", "minimumOvertimeDuration": "Overtime duration must be at least 30 minutes", "maximumOvertimeDuration": "Overtime duration cannot exceed 12 hours", "reasonableWorkingHours": "Overtime hours should be between 6:00 AM and 11:00 PM", "filterByStatus": "Filter by Status", "allStatus": "All Status", "noOvertimeRequests": "No overtime requests found", "submitYourFirstOvertimeRequest": "Submit your first overtime request using the New Request tab", "loadMore": "Load More", "rejectionReason": "Rejection Reason", "all": "All", "totalRequests": "Total Requests", "approve": "Approve", "reject": "Reject", "rejectRequest": "Reject Request", "rejectionReasonDescription": "Please provide a clear reason for rejecting this overtime request. This will help the employee understand the decision.", "enterRejectionReason": "Enter rejection reason...", "rejectionReasonRequired": "Rejection reason is required", "rejectionReasonTooShort": "Rejection reason must be at least 10 characters", "rejectionReasonTooLong": "Rejection reason must not exceed 500 characters", "overtimeReasonRequired": "Overtime reason is required", "overtimeReasonTooShort": "Reason must be at least 10 characters long", "overtimeReasonTooLong": "Reason must not exceed 500 characters", "approvedBy": "Approved by", "rejectedBy": "Rejected by", "time": "Time", "duration": "Duration", "daysOff": "Days Off", "fullMonthHistory": "Full Month History", "inShort": "In", "outShort": "Out", "hoursShort": "Hours", "viewFullHistory": "View Full History", "selectDateToViewDetails": "Select a date to view details", "errorOccurred": "An error occurred", "selectApprover": "Select Approver", "noApproversAvailable": "No approvers available", "cancel": "Cancel", "createRequest": "Create Request", "loadingOvertimeHistory": "Loading overtime history...", "overtimeHistory": "Overtime History", "viewAll": "View All", "recentRequests": "Recent Requests", "calendar": "Calendar", "calendarPageTitle": "Calendar", "calendarTabMonth": "Month", "calendarTabWeek": "Week", "calendarTabAgenda": "Agenda", "calendarHeaderEvents": "Events", "calendarHeaderMeetings": "Meetings", "calendarHeaderHolidays": "Holidays", "calendarWeekViewTitle": "Week View", "calendarWeekEventsTitle": "Week Events", "calendarAddEventDialogTitle": "Add Event", "calendarEventTitleHint": "Event Title", "calendarTimeHint": "Time", "calendarEventTypeHint": "Event Type", "calendarAddEventButton": "Add Event", "calendarEventsOnDate": "Events on {date}", "@calendarEventsOnDate": {"placeholders": {"date": {"type": "String"}}}, "calendarDayAbbreviationsSun": "Sun", "calendarDayAbbreviationsMon": "Mon", "calendarDayAbbreviationsTue": "<PERSON><PERSON>", "calendarDayAbbreviationsWed": "Wed", "calendarDayAbbreviationsThu": "<PERSON>hu", "calendarDayAbbreviationsFri": "<PERSON><PERSON>", "calendarDayAbbreviationsSat": "Sat", "calendarFullMonthNamesJan": "January", "calendarFullMonthNamesFeb": "February", "calendarFullMonthNamesMar": "March", "calendarFullMonthNamesApr": "April", "calendarFullMonthNamesMay": "May", "calendarFullMonthNamesJun": "June", "calendarFullMonthNamesJul": "July", "calendarFullMonthNamesAug": "August", "calendarFullMonthNamesSep": "September", "calendarFullMonthNamesOct": "October", "calendarFullMonthNamesNov": "November", "calendarFullMonthNamesDec": "December", "calendarShortMonthNamesJan": "Jan", "calendarShortMonthNamesFeb": "Feb", "calendarShortMonthNamesMar": "Mar", "calendarShortMonthNamesApr": "Apr", "calendarShortMonthNamesMay": "May", "calendarShortMonthNamesJun": "Jun", "calendarShortMonthNamesJul": "Jul", "calendarShortMonthNamesAug": "Aug", "calendarShortMonthNamesSep": "Sep", "calendarShortMonthNamesOct": "Oct", "calendarShortMonthNamesNov": "Nov", "calendarShortMonthNamesDec": "Dec", "calendarSampleStatEventsCount": "12", "calendarSampleStatMeetingsCount": "8", "calendarSampleStatHolidaysCount": "3", "tomorrow": "Tomorrow", "noEventsForThisWeek": "No events for this week", "noUpcomingEvents": "No upcoming events", "addEvent": "Add Event", "editEvent": "Edit Event", "deleteEvent": "Delete Event", "eventDetails": "Event Details", "eventTitle": "Event Title", "eventDescription": "Event Description", "eventDate": "Event Date", "eventTime": "Event Time", "eventType": "Event Type", "eventLocation": "Event Location", "allDay": "All Day", "recurring": "Recurring", "attendees": "Attendees", "meeting": "Meeting", "leave": "Leave", "holiday": "Holiday", "training": "Training", "event": "Event", "searchEvents": "Search Events", "noEventsFound": "No events found", "loadingEvents": "Loading events...", "addingEvent": "Adding event...", "updatingEvent": "Updating event...", "deletingEvent": "Deleting event...", "eventAddedSuccessfully": "Event added successfully", "eventUpdatedSuccessfully": "Event updated successfully", "eventDeletedSuccessfully": "Event deleted successfully", "failedToAddEvent": "Failed to add event", "failedToUpdateEvent": "Failed to update event", "failedToDeleteEvent": "Failed to delete event", "confirmDeleteEvent": "Are you sure you want to delete this event?", "confirmDeleteEvents": "Are you sure you want to delete {count} events?", "@confirmDeleteEvents": {"placeholders": {"count": {"type": "int"}}}, "selectEventType": "Select Event Type", "selectDate": "Select date", "selectTime": "Select Time", "edit": "Edit", "delete": "Delete", "refresh": "Refresh", "sync": "Sync", "export": "Export", "import": "Import", "filter": "Filter", "clear": "Clear", "apply": "Apply", "close": "Close", "sessionDetails": "Session Details", "sessionNumber": "Session {number}", "@sessionNumber": {"placeholders": {"number": {"type": "int"}}}, "multipleSessionsDetails": "Multiple Sessions Details ({count})", "@multipleSessionsDetails": {"placeholders": {"count": {"type": "int"}}}, "sessionCompleted": "Completed", "sessionInProgress": "In Progress", "location": "Location", "unknownLocation": "Unknown location", "statusOnTime": "On Time", "statusLate": "Late", "statusAbsent": "Absent", "statusOnLeave": "On Leave", "statusWeekend": "Weekend", "statusNoRecord": "No Record", "sunday": "Sunday", "monday": "Monday", "tuesday": "Tuesday", "wednesday": "Wednesday", "thursday": "Thursday", "friday": "Friday", "saturday": "Saturday", "january": "January", "february": "February", "march": "March", "april": "April", "may": "May", "june": "June", "july": "July", "august": "August", "september": "September", "october": "October", "november": "November", "december": "December", "createOvertimeRequest": "Create Overtime Request", "overtimeDate": "Overtime Date", "approver": "Approver", "pleaseSelectDate": "Please select date", "pleaseSelectStartTime": "Please select start time", "pleaseSelectEndTime": "Please select end time", "pleaseEnterReason": "Please enter reason", "pleaseSelectApprover": "Please select approver", "overtimeRequestSubmitted": "Overtime request submitted successfully", "failedToSubmitRequest": "Failed to submit request", "loadingOvertimeRequests": "Loading overtime requests...", "overtimeRequestDetails": "Overtime Request Details", "requestDate": "Request Date", "hours": "hours", "minutes": "minutes", "pendingApproval": "Pending approval", "submittedOn": "Submitted on", "fillDetailsToSubmit": "Fill in the details below to submit your overtime request", "@_LEAVE_FEATURE": {}, "leaveHistory": "Leave History", "leaveRequest": "Leave Request", "leaveRequestDetails": "Leave Request Details", "myLeave": "My Leave", "submitLeaveRequest": "Submit Leave Request", "fillLeaveDetailsToSubmit": "Fill in the details below to submit your leave request", "remainingLeaveDays": "Remaining Leave Days", "outOfDays": "out of {totalDays} days", "@outOfDays": {"placeholders": {"totalDays": {"type": "int"}}}, "used": "Used", "cancelled": "Cancelled", "days": "days", "day": "day", "leavePolicy": "Leave Policy", "recentLeaveRequests": "Recent Leave Requests", "viewAllLeaveHistory": "View All History", "noLeaveRequestsYet": "No leave requests yet", "noFilteredRequests": "No {status} requests", "@noFilteredRequests": {"placeholders": {"status": {"type": "String"}}}, "yourLeaveRequestsWillAppearHere": "Your leave requests will appear here", "showAll": "Show All", "editRequest": "Edit Request", "cancelRequest": "Cancel Request", "requestDetails": "Request Details", "leaveType": "Leave Type", "startDate": "Start Date", "endDate": "End Date", "status": "Status", "approvedOn": "Approved on", "rejectedOn": "Rejected on", "cancelRequestConfirm": "Are you sure you want to cancel this leave request?", "no": "No", "yes": "Yes", "requestCancelled": "Request cancelled", "editFunctionalityComingSoon": "Edit functionality coming soon", "annualLeave": "Annual Leave", "sickLeave": "Sick Leave", "personalLeave": "Personal Leave", "maternityLeave": "Maternity Leave", "unpaidLeave": "Unpaid Leave", "annualLeaveDescription": "Vacation, holiday, or personal time off", "sickLeaveDescription": "Medical appointments or illness", "personalLeaveDescription": "Personal matters or emergencies", "maternityLeaveDescription": "Maternity or paternity leave", "unpaidLeaveDescription": "Extended leave without pay", "dateRange": "Date Range", "durationInfo": "Duration: {duration} {durationUnit}", "@durationInfo": {"placeholders": {"duration": {"type": "int"}, "durationUnit": {"type": "String"}}}, "reasonForLeave": "Reason for Leave", "leaveReasonHint": "Please provide a detailed reason for your leave request...\n\nExample:\n• Family vacation\n• Medical appointment\n• Personal emergency", "pleaseProvideReason": "Please provide a reason for your leave", "pleaseFillAllFields": "Please fill in all required fields", "pleaseSelectStartDate": "Please select a start date", "pleaseSelectEndDate": "Please select an end date", "leaveRequestSubmittedSuccess": "Leave request submitted successfully!", "failedToLoadLeaveData": "Failed to load leave data: {error}", "@failedToLoadLeaveData": {"placeholders": {"error": {"type": "String"}}}, "failedToSubmitLeaveRequest": "Failed to submit leave request: {error}", "@failedToSubmitLeaveRequest": {"placeholders": {"error": {"type": "String"}}}, "@_ADMIN_USER_MANAGEMENT": {}, "adminUserManagement": "User Management", "adminUsers": "Users", "adminStatistics": "Statistics", "adminSettings": "Settings", "addNewUser": "Add New User", "createNewUserAccount": "Create a new user account", "createUser": "Create User", "editUser": "Edit User", "editUserDetails": "Edit User Details", "userDetails": "User Details", "firstName": "First Name", "lastName": "Last Name", "email": "Email", "password": "Password", "confirmPassword": "Confirm Password", "phoneNumber": "Phone Number", "role": "Role", "department": "Department", "position": "Position", "organization": "Organization", "active": "Active", "inactive": "Inactive", "disabled": "Disabled", "deleted": "Deleted", "enabled": "Enabled", "@_ADMIN_ROLES": {}, "roleAdmin": "Administrator", "roleManager": "Manager", "roleHR": "Human Resources", "roleEmployee": "Employee", "roleUser": "User", "@_ADMIN_DEPARTMENTS": {}, "departmentIT": "Information Technology", "departmentHR": "Human Resources", "departmentFinance": "Finance", "departmentMarketing": "Marketing", "departmentOperations": "Operations", "departmentSales": "Sales", "departmentSupport": "Support", "@_ADMIN_ACTIONS": {}, "searchUsers": "Search users...", "filterAndSort": "Filter & Sort", "bulkActions": "Bulk Actions", "selectAll": "Select All", "deselectAll": "Deselect All", "clearSelection": "Clear Selection", "usersSelected": "{count} users selected", "@usersSelected": {"placeholders": {"count": {"type": "int"}}}, "allRoles": "All Roles", "allDepartments": "All Departments", "sortBy": "Sort By", "sortByName": "Name", "sortByEmail": "Email", "sortByRole": "Role", "sortByDepartment": "Department", "sortByCreatedDate": "Created Date", "sortByLastLogin": "Last Login", "sortAscending": "Ascending", "sortDescending": "Descending", "@_ADMIN_USER_ACTIONS": {}, "viewUser": "View User", "editUserAction": "Edit User", "deleteUser": "Delete User", "restoreUser": "Restore User", "disableUser": "Disable User", "enableUser": "Enable User", "resetPassword": "Reset Password", "toggleUserStatus": "Toggle Status", "confirmDeleteUser": "Are you sure you want to delete this user?", "confirmRestoreUser": "Are you sure you want to restore this user?", "confirmDisableUser": "Are you sure you want to disable this user?", "confirmEnableUser": "Are you sure you want to enable this user?", "confirmResetPassword": "Are you sure you want to reset this user's password?", "@_ADMIN_BULK_ACTIONS": {}, "bulkDelete": "Bulk Delete", "bulkRestore": "Bulk Restore", "bulkDisable": "Bulk Disable", "bulkEnable": "Bulk Enable", "confirmBulkDelete": "Are you sure you want to delete {count} users?", "@confirmBulkDelete": {"placeholders": {"count": {"type": "int"}}}, "confirmBulkRestore": "Are you sure you want to restore {count} users?", "@confirmBulkRestore": {"placeholders": {"count": {"type": "int"}}}, "@_ADMIN_STATISTICS": {}, "userOverview": "User Overview", "totalUsers": "Total Users", "activeUsers": "Active Users", "disabledUsers": "Disabled Users", "deletedUsers": "Deleted Users", "roleDistribution": "Role Distribution", "analytics": "Analytics", "userGrowth": "User Growth", "userActivity": "User Activity", "departmentBreakdown": "Department Breakdown", "@_ADMIN_MESSAGES": {}, "userCreatedSuccessfully": "User created successfully!", "userUpdatedSuccessfully": "User updated successfully!", "userDeletedSuccessfully": "User deleted successfully!", "userRestoredSuccessfully": "User restored successfully!", "userDisabledSuccessfully": "User disabled successfully!", "userEnabledSuccessfully": "User enabled successfully!", "failedToCreateUser": "Failed to create user: {error}", "@failedToCreateUser": {"placeholders": {"error": {"type": "String"}}}, "failedToUpdateUser": "Failed to update user: {error}", "@failedToUpdateUser": {"placeholders": {"error": {"type": "String"}}}, "failedToDeleteUser": "Failed to delete user: {error}", "@failedToDeleteUser": {"placeholders": {"error": {"type": "String"}}}, "failedToLoadUsers": "Failed to load users: {error}", "@failedToLoadUsers": {"placeholders": {"error": {"type": "String"}}}, "noUsersFound": "No users found", "loadingUsers": "Loading users...", "refreshUsers": "Refresh Users", "addUser": "Add User", "@_ADMIN_VALIDATION": {}, "firstNameRequired": "First name is required", "lastNameRequired": "Last name is required", "emailRequired": "Email is required", "emailInvalid": "Please enter a valid email address", "passwordRequired": "Password is required", "passwordTooShort": "Password must be at least 8 characters", "passwordTooWeak": "Password must contain uppercase, lowercase, number and special character", "phoneInvalid": "Please enter a valid phone number", "roleRequired": "Role is required", "departmentRequired": "Department is required", "@_ADMIN_FORM_LABELS": {}, "requiredField": "Required field", "optionalField": "Optional field", "enterFirstName": "Enter first name", "enterLastName": "Enter last name", "enterEmail": "Enter email address", "enterPassword": "Enter password", "enterPhoneNumber": "Enter phone number", "selectRole": "Select role", "selectDepartment": "Select department", "enterPosition": "Enter position", "enterOrganization": "Enter organization", "@_ADMIN_SECTIONS": {}, "personalInformation": "Personal Information", "workInformation": "Work Information", "roleAndStatus": "Role & Status", "saveChanges": "Save Changes", "roleManagement": "Role Management", "departmentManagement": "Department Management", "createNewRole": "Create New Role", "editRole": "Edit Role", "createNewDepartment": "Create New Department", "editDepartment": "Edit Department", "roleName": "Role Name", "departmentName": "Department Name", "departmentCode": "Department Code", "departmentDescription": "Department Description", "parentDepartment": "Parent Department", "noParentTopLevel": "No Parent (Top Level)", "selectParentDepartment": "Select parent department (optional)", "noDepartment": "No Department", "basicInformation": "Basic Information", "hierarchy": "Hierarchy", "departmentStatus": "Department Status", "enable": "Enable", "disable": "Disable", "restore": "Rest<PERSON>", "deleteRole": "Delete Role", "deleteDepartment": "Delete Department", "deleteRoleConfirm": "Are you sure you want to delete the role \"{roleName}\"?", "deleteDepartmentConfirm": "Are you sure you want to delete the department \"{departmentName}\"?", "roleCreatedSuccessfully": "Role created successfully", "roleUpdatedSuccessfully": "Role updated successfully", "roleDeletedSuccessfully": "Role deleted successfully", "departmentCreatedSuccessfully": "Department created successfully", "departmentUpdatedSuccessfully": "Department updated successfully", "departmentDeletedSuccessfully": "Department deleted successfully", "departmentRestoredSuccessfully": "Department restored successfully", "departmentStatusUpdatedSuccessfully": "Department status updated successfully", "noRolesFound": "No roles found", "noDepartmentsFound": "No departments found", "createFirstRole": "Create your first role to get started", "createFirstDepartment": "Create your first department to get started", "addRole": "Add Role", "addDepartment": "Add Department", "updateRole": "Update Role", "updateDepartment": "Update Department", "createRole": "Create Role", "createDepartment": "Create Department", "searchRoles": "Search roles...", "searchDepartments": "Search departments...", "includeDeleted": "Include Deleted", "roleInformation": "Role Information", "roleDetails": "Role Details", "created": "Created", "lastUpdated": "Last Updated", "roleId": "Role ID", "departmentId": "Department ID", "enterRoleName": "Enter role name (e.g., Manager, HR, Developer)", "enterDepartmentName": "Enter department name (e.g., Human Resources, IT)", "enterDepartmentCode": "Enter department code (e.g., HR, IT, FIN)", "enterDepartmentDescription": "Enter department description (optional)", "roleNameRequired": "Role name is required", "departmentNameRequired": "Department name is required", "roleNameMinLength": "Role name must be at least 2 characters", "departmentNameMinLength": "Department name must be at least 2 characters", "roleNameMaxLength": "Role name must be less than 50 characters", "departmentNameMaxLength": "Department name must be less than 100 characters", "departmentCodeMinLength": "Department code must be at least 2 characters", "departmentCodeMaxLength": "Department code must be less than 10 characters", "roleNameInvalidCharacters": "Role name contains invalid characters", "departmentCodeInvalidCharacters": "Department code contains invalid characters", "roleNameAlreadyExists": "Role name already exists", "departmentNameAlreadyExists": "Department name already exists", "departmentCodeAlreadyExists": "Department code already exists", "changingRoleNameWarning": "Changing the role name may affect users assigned to this role.", "roleAndDepartmentInfo": "Role names and codes should be unique. They will be used throughout the system for access control.", "departmentHierarchyInfo": "Department names and codes should be unique. You can create a hierarchy by selecting a parent department."}