import 'dart:async';
import 'package:flutter/material.dart';
import 'package:iconsax/iconsax.dart';

typedef OnDragEnd = void Function(Offset position);

class ChatHeadWidget extends StatefulWidget {
  final Offset initialPosition;
  final VoidCallback onTap;
  final OnDragEnd onDragEnd;

  const ChatHeadWidget({
    super.key,
    required this.initialPosition,
    required this.onTap,
    required this.onDragEnd,
  });

  @override
  _ChatHeadWidgetState createState() => _ChatHeadWidgetState();
}

class _ChatHeadWidgetState extends State<ChatHeadWidget>
    with SingleTickerProviderStateMixin {
  late Offset _position;
  late final AnimationController _snapController;
  Animation<Offset>? _snapAnimation;

  Timer? _fadeTimer;
  bool _isFaded = false;
  static const _fadeDuration = Duration(seconds: 5);
  static const double _chatHeadSize = 60.0;
  static const double _paddingFromEdge = 10.0;

  @override
  void initState() {
    super.initState();
    _position = widget.initialPosition;
    _snapController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );
    _snapController.addListener(() {
      if (_snapController.isAnimating) {
        setState(() => _position = _snapAnimation!.value);
      }
    });
    _startFadeTimer();
  }

  @override
  void dispose() {
    _snapController.dispose();
    _fadeTimer?.cancel();
    super.dispose();
  }

  void _resetFadeTimer() {
    _fadeTimer?.cancel();
    if (_isFaded) {
      setState(() => _isFaded = false);
    }
    _startFadeTimer();
  }

  void _startFadeTimer() {
    _fadeTimer = Timer(_fadeDuration, () {
      if (mounted) {
        setState(() => _isFaded = true);
      }
    });
  }

  void _runSnapAnimation(Offset endPosition) {
    _snapAnimation = Tween<Offset>(
      begin: _position,
      end: endPosition,
    ).animate(CurvedAnimation(parent: _snapController, curve: Curves.easeOut));
    _snapController.forward(from: 0.0);
  }

  @override
  Widget build(BuildContext context) {
    final safeAreaSize = MediaQuery.of(context).size;

    return Positioned(
      left: _position.dx,
      top: _position.dy,
      child: GestureDetector(
        onTap: () {
          _resetFadeTimer();
          widget.onTap();
        },
        onPanStart: (details) {
          _resetFadeTimer();
          if (_snapController.isAnimating) _snapController.stop();
        },
        onPanUpdate: (details) {
          final newPosition = _position + details.delta;
          final clampedPosition = Offset(
            newPosition.dx.clamp(
              _paddingFromEdge,
              safeAreaSize.width - _chatHeadSize - _paddingFromEdge,
            ),
            newPosition.dy.clamp(
              0.0,
              safeAreaSize.height - _chatHeadSize - _paddingFromEdge,
            ),
          );
          if (newPosition != _position) {
            setState(() => _position = clampedPosition);
          }
        },
        onPanEnd: (details) {
          final double endX =
              (_position.dx + _chatHeadSize / 2) < safeAreaSize.width / 2
              ? _paddingFromEdge
              : safeAreaSize.width - _chatHeadSize - _paddingFromEdge;

          // Logic cho trục Y
          double endY = _position.dy;
          const double thresholdFromBottom = 100.0; // Ngưỡng cách bottom
          final double bottomLimit =
              safeAreaSize.height - _chatHeadSize - _paddingFromEdge;
          final double middleY =
              safeAreaSize.height / 2 - _chatHeadSize / 2; // Vị trí giữa theo Y

          if (_position.dy > bottomLimit - thresholdFromBottom) {
            endY = middleY; // Nhảy về giữa nếu gần bottom
          } else {
            endY = _position.dy.clamp(
              0.0,
              bottomLimit,
            ); // Giữ nguyên nếu không gần bottom
          }

          final endPosition = Offset(endX, endY);
          _runSnapAnimation(endPosition);
          widget.onDragEnd(endPosition);
          _resetFadeTimer();
        },
        child: AnimatedOpacity(
          duration: const Duration(milliseconds: 300),
          opacity: _isFaded ? 0.5 : 1.0,
          child: Container(
            width: _chatHeadSize,
            height: _chatHeadSize,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(16),
              gradient: LinearGradient(
                colors: [Colors.blue.shade400, Colors.blue.shade600],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.3),
                  blurRadius: 15,
                  spreadRadius: 2,
                  offset: const Offset(0, 8),
                ),
              ],
            ),
            child: const Icon(
              Iconsax.message_21,
              color: Colors.white,
              size: 32,
            ),
          ),
        ),
      ),
    );
  }
}
