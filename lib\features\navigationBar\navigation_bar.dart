// lib/widgets/custom_navigation_bar.dart

import 'package:flutter/material.dart';
import 'package:golderhr/core/extensions/l10n_extension.dart';
import 'package:google_nav_bar/google_nav_bar.dart';
import 'package:iconsax/iconsax.dart';

class CustomNavigationBar extends StatelessWidget {
  final int selectedIndex;
  final Function(int) onTabChange;

  const CustomNavigationBar({
    super.key,
    required this.selectedIndex,
    required this.onTabChange,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(blurRadius: 20, color: Colors.black.withValues(alpha: 0.1)),
        ],
      ),
      child: SafeArea(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 10.0, vertical: 8.0),
          // Giảm padding ngang
          child: GNav(
            rippleColor: Colors.grey[300]!,
            hoverColor: Colors.grey[100]!,
            gap: 6,
            activeColor: Colors.deepPurple,
            iconSize: 22,
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
            // Giảm padding của tab
            duration: const Duration(milliseconds: 400),
            tabBackgroundColor: Colors.deepPurple.withValues(alpha: 0.1),
            color: Colors.black54,
            textStyle: const TextStyle(
              fontSize: 14,
              color: Colors.deepPurple,
              fontWeight: FontWeight.w600,
            ),
            tabs: [
              GButton(icon: Iconsax.home, text: context.l10n.navigationBarHome),
              GButton(
                icon: Iconsax.people,
                text: context.l10n.navigationBarCustomer,
              ),
              GButton(
                icon: Iconsax.briefcase,
                text: context.l10n.navigationBarWork,
              ),
              GButton(
                icon: Iconsax.notification,
                text: context.l10n.navigationBarNotify,
              ),
              GButton(
                icon: Iconsax.more_square,
                text: context.l10n.navigationBarMore,
              ),
            ],
            selectedIndex: selectedIndex,
            onTabChange: onTabChange,
          ),
        ),
      ),
    );
  }
}
