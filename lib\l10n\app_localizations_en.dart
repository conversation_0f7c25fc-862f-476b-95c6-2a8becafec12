// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for English (`en`).
class AppLocalizationsEn extends AppLocalizations {
  AppLocalizationsEn([String locale = 'en']) : super(locale);

  @override
  String get acceptTerms => 'I accept the terms and conditions';

  @override
  String get authForgotPassword => 'Forgot Password';

  @override
  String get authNext => 'Next';

  @override
  String get authResetPassword => 'Reset Password';

  @override
  String get authSubTitleForgotPassword =>
      'Enter your email to reset your password.';

  @override
  String get authSubTitleResetPassword => 'Enter your new password.';

  @override
  String get authSubTitleVerifyOtp =>
      'Enter the 4-digit code sent to your email.';

  @override
  String get authTitleResetPassword => 'Reset Password';

  @override
  String get authVerifyOtp => 'Verify OTP';

  @override
  String get changePassword => 'Change Password';

  @override
  String get contactInfo => 'Contact Information';

  @override
  String get detectFaceAnalyzingSecurity => 'Analyzing security...';

  @override
  String get detectFaceAttendanceCompleted => 'Attendance completed';

  @override
  String get detectFaceCaptureImage => 'Capture Image';

  @override
  String get detectFaceCaptureToStart => 'Capture photo to start';

  @override
  String get detectFaceCheckIn => 'Check-In';

  @override
  String detectFaceCheckInError(Object error) {
    return 'Check-in failed: $error';
  }

  @override
  String get detectFaceCheckInSuccess => 'Check-in successful';

  @override
  String get detectFaceCheckOut => 'Check-Out';

  @override
  String detectFaceCheckOutError(Object error) {
    return 'Face CheckOut Error $error';
  }

  @override
  String get detectFaceCheckOutSuccess => 'Check-out successful';

  @override
  String get detectFaceCurrentLocation => 'Current location';

  @override
  String get detectFaceFaceAnalysis => 'Face analysis';

  @override
  String get detectFaceFaceAnalysisDescription =>
      'Use ML to detect facial features and ensure eyes are open.';

  @override
  String get detectFaceFaceNotFound => 'No face found in the image.';

  @override
  String detectFaceFacesDetected(Object count) {
    return 'Detected $count face(s)';
  }

  @override
  String get detectFaceGettingLocation => 'Getting location...';

  @override
  String get detectFaceImageCapturedReady =>
      'Image captured. Ready to check in.';

  @override
  String get detectFaceImageNotCaptured => 'Image capture canceled.';

  @override
  String get detectFaceLocationNotAvailable => 'Face Location Not Available';

  @override
  String get detectFaceLocationPermissionDenied => 'Location access denied.';

  @override
  String get detectFaceLocationPermissionPermanentlyDenied =>
      'Location access permanently denied.';

  @override
  String get detectFaceLocationVerification => 'Location verification';

  @override
  String get detectFaceLocationVerificationDescription =>
      'Ensure check-in location is within the company area.';

  @override
  String get detectFaceOpeningCamera => 'Opening camera...';

  @override
  String get detectFaceProcessingCheckIn => 'Processing check-in...';

  @override
  String get detectFaceProcessingCheckOut => 'Processing check-out...';

  @override
  String get detectFaceReadyForCheckIn => 'Ready for check-in';

  @override
  String get detectFaceReadyForCheckOut => 'Ready for check-out';

  @override
  String get detectFaceRetakeImage => 'Retake';

  @override
  String get detectFaceSecureCheckInSystem => 'Secure Check-In System';

  @override
  String get detectFaceSecureCheckInTitle => 'Secure Check-In';

  @override
  String get detectFaceSecurityInfoNotice =>
      'The system uses multiple layers of analysis to ensure transparency and fairness';

  @override
  String get detectFaceSecurityInfoTitle => 'Security Information';

  @override
  String get detectFaceSecurityInfoTooltip => 'Security information';

  @override
  String get detectFaceSystemStatus => 'SYSTEM STATUS';

  @override
  String get detectFaceUnderstood => 'Understood';

  @override
  String get detectFaceSystemWillCheck =>
      'System will verify the authenticity of the image';

  @override
  String get detectFaceUpdateLocationTooltip => 'Update location';

  @override
  String get editProfile => 'Edit Profile';

  @override
  String get error => 'Error';

  @override
  String get home => 'Home';

  @override
  String get homeErrorFunction => 'Error configuring function';

  @override
  String get homeFaceRecognition => 'Face Recognition';

  @override
  String get homeGoodAfternoon => 'Good Afternoon';

  @override
  String get homeGoodEvening => 'Good Evening';

  @override
  String get homeGoodMorning => 'Good Morning';

  @override
  String get homeLeave => 'Leave';

  @override
  String get homeNotificationAndUpdate => 'Notification & Update';

  @override
  String get homeOvertime => 'Overtime Hours';

  @override
  String get homeTodayAttendance => 'Today\'s Attendance';

  @override
  String get homeWorkHours => 'Work Hours';

  @override
  String get loginEmail => 'Email';

  @override
  String get loginForgotPassword => 'Forgot password?';

  @override
  String get loginHintEmail => 'Enter your email';

  @override
  String get loginHintPassword => 'Enter your password';

  @override
  String get loginNoAccount => 'Don\'t have an account?';

  @override
  String get loginPassword => 'Password';

  @override
  String get loginPasswordMinLength => 'Password must be at least 6 characters';

  @override
  String get loginPleaseEnterEmail => 'Please enter your email';

  @override
  String get loginPleaseEnterFullName => 'Please enter your full name';

  @override
  String get loginPleaseEnterPassword => 'Please enter your password';

  @override
  String get loginRememberMe => 'Remember me';

  @override
  String get loginSignIn => 'Sign in';

  @override
  String get loginSignUp => 'Sign up';

  @override
  String get loginSubtitle =>
      'We\'re happy to see you again. Log in to continue.';

  @override
  String get loginSuccess => 'Login Successfully';

  @override
  String get loginTitle => 'Welcome back!';

  @override
  String get logout => 'Logout';

  @override
  String get logOutSuccess => 'Login Successfully';

  @override
  String get moreAllFeatures => 'All Features';

  @override
  String get moreAttendance => 'Attendance';

  @override
  String get moreCalendar => 'Calendar';

  @override
  String get moreCRM => 'CRM Customers';

  @override
  String get moreHRM => 'HRM Management';

  @override
  String get moreLeave => 'Leave';

  @override
  String get moreOverTime => 'Overtime';

  @override
  String get moreQR => 'QR Scan';

  @override
  String get moreSetting => 'Setting';

  @override
  String get moreSupport => 'Support';

  @override
  String get moreTeam => 'Team';

  @override
  String get moreTraining => 'Training';

  @override
  String get moreRecruitment => 'Recruitment';

  @override
  String get moreUtility => 'Utility & Settings';

  @override
  String get navigationBarCustomer => 'Customer';

  @override
  String get navigationBarHome => 'Home';

  @override
  String get navigationBarMore => 'More';

  @override
  String get navigationBarNotify => 'Notifications';

  @override
  String get navigationBarWork => 'Work';

  @override
  String get notificationAll => 'All';

  @override
  String get notificationAllRead => 'All Read';

  @override
  String get notificationCategory => 'Category';

  @override
  String get notificationCustomers => 'Customers';

  @override
  String get notificationDetail => 'Notification Detail';

  @override
  String get notificationEmpty => 'You have no important notifications';

  @override
  String get notificationEmptyFromCustomer =>
      'No notifications from customers yet';

  @override
  String get notificationImportant => 'Important';

  @override
  String get notificationLevel => 'Level';

  @override
  String get notificationNoNewUpdates => 'No new updates';

  @override
  String get notificationPlaceholder =>
      'All your notifications will appear here';

  @override
  String get notificationReceivedTime => 'Received Time';

  @override
  String get notificationTittle => 'Notification';

  @override
  String get notificationUnread => 'UnRead';

  @override
  String get notificationViewAll => 'You have viewed all notifications';

  @override
  String get onTime => 'On Time';

  @override
  String get otpNotReceived => 'OTP not received';

  @override
  String get otpVerifySuccessfully => 'OTP verified successfully!';

  @override
  String get passwordResetSuccessfully => 'Password reset successfully!';

  @override
  String get pleaseEnterConfirmPassword => 'Please enter your confirm password';

  @override
  String get profile => 'Profile';

  @override
  String get profileDepartment => 'Department';

  @override
  String get profileJobInfo => 'Job Information';

  @override
  String get profilePhone => 'Phone Number';

  @override
  String get profilePosition => 'Position';

  @override
  String get professionalInformation => 'Professional Information';

  @override
  String get fullName => 'Full Name';

  @override
  String get registerConfirmPassword => 'Confirm Password';

  @override
  String get registerEmail => 'Email';

  @override
  String get registerFullName => 'Full Name';

  @override
  String get registerHaveAccount => 'Already have an account?';

  @override
  String get registerPassword => 'Password';

  @override
  String get registerPasswordsDoNotMatch => 'Passwords do not match';

  @override
  String get registerPleaseConfirmPassword => 'Please confirm your password';

  @override
  String get registerSubTitleSignUp =>
      'Enter your details to create an account.';

  @override
  String get registerSuccess => 'Registration successful';

  @override
  String get registerTitleSignUp => 'Create your account';

  @override
  String get resendCode => 'Resend Code';

  @override
  String get retry => 'Retry';

  @override
  String get save => 'Save';

  @override
  String get setting => 'Setting';

  @override
  String get settingAccount => 'Account';

  @override
  String get settingAddImage => 'Add Image';

  @override
  String get settingApp => 'App Settings';

  @override
  String get settingAutoCheckOut => 'Auto Check-out';

  @override
  String get settingBiometricLogin => 'Biometric Login';

  @override
  String get settingCancel => 'Cancel';

  @override
  String get settingCheckInAndSecurity => 'Check-in & Security';

  @override
  String get settingCheckUpDate => 'Check for Updates';

  @override
  String get settingChooseAnEmployeeFromList =>
      'Choose an employee from the list';

  @override
  String get settingChooseEmployee => 'Choose Employee';

  @override
  String get settingChooseImageALibrary => 'Choose an image from the library';

  @override
  String get settingConfirmLogOut => 'Are you sure you want to log out?';

  @override
  String get settingConfirmPassword => 'Confirm Password';

  @override
  String get settingContactSupport => 'Contact Support';

  @override
  String get settingCurrentPassword => 'Current Password';

  @override
  String get settingEmailSupport => 'Email Support';

  @override
  String get settingError => 'An error occurred. Please try again later!';

  @override
  String get settingFaceRecognition => 'Face Recognition';

  @override
  String get settingHelpCenter => 'Help Center';

  @override
  String get settingIntroduce => 'Introduce';

  @override
  String get settingLanguage => 'Language';

  @override
  String get settingLatestVersion => 'Latest Version';

  @override
  String get settingMessageErrorEmail => 'No email app found on the device.';

  @override
  String get settingMessageErrorPhone => 'Cannot open the phone app.';

  @override
  String get settingMessageErrorSocial => 'Cannot open this link.';

  @override
  String get settingMessageSupport => 'Message Support';

  @override
  String get settingNewPassword => 'New Password';

  @override
  String get settingPhoneSupport => 'Phone Support';

  @override
  String get settingSubChangePass => 'Change your password';

  @override
  String get settingSubEnableAutoCheckOut => 'Enable Auto Check-out';

  @override
  String get settingSubEnableFaceRecognition => 'Enable Face Recognition';

  @override
  String get settingSubGetHelp => 'Get help and support';

  @override
  String get settingSubTitleProfile => 'Edit your personal information';

  @override
  String get settingSubUseBiometricLogin => 'Use Biometric Login';

  @override
  String get settingSupport => 'Support';

  @override
  String get settingTheme => 'Theme';

  @override
  String get settingTitleProfile => 'Personal Information';

  @override
  String get settingUploadImage => 'Upload Image';

  @override
  String get settingUploadImageSuccess => 'Upload image successfully';

  @override
  String get settingVersion => 'Version';

  @override
  String get success => 'Success';

  @override
  String get thisWeek => 'This week';

  @override
  String get today => 'Today';

  @override
  String get initializing => 'Initializing';

  @override
  String get refreshingStatus => 'Refreshing status...';

  @override
  String get errorLoadingAttendanceStatus => 'Error loading attendance status.';

  @override
  String get allSessionsCompleted => 'All sessions completed.';

  @override
  String get noFurtherActionsAvailable => 'No further actions available.';

  @override
  String get locationServicesDisabled => 'Location services are disabled.';

  @override
  String detectFaceFaceFoundCount(Object count) {
    return 'Face detected ($count)';
  }

  @override
  String errorDetectingFaces(Object e) {
    return 'Error detecting faces.$e';
  }

  @override
  String notReadyForAction(Object e) {
    return 'System not ready for action. $e';
  }

  @override
  String get checkIn => 'Check In';

  @override
  String get imageCleared => 'Image has been cleared.';

  @override
  String get statusUpdated => 'Status updated.';

  @override
  String errorGettingLocation(Object error) {
    return 'Error getting location: $error';
  }

  @override
  String errorCapturingImage(Object error) {
    return 'Error capturing image: $error';
  }

  @override
  String get lastCheckOut => 'Last Check-Out';

  @override
  String get lastCheckIn => 'Last Check-In';

  @override
  String get totalCheckIns => 'Total Check-Ins';

  @override
  String get totalCheckOuts => 'Total Check-Outs';

  @override
  String get notCurrentlyWorking => 'Not currently working';

  @override
  String get working => 'Working';

  @override
  String get loading => 'Loading...';

  @override
  String get retryInitialization => 'Retry initialization';

  @override
  String get noActionAvailable => 'No action available';

  @override
  String get startNewSession => 'Start new session';

  @override
  String get notification => 'Notification';

  @override
  String get errorLoadingData => 'Error loading data';

  @override
  String get detectFaceConfirmCheckOut => 'Face detected. Confirm check-out?';

  @override
  String get detectFaceConfirmCheckIn => 'Face detected. Confirm check-in?';

  @override
  String get clearImage => 'Clear image';

  @override
  String get checkOut => 'Check out';

  @override
  String get unexpectedErrorPleaseRetry =>
      'Unexpected error occurred. Please retry.';

  @override
  String get noFaceDetectedInImage => 'No face detected in the image.';

  @override
  String get pleaseCaptureImage => 'Please capture an image.';

  @override
  String get pleaseWaitForLocation => 'Please wait for location data...';

  @override
  String get attendance => 'Attendance';

  @override
  String get thisMonth => 'Month';

  @override
  String get unexpectedError => 'An unexpected error occurred.';

  @override
  String get todaysSummary => 'Today\'s Summary';

  @override
  String get checkedIn => 'Checked In';

  @override
  String get notCheckedIn => 'Not Checked In';

  @override
  String get totalHours => 'Total Hours';

  @override
  String get overtime => 'Overtime';

  @override
  String get weeklySummary => 'Weekly Summary';

  @override
  String get workDays => 'Work Days';

  @override
  String get lateArrivals => 'Late Arrivals';

  @override
  String get weeklyPerformance => 'Weekly Performance';

  @override
  String get monthlySummary => 'Monthly Summary';

  @override
  String get overtimeRequest => 'Overtime Request';

  @override
  String get overtimeSummary => 'Overtime Summary';

  @override
  String get overtimeDetails => 'Overtime Details';

  @override
  String get newRequest => 'New Request';

  @override
  String get myOvertime => 'My Overtime';

  @override
  String get pending => 'Pending';

  @override
  String get approved => 'Approved';

  @override
  String get rejected => 'Rejected';

  @override
  String get date => 'Date';

  @override
  String get startTime => 'Start Time';

  @override
  String get endTime => 'End Time';

  @override
  String get reason => 'Reason';

  @override
  String get overtimeType => 'Overtime Type';

  @override
  String get regularOvertime => 'Regular';

  @override
  String get weekendOvertime => 'Weekend';

  @override
  String get holidayOvertime => 'Holiday';

  @override
  String get submitRequest => 'Submit Request';

  @override
  String get submitting => 'Submitting...';

  @override
  String get select => 'Select';

  @override
  String get hour => 'Hour';

  @override
  String get minute => 'Minute';

  @override
  String get period => 'Period';

  @override
  String get selected => 'Selected';

  @override
  String get pleaseSelectAllFields => 'Please select all required fields';

  @override
  String get endTimeMustBeAfterStartTime => 'End time must be after start time';

  @override
  String get cannotSelectPastDates =>
      'Cannot select past dates for overtime request';

  @override
  String get cannotSelectPastTime =>
      'Cannot select past time for today\'s overtime';

  @override
  String get minimumOvertimeDuration =>
      'Overtime duration must be at least 30 minutes';

  @override
  String get maximumOvertimeDuration =>
      'Overtime duration cannot exceed 12 hours';

  @override
  String get reasonableWorkingHours =>
      'Overtime hours should be between 6:00 AM and 11:00 PM';

  @override
  String get filterByStatus => 'Filter by Status';

  @override
  String get allStatus => 'All Status';

  @override
  String get noOvertimeRequests => 'No overtime requests found';

  @override
  String get submitYourFirstOvertimeRequest =>
      'Submit your first overtime request using the New Request tab';

  @override
  String get loadMore => 'Load More';

  @override
  String get rejectionReason => 'Rejection Reason';

  @override
  String get all => 'All';

  @override
  String get totalRequests => 'Total Requests';

  @override
  String get approve => 'Approve';

  @override
  String get reject => 'Reject';

  @override
  String get rejectRequest => 'Reject Request';

  @override
  String get rejectionReasonDescription =>
      'Please provide a clear reason for rejecting this overtime request. This will help the employee understand the decision.';

  @override
  String get enterRejectionReason => 'Enter rejection reason...';

  @override
  String get rejectionReasonRequired => 'Rejection reason is required';

  @override
  String get rejectionReasonTooShort =>
      'Rejection reason must be at least 10 characters';

  @override
  String get rejectionReasonTooLong =>
      'Rejection reason must not exceed 500 characters';

  @override
  String get overtimeReasonRequired => 'Overtime reason is required';

  @override
  String get overtimeReasonTooShort =>
      'Reason must be at least 10 characters long';

  @override
  String get overtimeReasonTooLong => 'Reason must not exceed 500 characters';

  @override
  String get approvedBy => 'Approved by';

  @override
  String get rejectedBy => 'Rejected by';

  @override
  String get time => 'Time';

  @override
  String get duration => 'Duration';

  @override
  String get daysOff => 'Days Off';

  @override
  String get fullMonthHistory => 'Full Month History';

  @override
  String get inShort => 'In';

  @override
  String get outShort => 'Out';

  @override
  String get hoursShort => 'Hours';

  @override
  String get viewFullHistory => 'View Full History';

  @override
  String get selectDateToViewDetails => 'Select a date to view details';

  @override
  String get errorOccurred => 'An error occurred';

  @override
  String get selectApprover => 'Select Approver';

  @override
  String get noApproversAvailable => 'No approvers available';

  @override
  String get cancel => 'Cancel';

  @override
  String get createRequest => 'Create Request';

  @override
  String get loadingOvertimeHistory => 'Loading overtime history...';

  @override
  String get overtimeHistory => 'Overtime History';

  @override
  String get viewAll => 'View All';

  @override
  String get recentRequests => 'Recent Requests';

  @override
  String get calendar => 'Calendar';

  @override
  String get calendarPageTitle => 'Calendar';

  @override
  String get calendarTabMonth => 'Month';

  @override
  String get calendarTabWeek => 'Week';

  @override
  String get calendarTabAgenda => 'Agenda';

  @override
  String get calendarHeaderEvents => 'Events';

  @override
  String get calendarHeaderMeetings => 'Meetings';

  @override
  String get calendarHeaderHolidays => 'Holidays';

  @override
  String get calendarWeekViewTitle => 'Week View';

  @override
  String get calendarWeekEventsTitle => 'Week Events';

  @override
  String get calendarAddEventDialogTitle => 'Add Event';

  @override
  String get calendarEventTitleHint => 'Event Title';

  @override
  String get calendarTimeHint => 'Time';

  @override
  String get calendarEventTypeHint => 'Event Type';

  @override
  String get calendarAddEventButton => 'Add Event';

  @override
  String calendarEventsOnDate(String date) {
    return 'Events on $date';
  }

  @override
  String get calendarDayAbbreviationsSun => 'Sun';

  @override
  String get calendarDayAbbreviationsMon => 'Mon';

  @override
  String get calendarDayAbbreviationsTue => 'Tue';

  @override
  String get calendarDayAbbreviationsWed => 'Wed';

  @override
  String get calendarDayAbbreviationsThu => 'Thu';

  @override
  String get calendarDayAbbreviationsFri => 'Fri';

  @override
  String get calendarDayAbbreviationsSat => 'Sat';

  @override
  String get calendarFullMonthNamesJan => 'January';

  @override
  String get calendarFullMonthNamesFeb => 'February';

  @override
  String get calendarFullMonthNamesMar => 'March';

  @override
  String get calendarFullMonthNamesApr => 'April';

  @override
  String get calendarFullMonthNamesMay => 'May';

  @override
  String get calendarFullMonthNamesJun => 'June';

  @override
  String get calendarFullMonthNamesJul => 'July';

  @override
  String get calendarFullMonthNamesAug => 'August';

  @override
  String get calendarFullMonthNamesSep => 'September';

  @override
  String get calendarFullMonthNamesOct => 'October';

  @override
  String get calendarFullMonthNamesNov => 'November';

  @override
  String get calendarFullMonthNamesDec => 'December';

  @override
  String get calendarShortMonthNamesJan => 'Jan';

  @override
  String get calendarShortMonthNamesFeb => 'Feb';

  @override
  String get calendarShortMonthNamesMar => 'Mar';

  @override
  String get calendarShortMonthNamesApr => 'Apr';

  @override
  String get calendarShortMonthNamesMay => 'May';

  @override
  String get calendarShortMonthNamesJun => 'Jun';

  @override
  String get calendarShortMonthNamesJul => 'Jul';

  @override
  String get calendarShortMonthNamesAug => 'Aug';

  @override
  String get calendarShortMonthNamesSep => 'Sep';

  @override
  String get calendarShortMonthNamesOct => 'Oct';

  @override
  String get calendarShortMonthNamesNov => 'Nov';

  @override
  String get calendarShortMonthNamesDec => 'Dec';

  @override
  String get calendarSampleStatEventsCount => '12';

  @override
  String get calendarSampleStatMeetingsCount => '8';

  @override
  String get calendarSampleStatHolidaysCount => '3';

  @override
  String get tomorrow => 'Tomorrow';

  @override
  String get noEventsForThisWeek => 'No events for this week';

  @override
  String get noUpcomingEvents => 'No upcoming events';

  @override
  String get addEvent => 'Add Event';

  @override
  String get editEvent => 'Edit Event';

  @override
  String get deleteEvent => 'Delete Event';

  @override
  String get eventDetails => 'Event Details';

  @override
  String get eventTitle => 'Event Title';

  @override
  String get eventDescription => 'Event Description';

  @override
  String get eventDate => 'Event Date';

  @override
  String get eventTime => 'Event Time';

  @override
  String get eventType => 'Event Type';

  @override
  String get eventLocation => 'Event Location';

  @override
  String get allDay => 'All Day';

  @override
  String get recurring => 'Recurring';

  @override
  String get attendees => 'Attendees';

  @override
  String get meeting => 'Meeting';

  @override
  String get leave => 'Leave';

  @override
  String get holiday => 'Holiday';

  @override
  String get training => 'Training';

  @override
  String get event => 'Event';

  @override
  String get searchEvents => 'Search Events';

  @override
  String get noEventsFound => 'No events found';

  @override
  String get loadingEvents => 'Loading events...';

  @override
  String get addingEvent => 'Adding event...';

  @override
  String get updatingEvent => 'Updating event...';

  @override
  String get deletingEvent => 'Deleting event...';

  @override
  String get eventAddedSuccessfully => 'Event added successfully';

  @override
  String get eventUpdatedSuccessfully => 'Event updated successfully';

  @override
  String get eventDeletedSuccessfully => 'Event deleted successfully';

  @override
  String get failedToAddEvent => 'Failed to add event';

  @override
  String get failedToUpdateEvent => 'Failed to update event';

  @override
  String get failedToDeleteEvent => 'Failed to delete event';

  @override
  String get confirmDeleteEvent =>
      'Are you sure you want to delete this event?';

  @override
  String confirmDeleteEvents(int count) {
    return 'Are you sure you want to delete $count events?';
  }

  @override
  String get selectEventType => 'Select Event Type';

  @override
  String get selectDate => 'Select date';

  @override
  String get selectTime => 'Select Time';

  @override
  String get edit => 'Edit';

  @override
  String get delete => 'Delete';

  @override
  String get refresh => 'Refresh';

  @override
  String get sync => 'Sync';

  @override
  String get export => 'Export';

  @override
  String get import => 'Import';

  @override
  String get filter => 'Filter';

  @override
  String get clear => 'Clear';

  @override
  String get apply => 'Apply';

  @override
  String get close => 'Close';

  @override
  String get sessionDetails => 'Session Details';

  @override
  String sessionNumber(int number) {
    return 'Session $number';
  }

  @override
  String multipleSessionsDetails(int count) {
    return 'Multiple Sessions Details ($count)';
  }

  @override
  String get sessionCompleted => 'Completed';

  @override
  String get sessionInProgress => 'In Progress';

  @override
  String get location => 'Location';

  @override
  String get unknownLocation => 'Unknown location';

  @override
  String get statusOnTime => 'On Time';

  @override
  String get statusLate => 'Late';

  @override
  String get statusAbsent => 'Absent';

  @override
  String get statusOnLeave => 'On Leave';

  @override
  String get statusWeekend => 'Weekend';

  @override
  String get statusNoRecord => 'No Record';

  @override
  String get sunday => 'Sunday';

  @override
  String get monday => 'Monday';

  @override
  String get tuesday => 'Tuesday';

  @override
  String get wednesday => 'Wednesday';

  @override
  String get thursday => 'Thursday';

  @override
  String get friday => 'Friday';

  @override
  String get saturday => 'Saturday';

  @override
  String get january => 'January';

  @override
  String get february => 'February';

  @override
  String get march => 'March';

  @override
  String get april => 'April';

  @override
  String get may => 'May';

  @override
  String get june => 'June';

  @override
  String get july => 'July';

  @override
  String get august => 'August';

  @override
  String get september => 'September';

  @override
  String get october => 'October';

  @override
  String get november => 'November';

  @override
  String get december => 'December';

  @override
  String get createOvertimeRequest => 'Create Overtime Request';

  @override
  String get overtimeDate => 'Overtime Date';

  @override
  String get approver => 'Approver';

  @override
  String get pleaseSelectDate => 'Please select date';

  @override
  String get pleaseSelectStartTime => 'Please select start time';

  @override
  String get pleaseSelectEndTime => 'Please select end time';

  @override
  String get pleaseEnterReason => 'Please enter reason';

  @override
  String get pleaseSelectApprover => 'Please select approver';

  @override
  String get overtimeRequestSubmitted =>
      'Overtime request submitted successfully';

  @override
  String get failedToSubmitRequest => 'Failed to submit request';

  @override
  String get loadingOvertimeRequests => 'Loading overtime requests...';

  @override
  String get overtimeRequestDetails => 'Overtime Request Details';

  @override
  String get requestDate => 'Request Date';

  @override
  String get hours => 'hours';

  @override
  String get minutes => 'minutes';

  @override
  String get pendingApproval => 'Pending approval';

  @override
  String get submittedOn => 'Submitted on';

  @override
  String get fillDetailsToSubmit =>
      'Fill in the details below to submit your overtime request';

  @override
  String get leaveHistory => 'Leave History';

  @override
  String get leaveRequest => 'Leave Request';

  @override
  String get leaveRequestDetails => 'Leave Request Details';

  @override
  String get myLeave => 'My Leave';

  @override
  String get submitLeaveRequest => 'Submit Leave Request';

  @override
  String get fillLeaveDetailsToSubmit =>
      'Fill in the details below to submit your leave request';

  @override
  String get remainingLeaveDays => 'Remaining Leave Days';

  @override
  String outOfDays(int totalDays) {
    return 'out of $totalDays days';
  }

  @override
  String get used => 'Used';

  @override
  String get cancelled => 'Cancelled';

  @override
  String get days => 'days';

  @override
  String get day => 'day';

  @override
  String get leavePolicy => 'Leave Policy';

  @override
  String get recentLeaveRequests => 'Recent Leave Requests';

  @override
  String get viewAllLeaveHistory => 'View All History';

  @override
  String get noLeaveRequestsYet => 'No leave requests yet';

  @override
  String noFilteredRequests(String status) {
    return 'No $status requests';
  }

  @override
  String get yourLeaveRequestsWillAppearHere =>
      'Your leave requests will appear here';

  @override
  String get showAll => 'Show All';

  @override
  String get editRequest => 'Edit Request';

  @override
  String get cancelRequest => 'Cancel Request';

  @override
  String get requestDetails => 'Request Details';

  @override
  String get leaveType => 'Leave Type';

  @override
  String get startDate => 'Start Date';

  @override
  String get endDate => 'End Date';

  @override
  String get status => 'Status';

  @override
  String get approvedOn => 'Approved on';

  @override
  String get rejectedOn => 'Rejected on';

  @override
  String get cancelRequestConfirm =>
      'Are you sure you want to cancel this leave request?';

  @override
  String get no => 'No';

  @override
  String get yes => 'Yes';

  @override
  String get requestCancelled => 'Request cancelled';

  @override
  String get editFunctionalityComingSoon => 'Edit functionality coming soon';

  @override
  String get annualLeave => 'Annual Leave';

  @override
  String get sickLeave => 'Sick Leave';

  @override
  String get personalLeave => 'Personal Leave';

  @override
  String get maternityLeave => 'Maternity Leave';

  @override
  String get unpaidLeave => 'Unpaid Leave';

  @override
  String get annualLeaveDescription =>
      'Vacation, holiday, or personal time off';

  @override
  String get sickLeaveDescription => 'Medical appointments or illness';

  @override
  String get personalLeaveDescription => 'Personal matters or emergencies';

  @override
  String get maternityLeaveDescription => 'Maternity or paternity leave';

  @override
  String get unpaidLeaveDescription => 'Extended leave without pay';

  @override
  String get dateRange => 'Date Range';

  @override
  String durationInfo(int duration, String durationUnit) {
    return 'Duration: $duration $durationUnit';
  }

  @override
  String get reasonForLeave => 'Reason for Leave';

  @override
  String get leaveReasonHint =>
      'Please provide a detailed reason for your leave request...\n\nExample:\n• Family vacation\n• Medical appointment\n• Personal emergency';

  @override
  String get pleaseProvideReason => 'Please provide a reason for your leave';

  @override
  String get pleaseFillAllFields => 'Please fill in all required fields';

  @override
  String get pleaseSelectStartDate => 'Please select a start date';

  @override
  String get pleaseSelectEndDate => 'Please select an end date';

  @override
  String get leaveRequestSubmittedSuccess =>
      'Leave request submitted successfully!';

  @override
  String failedToLoadLeaveData(String error) {
    return 'Failed to load leave data: $error';
  }

  @override
  String failedToSubmitLeaveRequest(String error) {
    return 'Failed to submit leave request: $error';
  }

  @override
  String get adminUserManagement => 'User Management';

  @override
  String get adminUsers => 'Users';

  @override
  String get adminStatistics => 'Statistics';

  @override
  String get adminSettings => 'Settings';

  @override
  String get addNewUser => 'Add New User';

  @override
  String get createNewUserAccount => 'Create a new user account';

  @override
  String get createUser => 'Create User';

  @override
  String get editUser => 'Edit User';

  @override
  String get editUserDetails => 'Edit User Details';

  @override
  String get userDetails => 'User Details';

  @override
  String get firstName => 'First Name';

  @override
  String get lastName => 'Last Name';

  @override
  String get email => 'Email';

  @override
  String get password => 'Password';

  @override
  String get confirmPassword => 'Confirm Password';

  @override
  String get phoneNumber => 'Phone Number';

  @override
  String get role => 'Role';

  @override
  String get department => 'Department';

  @override
  String get position => 'Position';

  @override
  String get organization => 'Organization';

  @override
  String get active => 'Active';

  @override
  String get inactive => 'Inactive';

  @override
  String get disabled => 'Disabled';

  @override
  String get deleted => 'Deleted';

  @override
  String get enabled => 'Enabled';

  @override
  String get roleAdmin => 'Administrator';

  @override
  String get roleManager => 'Manager';

  @override
  String get roleHR => 'Human Resources';

  @override
  String get roleEmployee => 'Employee';

  @override
  String get roleUser => 'User';

  @override
  String get departmentIT => 'Information Technology';

  @override
  String get departmentHR => 'Human Resources';

  @override
  String get departmentFinance => 'Finance';

  @override
  String get departmentMarketing => 'Marketing';

  @override
  String get departmentOperations => 'Operations';

  @override
  String get departmentSales => 'Sales';

  @override
  String get departmentSupport => 'Support';

  @override
  String get searchUsers => 'Search users...';

  @override
  String get filterAndSort => 'Filter & Sort';

  @override
  String get bulkActions => 'Bulk Actions';

  @override
  String get selectAll => 'Select All';

  @override
  String get deselectAll => 'Deselect All';

  @override
  String get clearSelection => 'Clear Selection';

  @override
  String usersSelected(int count) {
    return '$count users selected';
  }

  @override
  String get allRoles => 'All Roles';

  @override
  String get allDepartments => 'All Departments';

  @override
  String get sortBy => 'Sort By';

  @override
  String get sortByName => 'Name';

  @override
  String get sortByEmail => 'Email';

  @override
  String get sortByRole => 'Role';

  @override
  String get sortByDepartment => 'Department';

  @override
  String get sortByCreatedDate => 'Created Date';

  @override
  String get sortByLastLogin => 'Last Login';

  @override
  String get sortAscending => 'Ascending';

  @override
  String get sortDescending => 'Descending';

  @override
  String get viewUser => 'View User';

  @override
  String get editUserAction => 'Edit User';

  @override
  String get deleteUser => 'Delete User';

  @override
  String get restoreUser => 'Restore User';

  @override
  String get disableUser => 'Disable User';

  @override
  String get enableUser => 'Enable User';

  @override
  String get resetPassword => 'Reset Password';

  @override
  String get toggleUserStatus => 'Toggle Status';

  @override
  String get confirmDeleteUser => 'Are you sure you want to delete this user?';

  @override
  String get confirmRestoreUser =>
      'Are you sure you want to restore this user?';

  @override
  String get confirmDisableUser =>
      'Are you sure you want to disable this user?';

  @override
  String get confirmEnableUser => 'Are you sure you want to enable this user?';

  @override
  String get confirmResetPassword =>
      'Are you sure you want to reset this user\'s password?';

  @override
  String get bulkDelete => 'Bulk Delete';

  @override
  String get bulkRestore => 'Bulk Restore';

  @override
  String get bulkDisable => 'Bulk Disable';

  @override
  String get bulkEnable => 'Bulk Enable';

  @override
  String confirmBulkDelete(int count) {
    return 'Are you sure you want to delete $count users?';
  }

  @override
  String confirmBulkRestore(int count) {
    return 'Are you sure you want to restore $count users?';
  }

  @override
  String get userOverview => 'User Overview';

  @override
  String get totalUsers => 'Total Users';

  @override
  String get activeUsers => 'Active Users';

  @override
  String get disabledUsers => 'Disabled Users';

  @override
  String get deletedUsers => 'Deleted Users';

  @override
  String get roleDistribution => 'Role Distribution';

  @override
  String get analytics => 'Analytics';

  @override
  String get userGrowth => 'User Growth';

  @override
  String get userActivity => 'User Activity';

  @override
  String get departmentBreakdown => 'Department Breakdown';

  @override
  String get userCreatedSuccessfully => 'User created successfully!';

  @override
  String get userUpdatedSuccessfully => 'User updated successfully!';

  @override
  String get userDeletedSuccessfully => 'User deleted successfully!';

  @override
  String get userRestoredSuccessfully => 'User restored successfully!';

  @override
  String get userDisabledSuccessfully => 'User disabled successfully!';

  @override
  String get userEnabledSuccessfully => 'User enabled successfully!';

  @override
  String failedToCreateUser(String error) {
    return 'Failed to create user: $error';
  }

  @override
  String failedToUpdateUser(String error) {
    return 'Failed to update user: $error';
  }

  @override
  String failedToDeleteUser(String error) {
    return 'Failed to delete user: $error';
  }

  @override
  String failedToLoadUsers(String error) {
    return 'Failed to load users: $error';
  }

  @override
  String get noUsersFound => 'No users found';

  @override
  String get loadingUsers => 'Loading users...';

  @override
  String get refreshUsers => 'Refresh Users';

  @override
  String get addUser => 'Add User';

  @override
  String get firstNameRequired => 'First name is required';

  @override
  String get lastNameRequired => 'Last name is required';

  @override
  String get emailRequired => 'Email is required';

  @override
  String get emailInvalid => 'Please enter a valid email address';

  @override
  String get passwordRequired => 'Password is required';

  @override
  String get passwordTooShort => 'Password must be at least 8 characters';

  @override
  String get passwordTooWeak =>
      'Password must contain uppercase, lowercase, number and special character';

  @override
  String get phoneInvalid => 'Please enter a valid phone number';

  @override
  String get roleRequired => 'Role is required';

  @override
  String get departmentRequired => 'Department is required';

  @override
  String get requiredField => 'Required field';

  @override
  String get optionalField => 'Optional field';

  @override
  String get enterFirstName => 'Enter first name';

  @override
  String get enterLastName => 'Enter last name';

  @override
  String get enterEmail => 'Enter email address';

  @override
  String get enterPassword => 'Enter password';

  @override
  String get enterPhoneNumber => 'Enter phone number';

  @override
  String get selectRole => 'Select role';

  @override
  String get selectDepartment => 'Select department';

  @override
  String get enterPosition => 'Enter position';

  @override
  String get enterOrganization => 'Enter organization';

  @override
  String get personalInformation => 'Personal Information';

  @override
  String get workInformation => 'Work Information';

  @override
  String get roleAndStatus => 'Role & Status';

  @override
  String get saveChanges => 'Save Changes';

  @override
  String get roleManagement => 'Role Management';

  @override
  String get departmentManagement => 'Department Management';

  @override
  String get createNewRole => 'Create New Role';

  @override
  String get editRole => 'Edit Role';

  @override
  String get createNewDepartment => 'Create New Department';

  @override
  String get editDepartment => 'Edit Department';

  @override
  String get roleName => 'Role Name';

  @override
  String get departmentName => 'Department Name';

  @override
  String get departmentCode => 'Department Code';

  @override
  String get departmentDescription => 'Department Description';

  @override
  String get parentDepartment => 'Parent Department';

  @override
  String get noParentTopLevel => 'No Parent (Top Level)';

  @override
  String get selectParentDepartment => 'Select parent department (optional)';

  @override
  String get noDepartment => 'No Department';

  @override
  String get basicInformation => 'Basic Information';

  @override
  String get hierarchy => 'Hierarchy';

  @override
  String get departmentStatus => 'Department Status';

  @override
  String get enable => 'Enable';

  @override
  String get disable => 'Disable';

  @override
  String get restore => 'Restore';

  @override
  String get deleteRole => 'Delete Role';

  @override
  String get deleteDepartment => 'Delete Department';

  @override
  String deleteRoleConfirm(Object roleName) {
    return 'Are you sure you want to delete the role \"$roleName\"?';
  }

  @override
  String deleteDepartmentConfirm(Object departmentName) {
    return 'Are you sure you want to delete the department \"$departmentName\"?';
  }

  @override
  String get roleCreatedSuccessfully => 'Role created successfully';

  @override
  String get roleUpdatedSuccessfully => 'Role updated successfully';

  @override
  String get roleDeletedSuccessfully => 'Role deleted successfully';

  @override
  String get departmentCreatedSuccessfully => 'Department created successfully';

  @override
  String get departmentUpdatedSuccessfully => 'Department updated successfully';

  @override
  String get departmentDeletedSuccessfully => 'Department deleted successfully';

  @override
  String get departmentRestoredSuccessfully =>
      'Department restored successfully';

  @override
  String get departmentStatusUpdatedSuccessfully =>
      'Department status updated successfully';

  @override
  String get noRolesFound => 'No roles found';

  @override
  String get noDepartmentsFound => 'No departments found';

  @override
  String get createFirstRole => 'Create your first role to get started';

  @override
  String get createFirstDepartment =>
      'Create your first department to get started';

  @override
  String get addRole => 'Add Role';

  @override
  String get addDepartment => 'Add Department';

  @override
  String get updateRole => 'Update Role';

  @override
  String get updateDepartment => 'Update Department';

  @override
  String get createRole => 'Create Role';

  @override
  String get createDepartment => 'Create Department';

  @override
  String get searchRoles => 'Search roles...';

  @override
  String get searchDepartments => 'Search departments...';

  @override
  String get includeDeleted => 'Include Deleted';

  @override
  String get roleInformation => 'Role Information';

  @override
  String get roleDetails => 'Role Details';

  @override
  String get created => 'Created';

  @override
  String get lastUpdated => 'Last Updated';

  @override
  String get roleId => 'Role ID';

  @override
  String get departmentId => 'Department ID';

  @override
  String get enterRoleName => 'Enter role name (e.g., Manager, HR, Developer)';

  @override
  String get enterDepartmentName =>
      'Enter department name (e.g., Human Resources, IT)';

  @override
  String get enterDepartmentCode => 'Enter department code (e.g., HR, IT, FIN)';

  @override
  String get enterDepartmentDescription =>
      'Enter department description (optional)';

  @override
  String get roleNameRequired => 'Role name is required';

  @override
  String get departmentNameRequired => 'Department name is required';

  @override
  String get roleNameMinLength => 'Role name must be at least 2 characters';

  @override
  String get departmentNameMinLength =>
      'Department name must be at least 2 characters';

  @override
  String get roleNameMaxLength => 'Role name must be less than 50 characters';

  @override
  String get departmentNameMaxLength =>
      'Department name must be less than 100 characters';

  @override
  String get departmentCodeMinLength =>
      'Department code must be at least 2 characters';

  @override
  String get departmentCodeMaxLength =>
      'Department code must be less than 10 characters';

  @override
  String get roleNameInvalidCharacters =>
      'Role name contains invalid characters';

  @override
  String get departmentCodeInvalidCharacters =>
      'Department code contains invalid characters';

  @override
  String get roleNameAlreadyExists => 'Role name already exists';

  @override
  String get departmentNameAlreadyExists => 'Department name already exists';

  @override
  String get departmentCodeAlreadyExists => 'Department code already exists';

  @override
  String get changingRoleNameWarning =>
      'Changing the role name may affect users assigned to this role.';

  @override
  String get roleAndDepartmentInfo =>
      'Role names and codes should be unique. They will be used throughout the system for access control.';

  @override
  String get departmentHierarchyInfo =>
      'Department names and codes should be unique. You can create a hierarchy by selecting a parent department.';
}
