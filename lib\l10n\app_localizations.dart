import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:intl/intl.dart' as intl;

import 'app_localizations_en.dart';
import 'app_localizations_vi.dart';

// ignore_for_file: type=lint

/// Callers can lookup localized strings with an instance of AppLocalizations
/// returned by `AppLocalizations.of(context)`.
///
/// Applications need to include `AppLocalizations.delegate()` in their app's
/// `localizationDelegates` list, and the locales they support in the app's
/// `supportedLocales` list. For example:
///
/// ```dart
/// import 'l10n/app_localizations.dart';
///
/// return MaterialApp(
///   localizationsDelegates: AppLocalizations.localizationsDelegates,
///   supportedLocales: AppLocalizations.supportedLocales,
///   home: MyApplicationHome(),
/// );
/// ```
///
/// ## Update pubspec.yaml
///
/// Please make sure to update your pubspec.yaml to include the following
/// packages:
///
/// ```yaml
/// dependencies:
///   # Internationalization support.
///   flutter_localizations:
///     sdk: flutter
///   intl: any # Use the pinned version from flutter_localizations
///
///   # Rest of dependencies
/// ```
///
/// ## iOS Applications
///
/// iOS applications define key application metadata, including supported
/// locales, in an Info.plist file that is built into the application bundle.
/// To configure the locales supported by your app, you’ll need to edit this
/// file.
///
/// First, open your project’s ios/Runner.xcworkspace Xcode workspace file.
/// Then, in the Project Navigator, open the Info.plist file under the Runner
/// project’s Runner folder.
///
/// Next, select the Information Property List item, select Add Item from the
/// Editor menu, then select Localizations from the pop-up menu.
///
/// Select and expand the newly-created Localizations item then, for each
/// locale your application supports, add a new item and select the locale
/// you wish to add from the pop-up menu in the Value field. This list should
/// be consistent with the languages listed in the AppLocalizations.supportedLocales
/// property.
abstract class AppLocalizations {
  AppLocalizations(String locale)
    : localeName = intl.Intl.canonicalizedLocale(locale.toString());

  final String localeName;

  static AppLocalizations? of(BuildContext context) {
    return Localizations.of<AppLocalizations>(context, AppLocalizations);
  }

  static const LocalizationsDelegate<AppLocalizations> delegate =
      _AppLocalizationsDelegate();

  /// A list of this localizations delegate along with the default localizations
  /// delegates.
  ///
  /// Returns a list of localizations delegates containing this delegate along with
  /// GlobalMaterialLocalizations.delegate, GlobalCupertinoLocalizations.delegate,
  /// and GlobalWidgetsLocalizations.delegate.
  ///
  /// Additional delegates can be added by appending to this list in
  /// MaterialApp. This list does not have to be used at all if a custom list
  /// of delegates is preferred or required.
  static const List<LocalizationsDelegate<dynamic>> localizationsDelegates =
      <LocalizationsDelegate<dynamic>>[
        delegate,
        GlobalMaterialLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
      ];

  /// A list of this localizations delegate's supported locales.
  static const List<Locale> supportedLocales = <Locale>[
    Locale('en'),
    Locale('vi'),
  ];

  /// No description provided for @acceptTerms.
  ///
  /// In en, this message translates to:
  /// **'I accept the terms and conditions'**
  String get acceptTerms;

  /// No description provided for @authForgotPassword.
  ///
  /// In en, this message translates to:
  /// **'Forgot Password'**
  String get authForgotPassword;

  /// No description provided for @authNext.
  ///
  /// In en, this message translates to:
  /// **'Next'**
  String get authNext;

  /// No description provided for @authResetPassword.
  ///
  /// In en, this message translates to:
  /// **'Reset Password'**
  String get authResetPassword;

  /// No description provided for @authSubTitleForgotPassword.
  ///
  /// In en, this message translates to:
  /// **'Enter your email to reset your password.'**
  String get authSubTitleForgotPassword;

  /// No description provided for @authSubTitleResetPassword.
  ///
  /// In en, this message translates to:
  /// **'Enter your new password.'**
  String get authSubTitleResetPassword;

  /// No description provided for @authSubTitleVerifyOtp.
  ///
  /// In en, this message translates to:
  /// **'Enter the 4-digit code sent to your email.'**
  String get authSubTitleVerifyOtp;

  /// No description provided for @authTitleResetPassword.
  ///
  /// In en, this message translates to:
  /// **'Reset Password'**
  String get authTitleResetPassword;

  /// No description provided for @authVerifyOtp.
  ///
  /// In en, this message translates to:
  /// **'Verify OTP'**
  String get authVerifyOtp;

  /// No description provided for @changePassword.
  ///
  /// In en, this message translates to:
  /// **'Change Password'**
  String get changePassword;

  /// No description provided for @contactInfo.
  ///
  /// In en, this message translates to:
  /// **'Contact Information'**
  String get contactInfo;

  /// No description provided for @detectFaceAnalyzingSecurity.
  ///
  /// In en, this message translates to:
  /// **'Analyzing security...'**
  String get detectFaceAnalyzingSecurity;

  /// No description provided for @detectFaceAttendanceCompleted.
  ///
  /// In en, this message translates to:
  /// **'Attendance completed'**
  String get detectFaceAttendanceCompleted;

  /// No description provided for @detectFaceCaptureImage.
  ///
  /// In en, this message translates to:
  /// **'Capture Image'**
  String get detectFaceCaptureImage;

  /// No description provided for @detectFaceCaptureToStart.
  ///
  /// In en, this message translates to:
  /// **'Capture photo to start'**
  String get detectFaceCaptureToStart;

  /// No description provided for @detectFaceCheckIn.
  ///
  /// In en, this message translates to:
  /// **'Check-In'**
  String get detectFaceCheckIn;

  /// No description provided for @detectFaceCheckInError.
  ///
  /// In en, this message translates to:
  /// **'Check-in failed: {error}'**
  String detectFaceCheckInError(Object error);

  /// No description provided for @detectFaceCheckInSuccess.
  ///
  /// In en, this message translates to:
  /// **'Check-in successful'**
  String get detectFaceCheckInSuccess;

  /// No description provided for @detectFaceCheckOut.
  ///
  /// In en, this message translates to:
  /// **'Check-Out'**
  String get detectFaceCheckOut;

  /// No description provided for @detectFaceCheckOutError.
  ///
  /// In en, this message translates to:
  /// **'Face CheckOut Error {error}'**
  String detectFaceCheckOutError(Object error);

  /// No description provided for @detectFaceCheckOutSuccess.
  ///
  /// In en, this message translates to:
  /// **'Check-out successful'**
  String get detectFaceCheckOutSuccess;

  /// No description provided for @detectFaceCurrentLocation.
  ///
  /// In en, this message translates to:
  /// **'Current location'**
  String get detectFaceCurrentLocation;

  /// No description provided for @detectFaceFaceAnalysis.
  ///
  /// In en, this message translates to:
  /// **'Face analysis'**
  String get detectFaceFaceAnalysis;

  /// No description provided for @detectFaceFaceAnalysisDescription.
  ///
  /// In en, this message translates to:
  /// **'Use ML to detect facial features and ensure eyes are open.'**
  String get detectFaceFaceAnalysisDescription;

  /// No description provided for @detectFaceFaceNotFound.
  ///
  /// In en, this message translates to:
  /// **'No face found in the image.'**
  String get detectFaceFaceNotFound;

  /// No description provided for @detectFaceFacesDetected.
  ///
  /// In en, this message translates to:
  /// **'Detected {count} face(s)'**
  String detectFaceFacesDetected(Object count);

  /// No description provided for @detectFaceGettingLocation.
  ///
  /// In en, this message translates to:
  /// **'Getting location...'**
  String get detectFaceGettingLocation;

  /// No description provided for @detectFaceImageCapturedReady.
  ///
  /// In en, this message translates to:
  /// **'Image captured. Ready to check in.'**
  String get detectFaceImageCapturedReady;

  /// No description provided for @detectFaceImageNotCaptured.
  ///
  /// In en, this message translates to:
  /// **'Image capture canceled.'**
  String get detectFaceImageNotCaptured;

  /// No description provided for @detectFaceLocationNotAvailable.
  ///
  /// In en, this message translates to:
  /// **'Face Location Not Available'**
  String get detectFaceLocationNotAvailable;

  /// No description provided for @detectFaceLocationPermissionDenied.
  ///
  /// In en, this message translates to:
  /// **'Location access denied.'**
  String get detectFaceLocationPermissionDenied;

  /// No description provided for @detectFaceLocationPermissionPermanentlyDenied.
  ///
  /// In en, this message translates to:
  /// **'Location access permanently denied.'**
  String get detectFaceLocationPermissionPermanentlyDenied;

  /// No description provided for @detectFaceLocationVerification.
  ///
  /// In en, this message translates to:
  /// **'Location verification'**
  String get detectFaceLocationVerification;

  /// No description provided for @detectFaceLocationVerificationDescription.
  ///
  /// In en, this message translates to:
  /// **'Ensure check-in location is within the company area.'**
  String get detectFaceLocationVerificationDescription;

  /// No description provided for @detectFaceOpeningCamera.
  ///
  /// In en, this message translates to:
  /// **'Opening camera...'**
  String get detectFaceOpeningCamera;

  /// No description provided for @detectFaceProcessingCheckIn.
  ///
  /// In en, this message translates to:
  /// **'Processing check-in...'**
  String get detectFaceProcessingCheckIn;

  /// No description provided for @detectFaceProcessingCheckOut.
  ///
  /// In en, this message translates to:
  /// **'Processing check-out...'**
  String get detectFaceProcessingCheckOut;

  /// No description provided for @detectFaceReadyForCheckIn.
  ///
  /// In en, this message translates to:
  /// **'Ready for check-in'**
  String get detectFaceReadyForCheckIn;

  /// No description provided for @detectFaceReadyForCheckOut.
  ///
  /// In en, this message translates to:
  /// **'Ready for check-out'**
  String get detectFaceReadyForCheckOut;

  /// No description provided for @detectFaceRetakeImage.
  ///
  /// In en, this message translates to:
  /// **'Retake'**
  String get detectFaceRetakeImage;

  /// No description provided for @detectFaceSecureCheckInSystem.
  ///
  /// In en, this message translates to:
  /// **'Secure Check-In System'**
  String get detectFaceSecureCheckInSystem;

  /// No description provided for @detectFaceSecureCheckInTitle.
  ///
  /// In en, this message translates to:
  /// **'Secure Check-In'**
  String get detectFaceSecureCheckInTitle;

  /// No description provided for @detectFaceSecurityInfoNotice.
  ///
  /// In en, this message translates to:
  /// **'The system uses multiple layers of analysis to ensure transparency and fairness'**
  String get detectFaceSecurityInfoNotice;

  /// No description provided for @detectFaceSecurityInfoTitle.
  ///
  /// In en, this message translates to:
  /// **'Security Information'**
  String get detectFaceSecurityInfoTitle;

  /// No description provided for @detectFaceSecurityInfoTooltip.
  ///
  /// In en, this message translates to:
  /// **'Security information'**
  String get detectFaceSecurityInfoTooltip;

  /// No description provided for @detectFaceSystemStatus.
  ///
  /// In en, this message translates to:
  /// **'SYSTEM STATUS'**
  String get detectFaceSystemStatus;

  /// No description provided for @detectFaceUnderstood.
  ///
  /// In en, this message translates to:
  /// **'Understood'**
  String get detectFaceUnderstood;

  /// No description provided for @detectFaceSystemWillCheck.
  ///
  /// In en, this message translates to:
  /// **'System will verify the authenticity of the image'**
  String get detectFaceSystemWillCheck;

  /// No description provided for @detectFaceUpdateLocationTooltip.
  ///
  /// In en, this message translates to:
  /// **'Update location'**
  String get detectFaceUpdateLocationTooltip;

  /// No description provided for @editProfile.
  ///
  /// In en, this message translates to:
  /// **'Edit Profile'**
  String get editProfile;

  /// No description provided for @error.
  ///
  /// In en, this message translates to:
  /// **'Error'**
  String get error;

  /// No description provided for @home.
  ///
  /// In en, this message translates to:
  /// **'Home'**
  String get home;

  /// No description provided for @homeErrorFunction.
  ///
  /// In en, this message translates to:
  /// **'Error configuring function'**
  String get homeErrorFunction;

  /// No description provided for @homeFaceRecognition.
  ///
  /// In en, this message translates to:
  /// **'Face Recognition'**
  String get homeFaceRecognition;

  /// No description provided for @homeGoodAfternoon.
  ///
  /// In en, this message translates to:
  /// **'Good Afternoon'**
  String get homeGoodAfternoon;

  /// No description provided for @homeGoodEvening.
  ///
  /// In en, this message translates to:
  /// **'Good Evening'**
  String get homeGoodEvening;

  /// No description provided for @homeGoodMorning.
  ///
  /// In en, this message translates to:
  /// **'Good Morning'**
  String get homeGoodMorning;

  /// No description provided for @homeLeave.
  ///
  /// In en, this message translates to:
  /// **'Leave'**
  String get homeLeave;

  /// No description provided for @homeNotificationAndUpdate.
  ///
  /// In en, this message translates to:
  /// **'Notification & Update'**
  String get homeNotificationAndUpdate;

  /// No description provided for @homeOvertime.
  ///
  /// In en, this message translates to:
  /// **'Overtime Hours'**
  String get homeOvertime;

  /// No description provided for @homeTodayAttendance.
  ///
  /// In en, this message translates to:
  /// **'Today\'s Attendance'**
  String get homeTodayAttendance;

  /// No description provided for @homeWorkHours.
  ///
  /// In en, this message translates to:
  /// **'Work Hours'**
  String get homeWorkHours;

  /// No description provided for @loginEmail.
  ///
  /// In en, this message translates to:
  /// **'Email'**
  String get loginEmail;

  /// No description provided for @loginForgotPassword.
  ///
  /// In en, this message translates to:
  /// **'Forgot password?'**
  String get loginForgotPassword;

  /// No description provided for @loginHintEmail.
  ///
  /// In en, this message translates to:
  /// **'Enter your email'**
  String get loginHintEmail;

  /// No description provided for @loginHintPassword.
  ///
  /// In en, this message translates to:
  /// **'Enter your password'**
  String get loginHintPassword;

  /// No description provided for @loginNoAccount.
  ///
  /// In en, this message translates to:
  /// **'Don\'t have an account?'**
  String get loginNoAccount;

  /// No description provided for @loginPassword.
  ///
  /// In en, this message translates to:
  /// **'Password'**
  String get loginPassword;

  /// No description provided for @loginPasswordMinLength.
  ///
  /// In en, this message translates to:
  /// **'Password must be at least 6 characters'**
  String get loginPasswordMinLength;

  /// No description provided for @loginPleaseEnterEmail.
  ///
  /// In en, this message translates to:
  /// **'Please enter your email'**
  String get loginPleaseEnterEmail;

  /// No description provided for @loginPleaseEnterFullName.
  ///
  /// In en, this message translates to:
  /// **'Please enter your full name'**
  String get loginPleaseEnterFullName;

  /// No description provided for @loginPleaseEnterPassword.
  ///
  /// In en, this message translates to:
  /// **'Please enter your password'**
  String get loginPleaseEnterPassword;

  /// No description provided for @loginRememberMe.
  ///
  /// In en, this message translates to:
  /// **'Remember me'**
  String get loginRememberMe;

  /// No description provided for @loginSignIn.
  ///
  /// In en, this message translates to:
  /// **'Sign in'**
  String get loginSignIn;

  /// No description provided for @loginSignUp.
  ///
  /// In en, this message translates to:
  /// **'Sign up'**
  String get loginSignUp;

  /// No description provided for @loginSubtitle.
  ///
  /// In en, this message translates to:
  /// **'We\'re happy to see you again. Log in to continue.'**
  String get loginSubtitle;

  /// No description provided for @loginSuccess.
  ///
  /// In en, this message translates to:
  /// **'Login Successfully'**
  String get loginSuccess;

  /// No description provided for @loginTitle.
  ///
  /// In en, this message translates to:
  /// **'Welcome back!'**
  String get loginTitle;

  /// No description provided for @logout.
  ///
  /// In en, this message translates to:
  /// **'Logout'**
  String get logout;

  /// No description provided for @logOutSuccess.
  ///
  /// In en, this message translates to:
  /// **'Login Successfully'**
  String get logOutSuccess;

  /// No description provided for @moreAllFeatures.
  ///
  /// In en, this message translates to:
  /// **'All Features'**
  String get moreAllFeatures;

  /// No description provided for @moreAttendance.
  ///
  /// In en, this message translates to:
  /// **'Attendance'**
  String get moreAttendance;

  /// No description provided for @moreCalendar.
  ///
  /// In en, this message translates to:
  /// **'Calendar'**
  String get moreCalendar;

  /// No description provided for @moreCRM.
  ///
  /// In en, this message translates to:
  /// **'CRM Customers'**
  String get moreCRM;

  /// No description provided for @moreHRM.
  ///
  /// In en, this message translates to:
  /// **'HRM Management'**
  String get moreHRM;

  /// No description provided for @moreLeave.
  ///
  /// In en, this message translates to:
  /// **'Leave'**
  String get moreLeave;

  /// No description provided for @moreOverTime.
  ///
  /// In en, this message translates to:
  /// **'Overtime'**
  String get moreOverTime;

  /// No description provided for @moreQR.
  ///
  /// In en, this message translates to:
  /// **'QR Scan'**
  String get moreQR;

  /// No description provided for @moreSetting.
  ///
  /// In en, this message translates to:
  /// **'Setting'**
  String get moreSetting;

  /// No description provided for @moreSupport.
  ///
  /// In en, this message translates to:
  /// **'Support'**
  String get moreSupport;

  /// No description provided for @moreTeam.
  ///
  /// In en, this message translates to:
  /// **'Team'**
  String get moreTeam;

  /// No description provided for @moreTraining.
  ///
  /// In en, this message translates to:
  /// **'Training'**
  String get moreTraining;

  /// No description provided for @moreRecruitment.
  ///
  /// In en, this message translates to:
  /// **'Recruitment'**
  String get moreRecruitment;

  /// No description provided for @moreUtility.
  ///
  /// In en, this message translates to:
  /// **'Utility & Settings'**
  String get moreUtility;

  /// No description provided for @navigationBarCustomer.
  ///
  /// In en, this message translates to:
  /// **'Customer'**
  String get navigationBarCustomer;

  /// No description provided for @navigationBarHome.
  ///
  /// In en, this message translates to:
  /// **'Home'**
  String get navigationBarHome;

  /// No description provided for @navigationBarMore.
  ///
  /// In en, this message translates to:
  /// **'More'**
  String get navigationBarMore;

  /// No description provided for @navigationBarNotify.
  ///
  /// In en, this message translates to:
  /// **'Notifications'**
  String get navigationBarNotify;

  /// No description provided for @navigationBarWork.
  ///
  /// In en, this message translates to:
  /// **'Work'**
  String get navigationBarWork;

  /// No description provided for @notificationAll.
  ///
  /// In en, this message translates to:
  /// **'All'**
  String get notificationAll;

  /// No description provided for @notificationAllRead.
  ///
  /// In en, this message translates to:
  /// **'All Read'**
  String get notificationAllRead;

  /// No description provided for @notificationCategory.
  ///
  /// In en, this message translates to:
  /// **'Category'**
  String get notificationCategory;

  /// No description provided for @notificationCustomers.
  ///
  /// In en, this message translates to:
  /// **'Customers'**
  String get notificationCustomers;

  /// No description provided for @notificationDetail.
  ///
  /// In en, this message translates to:
  /// **'Notification Detail'**
  String get notificationDetail;

  /// No description provided for @notificationEmpty.
  ///
  /// In en, this message translates to:
  /// **'You have no important notifications'**
  String get notificationEmpty;

  /// No description provided for @notificationEmptyFromCustomer.
  ///
  /// In en, this message translates to:
  /// **'No notifications from customers yet'**
  String get notificationEmptyFromCustomer;

  /// No description provided for @notificationImportant.
  ///
  /// In en, this message translates to:
  /// **'Important'**
  String get notificationImportant;

  /// No description provided for @notificationLevel.
  ///
  /// In en, this message translates to:
  /// **'Level'**
  String get notificationLevel;

  /// No description provided for @notificationNoNewUpdates.
  ///
  /// In en, this message translates to:
  /// **'No new updates'**
  String get notificationNoNewUpdates;

  /// No description provided for @notificationPlaceholder.
  ///
  /// In en, this message translates to:
  /// **'All your notifications will appear here'**
  String get notificationPlaceholder;

  /// No description provided for @notificationReceivedTime.
  ///
  /// In en, this message translates to:
  /// **'Received Time'**
  String get notificationReceivedTime;

  /// No description provided for @notificationTittle.
  ///
  /// In en, this message translates to:
  /// **'Notification'**
  String get notificationTittle;

  /// No description provided for @notificationUnread.
  ///
  /// In en, this message translates to:
  /// **'UnRead'**
  String get notificationUnread;

  /// No description provided for @notificationViewAll.
  ///
  /// In en, this message translates to:
  /// **'You have viewed all notifications'**
  String get notificationViewAll;

  /// No description provided for @onTime.
  ///
  /// In en, this message translates to:
  /// **'On Time'**
  String get onTime;

  /// No description provided for @otpNotReceived.
  ///
  /// In en, this message translates to:
  /// **'OTP not received'**
  String get otpNotReceived;

  /// No description provided for @otpVerifySuccessfully.
  ///
  /// In en, this message translates to:
  /// **'OTP verified successfully!'**
  String get otpVerifySuccessfully;

  /// No description provided for @passwordResetSuccessfully.
  ///
  /// In en, this message translates to:
  /// **'Password reset successfully!'**
  String get passwordResetSuccessfully;

  /// No description provided for @pleaseEnterConfirmPassword.
  ///
  /// In en, this message translates to:
  /// **'Please enter your confirm password'**
  String get pleaseEnterConfirmPassword;

  /// No description provided for @profile.
  ///
  /// In en, this message translates to:
  /// **'Profile'**
  String get profile;

  /// No description provided for @profileDepartment.
  ///
  /// In en, this message translates to:
  /// **'Department'**
  String get profileDepartment;

  /// No description provided for @profileJobInfo.
  ///
  /// In en, this message translates to:
  /// **'Job Information'**
  String get profileJobInfo;

  /// No description provided for @profilePhone.
  ///
  /// In en, this message translates to:
  /// **'Phone Number'**
  String get profilePhone;

  /// No description provided for @profilePosition.
  ///
  /// In en, this message translates to:
  /// **'Position'**
  String get profilePosition;

  /// No description provided for @professionalInformation.
  ///
  /// In en, this message translates to:
  /// **'Professional Information'**
  String get professionalInformation;

  /// No description provided for @fullName.
  ///
  /// In en, this message translates to:
  /// **'Full Name'**
  String get fullName;

  /// No description provided for @registerConfirmPassword.
  ///
  /// In en, this message translates to:
  /// **'Confirm Password'**
  String get registerConfirmPassword;

  /// No description provided for @registerEmail.
  ///
  /// In en, this message translates to:
  /// **'Email'**
  String get registerEmail;

  /// No description provided for @registerFullName.
  ///
  /// In en, this message translates to:
  /// **'Full Name'**
  String get registerFullName;

  /// No description provided for @registerHaveAccount.
  ///
  /// In en, this message translates to:
  /// **'Already have an account?'**
  String get registerHaveAccount;

  /// No description provided for @registerPassword.
  ///
  /// In en, this message translates to:
  /// **'Password'**
  String get registerPassword;

  /// No description provided for @registerPasswordsDoNotMatch.
  ///
  /// In en, this message translates to:
  /// **'Passwords do not match'**
  String get registerPasswordsDoNotMatch;

  /// No description provided for @registerPleaseConfirmPassword.
  ///
  /// In en, this message translates to:
  /// **'Please confirm your password'**
  String get registerPleaseConfirmPassword;

  /// No description provided for @registerSubTitleSignUp.
  ///
  /// In en, this message translates to:
  /// **'Enter your details to create an account.'**
  String get registerSubTitleSignUp;

  /// No description provided for @registerSuccess.
  ///
  /// In en, this message translates to:
  /// **'Registration successful'**
  String get registerSuccess;

  /// No description provided for @registerTitleSignUp.
  ///
  /// In en, this message translates to:
  /// **'Create your account'**
  String get registerTitleSignUp;

  /// No description provided for @resendCode.
  ///
  /// In en, this message translates to:
  /// **'Resend Code'**
  String get resendCode;

  /// No description provided for @retry.
  ///
  /// In en, this message translates to:
  /// **'Retry'**
  String get retry;

  /// No description provided for @save.
  ///
  /// In en, this message translates to:
  /// **'Save'**
  String get save;

  /// No description provided for @setting.
  ///
  /// In en, this message translates to:
  /// **'Setting'**
  String get setting;

  /// No description provided for @settingAccount.
  ///
  /// In en, this message translates to:
  /// **'Account'**
  String get settingAccount;

  /// No description provided for @settingAddImage.
  ///
  /// In en, this message translates to:
  /// **'Add Image'**
  String get settingAddImage;

  /// No description provided for @settingApp.
  ///
  /// In en, this message translates to:
  /// **'App Settings'**
  String get settingApp;

  /// No description provided for @settingAutoCheckOut.
  ///
  /// In en, this message translates to:
  /// **'Auto Check-out'**
  String get settingAutoCheckOut;

  /// No description provided for @settingBiometricLogin.
  ///
  /// In en, this message translates to:
  /// **'Biometric Login'**
  String get settingBiometricLogin;

  /// No description provided for @settingCancel.
  ///
  /// In en, this message translates to:
  /// **'Cancel'**
  String get settingCancel;

  /// No description provided for @settingCheckInAndSecurity.
  ///
  /// In en, this message translates to:
  /// **'Check-in & Security'**
  String get settingCheckInAndSecurity;

  /// No description provided for @settingCheckUpDate.
  ///
  /// In en, this message translates to:
  /// **'Check for Updates'**
  String get settingCheckUpDate;

  /// No description provided for @settingChooseAnEmployeeFromList.
  ///
  /// In en, this message translates to:
  /// **'Choose an employee from the list'**
  String get settingChooseAnEmployeeFromList;

  /// No description provided for @settingChooseEmployee.
  ///
  /// In en, this message translates to:
  /// **'Choose Employee'**
  String get settingChooseEmployee;

  /// No description provided for @settingChooseImageALibrary.
  ///
  /// In en, this message translates to:
  /// **'Choose an image from the library'**
  String get settingChooseImageALibrary;

  /// No description provided for @settingConfirmLogOut.
  ///
  /// In en, this message translates to:
  /// **'Are you sure you want to log out?'**
  String get settingConfirmLogOut;

  /// No description provided for @settingConfirmPassword.
  ///
  /// In en, this message translates to:
  /// **'Confirm Password'**
  String get settingConfirmPassword;

  /// No description provided for @settingContactSupport.
  ///
  /// In en, this message translates to:
  /// **'Contact Support'**
  String get settingContactSupport;

  /// No description provided for @settingCurrentPassword.
  ///
  /// In en, this message translates to:
  /// **'Current Password'**
  String get settingCurrentPassword;

  /// No description provided for @settingEmailSupport.
  ///
  /// In en, this message translates to:
  /// **'Email Support'**
  String get settingEmailSupport;

  /// No description provided for @settingError.
  ///
  /// In en, this message translates to:
  /// **'An error occurred. Please try again later!'**
  String get settingError;

  /// No description provided for @settingFaceRecognition.
  ///
  /// In en, this message translates to:
  /// **'Face Recognition'**
  String get settingFaceRecognition;

  /// No description provided for @settingHelpCenter.
  ///
  /// In en, this message translates to:
  /// **'Help Center'**
  String get settingHelpCenter;

  /// No description provided for @settingIntroduce.
  ///
  /// In en, this message translates to:
  /// **'Introduce'**
  String get settingIntroduce;

  /// No description provided for @settingLanguage.
  ///
  /// In en, this message translates to:
  /// **'Language'**
  String get settingLanguage;

  /// No description provided for @settingLatestVersion.
  ///
  /// In en, this message translates to:
  /// **'Latest Version'**
  String get settingLatestVersion;

  /// No description provided for @settingMessageErrorEmail.
  ///
  /// In en, this message translates to:
  /// **'No email app found on the device.'**
  String get settingMessageErrorEmail;

  /// No description provided for @settingMessageErrorPhone.
  ///
  /// In en, this message translates to:
  /// **'Cannot open the phone app.'**
  String get settingMessageErrorPhone;

  /// No description provided for @settingMessageErrorSocial.
  ///
  /// In en, this message translates to:
  /// **'Cannot open this link.'**
  String get settingMessageErrorSocial;

  /// No description provided for @settingMessageSupport.
  ///
  /// In en, this message translates to:
  /// **'Message Support'**
  String get settingMessageSupport;

  /// No description provided for @settingNewPassword.
  ///
  /// In en, this message translates to:
  /// **'New Password'**
  String get settingNewPassword;

  /// No description provided for @settingPhoneSupport.
  ///
  /// In en, this message translates to:
  /// **'Phone Support'**
  String get settingPhoneSupport;

  /// No description provided for @settingSubChangePass.
  ///
  /// In en, this message translates to:
  /// **'Change your password'**
  String get settingSubChangePass;

  /// No description provided for @settingSubEnableAutoCheckOut.
  ///
  /// In en, this message translates to:
  /// **'Enable Auto Check-out'**
  String get settingSubEnableAutoCheckOut;

  /// No description provided for @settingSubEnableFaceRecognition.
  ///
  /// In en, this message translates to:
  /// **'Enable Face Recognition'**
  String get settingSubEnableFaceRecognition;

  /// No description provided for @settingSubGetHelp.
  ///
  /// In en, this message translates to:
  /// **'Get help and support'**
  String get settingSubGetHelp;

  /// No description provided for @settingSubTitleProfile.
  ///
  /// In en, this message translates to:
  /// **'Edit your personal information'**
  String get settingSubTitleProfile;

  /// No description provided for @settingSubUseBiometricLogin.
  ///
  /// In en, this message translates to:
  /// **'Use Biometric Login'**
  String get settingSubUseBiometricLogin;

  /// No description provided for @settingSupport.
  ///
  /// In en, this message translates to:
  /// **'Support'**
  String get settingSupport;

  /// No description provided for @settingTheme.
  ///
  /// In en, this message translates to:
  /// **'Theme'**
  String get settingTheme;

  /// No description provided for @settingTitleProfile.
  ///
  /// In en, this message translates to:
  /// **'Personal Information'**
  String get settingTitleProfile;

  /// No description provided for @settingUploadImage.
  ///
  /// In en, this message translates to:
  /// **'Upload Image'**
  String get settingUploadImage;

  /// No description provided for @settingUploadImageSuccess.
  ///
  /// In en, this message translates to:
  /// **'Upload image successfully'**
  String get settingUploadImageSuccess;

  /// No description provided for @settingVersion.
  ///
  /// In en, this message translates to:
  /// **'Version'**
  String get settingVersion;

  /// No description provided for @success.
  ///
  /// In en, this message translates to:
  /// **'Success'**
  String get success;

  /// No description provided for @thisWeek.
  ///
  /// In en, this message translates to:
  /// **'This week'**
  String get thisWeek;

  /// No description provided for @today.
  ///
  /// In en, this message translates to:
  /// **'Today'**
  String get today;

  /// No description provided for @initializing.
  ///
  /// In en, this message translates to:
  /// **'Initializing'**
  String get initializing;

  /// No description provided for @refreshingStatus.
  ///
  /// In en, this message translates to:
  /// **'Refreshing status...'**
  String get refreshingStatus;

  /// No description provided for @errorLoadingAttendanceStatus.
  ///
  /// In en, this message translates to:
  /// **'Error loading attendance status.'**
  String get errorLoadingAttendanceStatus;

  /// No description provided for @allSessionsCompleted.
  ///
  /// In en, this message translates to:
  /// **'All sessions completed.'**
  String get allSessionsCompleted;

  /// No description provided for @noFurtherActionsAvailable.
  ///
  /// In en, this message translates to:
  /// **'No further actions available.'**
  String get noFurtherActionsAvailable;

  /// No description provided for @locationServicesDisabled.
  ///
  /// In en, this message translates to:
  /// **'Location services are disabled.'**
  String get locationServicesDisabled;

  /// No description provided for @detectFaceFaceFoundCount.
  ///
  /// In en, this message translates to:
  /// **'Face detected ({count})'**
  String detectFaceFaceFoundCount(Object count);

  /// No description provided for @errorDetectingFaces.
  ///
  /// In en, this message translates to:
  /// **'Error detecting faces.{e}'**
  String errorDetectingFaces(Object e);

  /// No description provided for @notReadyForAction.
  ///
  /// In en, this message translates to:
  /// **'System not ready for action. {e}'**
  String notReadyForAction(Object e);

  /// No description provided for @checkIn.
  ///
  /// In en, this message translates to:
  /// **'Check In'**
  String get checkIn;

  /// No description provided for @imageCleared.
  ///
  /// In en, this message translates to:
  /// **'Image has been cleared.'**
  String get imageCleared;

  /// No description provided for @statusUpdated.
  ///
  /// In en, this message translates to:
  /// **'Status updated.'**
  String get statusUpdated;

  /// No description provided for @errorGettingLocation.
  ///
  /// In en, this message translates to:
  /// **'Error getting location: {error}'**
  String errorGettingLocation(Object error);

  /// No description provided for @errorCapturingImage.
  ///
  /// In en, this message translates to:
  /// **'Error capturing image: {error}'**
  String errorCapturingImage(Object error);

  /// No description provided for @lastCheckOut.
  ///
  /// In en, this message translates to:
  /// **'Last Check-Out'**
  String get lastCheckOut;

  /// No description provided for @lastCheckIn.
  ///
  /// In en, this message translates to:
  /// **'Last Check-In'**
  String get lastCheckIn;

  /// No description provided for @totalCheckIns.
  ///
  /// In en, this message translates to:
  /// **'Total Check-Ins'**
  String get totalCheckIns;

  /// No description provided for @totalCheckOuts.
  ///
  /// In en, this message translates to:
  /// **'Total Check-Outs'**
  String get totalCheckOuts;

  /// No description provided for @notCurrentlyWorking.
  ///
  /// In en, this message translates to:
  /// **'Not currently working'**
  String get notCurrentlyWorking;

  /// No description provided for @working.
  ///
  /// In en, this message translates to:
  /// **'Working'**
  String get working;

  /// No description provided for @loading.
  ///
  /// In en, this message translates to:
  /// **'Loading...'**
  String get loading;

  /// No description provided for @retryInitialization.
  ///
  /// In en, this message translates to:
  /// **'Retry initialization'**
  String get retryInitialization;

  /// No description provided for @noActionAvailable.
  ///
  /// In en, this message translates to:
  /// **'No action available'**
  String get noActionAvailable;

  /// No description provided for @startNewSession.
  ///
  /// In en, this message translates to:
  /// **'Start new session'**
  String get startNewSession;

  /// No description provided for @notification.
  ///
  /// In en, this message translates to:
  /// **'Notification'**
  String get notification;

  /// No description provided for @errorLoadingData.
  ///
  /// In en, this message translates to:
  /// **'Error loading data'**
  String get errorLoadingData;

  /// No description provided for @detectFaceConfirmCheckOut.
  ///
  /// In en, this message translates to:
  /// **'Face detected. Confirm check-out?'**
  String get detectFaceConfirmCheckOut;

  /// No description provided for @detectFaceConfirmCheckIn.
  ///
  /// In en, this message translates to:
  /// **'Face detected. Confirm check-in?'**
  String get detectFaceConfirmCheckIn;

  /// No description provided for @clearImage.
  ///
  /// In en, this message translates to:
  /// **'Clear image'**
  String get clearImage;

  /// No description provided for @checkOut.
  ///
  /// In en, this message translates to:
  /// **'Check out'**
  String get checkOut;

  /// No description provided for @unexpectedErrorPleaseRetry.
  ///
  /// In en, this message translates to:
  /// **'Unexpected error occurred. Please retry.'**
  String get unexpectedErrorPleaseRetry;

  /// No description provided for @noFaceDetectedInImage.
  ///
  /// In en, this message translates to:
  /// **'No face detected in the image.'**
  String get noFaceDetectedInImage;

  /// No description provided for @pleaseCaptureImage.
  ///
  /// In en, this message translates to:
  /// **'Please capture an image.'**
  String get pleaseCaptureImage;

  /// No description provided for @pleaseWaitForLocation.
  ///
  /// In en, this message translates to:
  /// **'Please wait for location data...'**
  String get pleaseWaitForLocation;

  /// No description provided for @attendance.
  ///
  /// In en, this message translates to:
  /// **'Attendance'**
  String get attendance;

  /// No description provided for @thisMonth.
  ///
  /// In en, this message translates to:
  /// **'Month'**
  String get thisMonth;

  /// No description provided for @unexpectedError.
  ///
  /// In en, this message translates to:
  /// **'An unexpected error occurred.'**
  String get unexpectedError;

  /// No description provided for @todaysSummary.
  ///
  /// In en, this message translates to:
  /// **'Today\'s Summary'**
  String get todaysSummary;

  /// No description provided for @checkedIn.
  ///
  /// In en, this message translates to:
  /// **'Checked In'**
  String get checkedIn;

  /// No description provided for @notCheckedIn.
  ///
  /// In en, this message translates to:
  /// **'Not Checked In'**
  String get notCheckedIn;

  /// No description provided for @totalHours.
  ///
  /// In en, this message translates to:
  /// **'Total Hours'**
  String get totalHours;

  /// No description provided for @overtime.
  ///
  /// In en, this message translates to:
  /// **'Overtime'**
  String get overtime;

  /// No description provided for @weeklySummary.
  ///
  /// In en, this message translates to:
  /// **'Weekly Summary'**
  String get weeklySummary;

  /// No description provided for @workDays.
  ///
  /// In en, this message translates to:
  /// **'Work Days'**
  String get workDays;

  /// No description provided for @lateArrivals.
  ///
  /// In en, this message translates to:
  /// **'Late Arrivals'**
  String get lateArrivals;

  /// No description provided for @weeklyPerformance.
  ///
  /// In en, this message translates to:
  /// **'Weekly Performance'**
  String get weeklyPerformance;

  /// No description provided for @monthlySummary.
  ///
  /// In en, this message translates to:
  /// **'Monthly Summary'**
  String get monthlySummary;

  /// No description provided for @overtimeRequest.
  ///
  /// In en, this message translates to:
  /// **'Overtime Request'**
  String get overtimeRequest;

  /// No description provided for @overtimeSummary.
  ///
  /// In en, this message translates to:
  /// **'Overtime Summary'**
  String get overtimeSummary;

  /// No description provided for @overtimeDetails.
  ///
  /// In en, this message translates to:
  /// **'Overtime Details'**
  String get overtimeDetails;

  /// No description provided for @newRequest.
  ///
  /// In en, this message translates to:
  /// **'New Request'**
  String get newRequest;

  /// No description provided for @myOvertime.
  ///
  /// In en, this message translates to:
  /// **'My Overtime'**
  String get myOvertime;

  /// No description provided for @pending.
  ///
  /// In en, this message translates to:
  /// **'Pending'**
  String get pending;

  /// No description provided for @approved.
  ///
  /// In en, this message translates to:
  /// **'Approved'**
  String get approved;

  /// No description provided for @rejected.
  ///
  /// In en, this message translates to:
  /// **'Rejected'**
  String get rejected;

  /// No description provided for @date.
  ///
  /// In en, this message translates to:
  /// **'Date'**
  String get date;

  /// No description provided for @startTime.
  ///
  /// In en, this message translates to:
  /// **'Start Time'**
  String get startTime;

  /// No description provided for @endTime.
  ///
  /// In en, this message translates to:
  /// **'End Time'**
  String get endTime;

  /// No description provided for @reason.
  ///
  /// In en, this message translates to:
  /// **'Reason'**
  String get reason;

  /// No description provided for @overtimeType.
  ///
  /// In en, this message translates to:
  /// **'Overtime Type'**
  String get overtimeType;

  /// No description provided for @regularOvertime.
  ///
  /// In en, this message translates to:
  /// **'Regular'**
  String get regularOvertime;

  /// No description provided for @weekendOvertime.
  ///
  /// In en, this message translates to:
  /// **'Weekend'**
  String get weekendOvertime;

  /// No description provided for @holidayOvertime.
  ///
  /// In en, this message translates to:
  /// **'Holiday'**
  String get holidayOvertime;

  /// No description provided for @submitRequest.
  ///
  /// In en, this message translates to:
  /// **'Submit Request'**
  String get submitRequest;

  /// No description provided for @submitting.
  ///
  /// In en, this message translates to:
  /// **'Submitting...'**
  String get submitting;

  /// No description provided for @select.
  ///
  /// In en, this message translates to:
  /// **'Select'**
  String get select;

  /// No description provided for @hour.
  ///
  /// In en, this message translates to:
  /// **'Hour'**
  String get hour;

  /// No description provided for @minute.
  ///
  /// In en, this message translates to:
  /// **'Minute'**
  String get minute;

  /// No description provided for @period.
  ///
  /// In en, this message translates to:
  /// **'Period'**
  String get period;

  /// No description provided for @selected.
  ///
  /// In en, this message translates to:
  /// **'Selected'**
  String get selected;

  /// No description provided for @pleaseSelectAllFields.
  ///
  /// In en, this message translates to:
  /// **'Please select all required fields'**
  String get pleaseSelectAllFields;

  /// No description provided for @endTimeMustBeAfterStartTime.
  ///
  /// In en, this message translates to:
  /// **'End time must be after start time'**
  String get endTimeMustBeAfterStartTime;

  /// No description provided for @cannotSelectPastDates.
  ///
  /// In en, this message translates to:
  /// **'Cannot select past dates for overtime request'**
  String get cannotSelectPastDates;

  /// No description provided for @cannotSelectPastTime.
  ///
  /// In en, this message translates to:
  /// **'Cannot select past time for today\'s overtime'**
  String get cannotSelectPastTime;

  /// No description provided for @minimumOvertimeDuration.
  ///
  /// In en, this message translates to:
  /// **'Overtime duration must be at least 30 minutes'**
  String get minimumOvertimeDuration;

  /// No description provided for @maximumOvertimeDuration.
  ///
  /// In en, this message translates to:
  /// **'Overtime duration cannot exceed 12 hours'**
  String get maximumOvertimeDuration;

  /// No description provided for @reasonableWorkingHours.
  ///
  /// In en, this message translates to:
  /// **'Overtime hours should be between 6:00 AM and 11:00 PM'**
  String get reasonableWorkingHours;

  /// No description provided for @filterByStatus.
  ///
  /// In en, this message translates to:
  /// **'Filter by Status'**
  String get filterByStatus;

  /// No description provided for @allStatus.
  ///
  /// In en, this message translates to:
  /// **'All Status'**
  String get allStatus;

  /// No description provided for @noOvertimeRequests.
  ///
  /// In en, this message translates to:
  /// **'No overtime requests found'**
  String get noOvertimeRequests;

  /// No description provided for @submitYourFirstOvertimeRequest.
  ///
  /// In en, this message translates to:
  /// **'Submit your first overtime request using the New Request tab'**
  String get submitYourFirstOvertimeRequest;

  /// No description provided for @loadMore.
  ///
  /// In en, this message translates to:
  /// **'Load More'**
  String get loadMore;

  /// No description provided for @rejectionReason.
  ///
  /// In en, this message translates to:
  /// **'Rejection Reason'**
  String get rejectionReason;

  /// No description provided for @all.
  ///
  /// In en, this message translates to:
  /// **'All'**
  String get all;

  /// No description provided for @totalRequests.
  ///
  /// In en, this message translates to:
  /// **'Total Requests'**
  String get totalRequests;

  /// No description provided for @approve.
  ///
  /// In en, this message translates to:
  /// **'Approve'**
  String get approve;

  /// No description provided for @reject.
  ///
  /// In en, this message translates to:
  /// **'Reject'**
  String get reject;

  /// No description provided for @rejectRequest.
  ///
  /// In en, this message translates to:
  /// **'Reject Request'**
  String get rejectRequest;

  /// No description provided for @rejectionReasonDescription.
  ///
  /// In en, this message translates to:
  /// **'Please provide a clear reason for rejecting this overtime request. This will help the employee understand the decision.'**
  String get rejectionReasonDescription;

  /// No description provided for @enterRejectionReason.
  ///
  /// In en, this message translates to:
  /// **'Enter rejection reason...'**
  String get enterRejectionReason;

  /// No description provided for @rejectionReasonRequired.
  ///
  /// In en, this message translates to:
  /// **'Rejection reason is required'**
  String get rejectionReasonRequired;

  /// No description provided for @rejectionReasonTooShort.
  ///
  /// In en, this message translates to:
  /// **'Rejection reason must be at least 10 characters'**
  String get rejectionReasonTooShort;

  /// No description provided for @rejectionReasonTooLong.
  ///
  /// In en, this message translates to:
  /// **'Rejection reason must not exceed 500 characters'**
  String get rejectionReasonTooLong;

  /// No description provided for @overtimeReasonRequired.
  ///
  /// In en, this message translates to:
  /// **'Overtime reason is required'**
  String get overtimeReasonRequired;

  /// No description provided for @overtimeReasonTooShort.
  ///
  /// In en, this message translates to:
  /// **'Reason must be at least 10 characters long'**
  String get overtimeReasonTooShort;

  /// No description provided for @overtimeReasonTooLong.
  ///
  /// In en, this message translates to:
  /// **'Reason must not exceed 500 characters'**
  String get overtimeReasonTooLong;

  /// No description provided for @approvedBy.
  ///
  /// In en, this message translates to:
  /// **'Approved by'**
  String get approvedBy;

  /// No description provided for @rejectedBy.
  ///
  /// In en, this message translates to:
  /// **'Rejected by'**
  String get rejectedBy;

  /// No description provided for @time.
  ///
  /// In en, this message translates to:
  /// **'Time'**
  String get time;

  /// No description provided for @duration.
  ///
  /// In en, this message translates to:
  /// **'Duration'**
  String get duration;

  /// No description provided for @daysOff.
  ///
  /// In en, this message translates to:
  /// **'Days Off'**
  String get daysOff;

  /// No description provided for @fullMonthHistory.
  ///
  /// In en, this message translates to:
  /// **'Full Month History'**
  String get fullMonthHistory;

  /// No description provided for @inShort.
  ///
  /// In en, this message translates to:
  /// **'In'**
  String get inShort;

  /// No description provided for @outShort.
  ///
  /// In en, this message translates to:
  /// **'Out'**
  String get outShort;

  /// No description provided for @hoursShort.
  ///
  /// In en, this message translates to:
  /// **'Hours'**
  String get hoursShort;

  /// No description provided for @viewFullHistory.
  ///
  /// In en, this message translates to:
  /// **'View Full History'**
  String get viewFullHistory;

  /// No description provided for @selectDateToViewDetails.
  ///
  /// In en, this message translates to:
  /// **'Select a date to view details'**
  String get selectDateToViewDetails;

  /// No description provided for @errorOccurred.
  ///
  /// In en, this message translates to:
  /// **'An error occurred'**
  String get errorOccurred;

  /// No description provided for @selectApprover.
  ///
  /// In en, this message translates to:
  /// **'Select Approver'**
  String get selectApprover;

  /// No description provided for @noApproversAvailable.
  ///
  /// In en, this message translates to:
  /// **'No approvers available'**
  String get noApproversAvailable;

  /// No description provided for @cancel.
  ///
  /// In en, this message translates to:
  /// **'Cancel'**
  String get cancel;

  /// No description provided for @createRequest.
  ///
  /// In en, this message translates to:
  /// **'Create Request'**
  String get createRequest;

  /// No description provided for @loadingOvertimeHistory.
  ///
  /// In en, this message translates to:
  /// **'Loading overtime history...'**
  String get loadingOvertimeHistory;

  /// No description provided for @overtimeHistory.
  ///
  /// In en, this message translates to:
  /// **'Overtime History'**
  String get overtimeHistory;

  /// No description provided for @viewAll.
  ///
  /// In en, this message translates to:
  /// **'View All'**
  String get viewAll;

  /// No description provided for @recentRequests.
  ///
  /// In en, this message translates to:
  /// **'Recent Requests'**
  String get recentRequests;

  /// No description provided for @calendar.
  ///
  /// In en, this message translates to:
  /// **'Calendar'**
  String get calendar;

  /// No description provided for @calendarPageTitle.
  ///
  /// In en, this message translates to:
  /// **'Calendar'**
  String get calendarPageTitle;

  /// No description provided for @calendarTabMonth.
  ///
  /// In en, this message translates to:
  /// **'Month'**
  String get calendarTabMonth;

  /// No description provided for @calendarTabWeek.
  ///
  /// In en, this message translates to:
  /// **'Week'**
  String get calendarTabWeek;

  /// No description provided for @calendarTabAgenda.
  ///
  /// In en, this message translates to:
  /// **'Agenda'**
  String get calendarTabAgenda;

  /// No description provided for @calendarHeaderEvents.
  ///
  /// In en, this message translates to:
  /// **'Events'**
  String get calendarHeaderEvents;

  /// No description provided for @calendarHeaderMeetings.
  ///
  /// In en, this message translates to:
  /// **'Meetings'**
  String get calendarHeaderMeetings;

  /// No description provided for @calendarHeaderHolidays.
  ///
  /// In en, this message translates to:
  /// **'Holidays'**
  String get calendarHeaderHolidays;

  /// No description provided for @calendarWeekViewTitle.
  ///
  /// In en, this message translates to:
  /// **'Week View'**
  String get calendarWeekViewTitle;

  /// No description provided for @calendarWeekEventsTitle.
  ///
  /// In en, this message translates to:
  /// **'Week Events'**
  String get calendarWeekEventsTitle;

  /// No description provided for @calendarAddEventDialogTitle.
  ///
  /// In en, this message translates to:
  /// **'Add Event'**
  String get calendarAddEventDialogTitle;

  /// No description provided for @calendarEventTitleHint.
  ///
  /// In en, this message translates to:
  /// **'Event Title'**
  String get calendarEventTitleHint;

  /// No description provided for @calendarTimeHint.
  ///
  /// In en, this message translates to:
  /// **'Time'**
  String get calendarTimeHint;

  /// No description provided for @calendarEventTypeHint.
  ///
  /// In en, this message translates to:
  /// **'Event Type'**
  String get calendarEventTypeHint;

  /// No description provided for @calendarAddEventButton.
  ///
  /// In en, this message translates to:
  /// **'Add Event'**
  String get calendarAddEventButton;

  /// No description provided for @calendarEventsOnDate.
  ///
  /// In en, this message translates to:
  /// **'Events on {date}'**
  String calendarEventsOnDate(String date);

  /// No description provided for @calendarDayAbbreviationsSun.
  ///
  /// In en, this message translates to:
  /// **'Sun'**
  String get calendarDayAbbreviationsSun;

  /// No description provided for @calendarDayAbbreviationsMon.
  ///
  /// In en, this message translates to:
  /// **'Mon'**
  String get calendarDayAbbreviationsMon;

  /// No description provided for @calendarDayAbbreviationsTue.
  ///
  /// In en, this message translates to:
  /// **'Tue'**
  String get calendarDayAbbreviationsTue;

  /// No description provided for @calendarDayAbbreviationsWed.
  ///
  /// In en, this message translates to:
  /// **'Wed'**
  String get calendarDayAbbreviationsWed;

  /// No description provided for @calendarDayAbbreviationsThu.
  ///
  /// In en, this message translates to:
  /// **'Thu'**
  String get calendarDayAbbreviationsThu;

  /// No description provided for @calendarDayAbbreviationsFri.
  ///
  /// In en, this message translates to:
  /// **'Fri'**
  String get calendarDayAbbreviationsFri;

  /// No description provided for @calendarDayAbbreviationsSat.
  ///
  /// In en, this message translates to:
  /// **'Sat'**
  String get calendarDayAbbreviationsSat;

  /// No description provided for @calendarFullMonthNamesJan.
  ///
  /// In en, this message translates to:
  /// **'January'**
  String get calendarFullMonthNamesJan;

  /// No description provided for @calendarFullMonthNamesFeb.
  ///
  /// In en, this message translates to:
  /// **'February'**
  String get calendarFullMonthNamesFeb;

  /// No description provided for @calendarFullMonthNamesMar.
  ///
  /// In en, this message translates to:
  /// **'March'**
  String get calendarFullMonthNamesMar;

  /// No description provided for @calendarFullMonthNamesApr.
  ///
  /// In en, this message translates to:
  /// **'April'**
  String get calendarFullMonthNamesApr;

  /// No description provided for @calendarFullMonthNamesMay.
  ///
  /// In en, this message translates to:
  /// **'May'**
  String get calendarFullMonthNamesMay;

  /// No description provided for @calendarFullMonthNamesJun.
  ///
  /// In en, this message translates to:
  /// **'June'**
  String get calendarFullMonthNamesJun;

  /// No description provided for @calendarFullMonthNamesJul.
  ///
  /// In en, this message translates to:
  /// **'July'**
  String get calendarFullMonthNamesJul;

  /// No description provided for @calendarFullMonthNamesAug.
  ///
  /// In en, this message translates to:
  /// **'August'**
  String get calendarFullMonthNamesAug;

  /// No description provided for @calendarFullMonthNamesSep.
  ///
  /// In en, this message translates to:
  /// **'September'**
  String get calendarFullMonthNamesSep;

  /// No description provided for @calendarFullMonthNamesOct.
  ///
  /// In en, this message translates to:
  /// **'October'**
  String get calendarFullMonthNamesOct;

  /// No description provided for @calendarFullMonthNamesNov.
  ///
  /// In en, this message translates to:
  /// **'November'**
  String get calendarFullMonthNamesNov;

  /// No description provided for @calendarFullMonthNamesDec.
  ///
  /// In en, this message translates to:
  /// **'December'**
  String get calendarFullMonthNamesDec;

  /// No description provided for @calendarShortMonthNamesJan.
  ///
  /// In en, this message translates to:
  /// **'Jan'**
  String get calendarShortMonthNamesJan;

  /// No description provided for @calendarShortMonthNamesFeb.
  ///
  /// In en, this message translates to:
  /// **'Feb'**
  String get calendarShortMonthNamesFeb;

  /// No description provided for @calendarShortMonthNamesMar.
  ///
  /// In en, this message translates to:
  /// **'Mar'**
  String get calendarShortMonthNamesMar;

  /// No description provided for @calendarShortMonthNamesApr.
  ///
  /// In en, this message translates to:
  /// **'Apr'**
  String get calendarShortMonthNamesApr;

  /// No description provided for @calendarShortMonthNamesMay.
  ///
  /// In en, this message translates to:
  /// **'May'**
  String get calendarShortMonthNamesMay;

  /// No description provided for @calendarShortMonthNamesJun.
  ///
  /// In en, this message translates to:
  /// **'Jun'**
  String get calendarShortMonthNamesJun;

  /// No description provided for @calendarShortMonthNamesJul.
  ///
  /// In en, this message translates to:
  /// **'Jul'**
  String get calendarShortMonthNamesJul;

  /// No description provided for @calendarShortMonthNamesAug.
  ///
  /// In en, this message translates to:
  /// **'Aug'**
  String get calendarShortMonthNamesAug;

  /// No description provided for @calendarShortMonthNamesSep.
  ///
  /// In en, this message translates to:
  /// **'Sep'**
  String get calendarShortMonthNamesSep;

  /// No description provided for @calendarShortMonthNamesOct.
  ///
  /// In en, this message translates to:
  /// **'Oct'**
  String get calendarShortMonthNamesOct;

  /// No description provided for @calendarShortMonthNamesNov.
  ///
  /// In en, this message translates to:
  /// **'Nov'**
  String get calendarShortMonthNamesNov;

  /// No description provided for @calendarShortMonthNamesDec.
  ///
  /// In en, this message translates to:
  /// **'Dec'**
  String get calendarShortMonthNamesDec;

  /// No description provided for @calendarSampleStatEventsCount.
  ///
  /// In en, this message translates to:
  /// **'12'**
  String get calendarSampleStatEventsCount;

  /// No description provided for @calendarSampleStatMeetingsCount.
  ///
  /// In en, this message translates to:
  /// **'8'**
  String get calendarSampleStatMeetingsCount;

  /// No description provided for @calendarSampleStatHolidaysCount.
  ///
  /// In en, this message translates to:
  /// **'3'**
  String get calendarSampleStatHolidaysCount;

  /// No description provided for @tomorrow.
  ///
  /// In en, this message translates to:
  /// **'Tomorrow'**
  String get tomorrow;

  /// No description provided for @noEventsForThisWeek.
  ///
  /// In en, this message translates to:
  /// **'No events for this week'**
  String get noEventsForThisWeek;

  /// No description provided for @noUpcomingEvents.
  ///
  /// In en, this message translates to:
  /// **'No upcoming events'**
  String get noUpcomingEvents;

  /// No description provided for @addEvent.
  ///
  /// In en, this message translates to:
  /// **'Add Event'**
  String get addEvent;

  /// No description provided for @editEvent.
  ///
  /// In en, this message translates to:
  /// **'Edit Event'**
  String get editEvent;

  /// No description provided for @deleteEvent.
  ///
  /// In en, this message translates to:
  /// **'Delete Event'**
  String get deleteEvent;

  /// No description provided for @eventDetails.
  ///
  /// In en, this message translates to:
  /// **'Event Details'**
  String get eventDetails;

  /// No description provided for @eventTitle.
  ///
  /// In en, this message translates to:
  /// **'Event Title'**
  String get eventTitle;

  /// No description provided for @eventDescription.
  ///
  /// In en, this message translates to:
  /// **'Event Description'**
  String get eventDescription;

  /// No description provided for @eventDate.
  ///
  /// In en, this message translates to:
  /// **'Event Date'**
  String get eventDate;

  /// No description provided for @eventTime.
  ///
  /// In en, this message translates to:
  /// **'Event Time'**
  String get eventTime;

  /// No description provided for @eventType.
  ///
  /// In en, this message translates to:
  /// **'Event Type'**
  String get eventType;

  /// No description provided for @eventLocation.
  ///
  /// In en, this message translates to:
  /// **'Event Location'**
  String get eventLocation;

  /// No description provided for @allDay.
  ///
  /// In en, this message translates to:
  /// **'All Day'**
  String get allDay;

  /// No description provided for @recurring.
  ///
  /// In en, this message translates to:
  /// **'Recurring'**
  String get recurring;

  /// No description provided for @attendees.
  ///
  /// In en, this message translates to:
  /// **'Attendees'**
  String get attendees;

  /// No description provided for @meeting.
  ///
  /// In en, this message translates to:
  /// **'Meeting'**
  String get meeting;

  /// No description provided for @leave.
  ///
  /// In en, this message translates to:
  /// **'Leave'**
  String get leave;

  /// No description provided for @holiday.
  ///
  /// In en, this message translates to:
  /// **'Holiday'**
  String get holiday;

  /// No description provided for @training.
  ///
  /// In en, this message translates to:
  /// **'Training'**
  String get training;

  /// No description provided for @event.
  ///
  /// In en, this message translates to:
  /// **'Event'**
  String get event;

  /// No description provided for @searchEvents.
  ///
  /// In en, this message translates to:
  /// **'Search Events'**
  String get searchEvents;

  /// No description provided for @noEventsFound.
  ///
  /// In en, this message translates to:
  /// **'No events found'**
  String get noEventsFound;

  /// No description provided for @loadingEvents.
  ///
  /// In en, this message translates to:
  /// **'Loading events...'**
  String get loadingEvents;

  /// No description provided for @addingEvent.
  ///
  /// In en, this message translates to:
  /// **'Adding event...'**
  String get addingEvent;

  /// No description provided for @updatingEvent.
  ///
  /// In en, this message translates to:
  /// **'Updating event...'**
  String get updatingEvent;

  /// No description provided for @deletingEvent.
  ///
  /// In en, this message translates to:
  /// **'Deleting event...'**
  String get deletingEvent;

  /// No description provided for @eventAddedSuccessfully.
  ///
  /// In en, this message translates to:
  /// **'Event added successfully'**
  String get eventAddedSuccessfully;

  /// No description provided for @eventUpdatedSuccessfully.
  ///
  /// In en, this message translates to:
  /// **'Event updated successfully'**
  String get eventUpdatedSuccessfully;

  /// No description provided for @eventDeletedSuccessfully.
  ///
  /// In en, this message translates to:
  /// **'Event deleted successfully'**
  String get eventDeletedSuccessfully;

  /// No description provided for @failedToAddEvent.
  ///
  /// In en, this message translates to:
  /// **'Failed to add event'**
  String get failedToAddEvent;

  /// No description provided for @failedToUpdateEvent.
  ///
  /// In en, this message translates to:
  /// **'Failed to update event'**
  String get failedToUpdateEvent;

  /// No description provided for @failedToDeleteEvent.
  ///
  /// In en, this message translates to:
  /// **'Failed to delete event'**
  String get failedToDeleteEvent;

  /// No description provided for @confirmDeleteEvent.
  ///
  /// In en, this message translates to:
  /// **'Are you sure you want to delete this event?'**
  String get confirmDeleteEvent;

  /// No description provided for @confirmDeleteEvents.
  ///
  /// In en, this message translates to:
  /// **'Are you sure you want to delete {count} events?'**
  String confirmDeleteEvents(int count);

  /// No description provided for @selectEventType.
  ///
  /// In en, this message translates to:
  /// **'Select Event Type'**
  String get selectEventType;

  /// No description provided for @selectDate.
  ///
  /// In en, this message translates to:
  /// **'Select date'**
  String get selectDate;

  /// No description provided for @selectTime.
  ///
  /// In en, this message translates to:
  /// **'Select Time'**
  String get selectTime;

  /// No description provided for @edit.
  ///
  /// In en, this message translates to:
  /// **'Edit'**
  String get edit;

  /// No description provided for @delete.
  ///
  /// In en, this message translates to:
  /// **'Delete'**
  String get delete;

  /// No description provided for @refresh.
  ///
  /// In en, this message translates to:
  /// **'Refresh'**
  String get refresh;

  /// No description provided for @sync.
  ///
  /// In en, this message translates to:
  /// **'Sync'**
  String get sync;

  /// No description provided for @export.
  ///
  /// In en, this message translates to:
  /// **'Export'**
  String get export;

  /// No description provided for @import.
  ///
  /// In en, this message translates to:
  /// **'Import'**
  String get import;

  /// No description provided for @filter.
  ///
  /// In en, this message translates to:
  /// **'Filter'**
  String get filter;

  /// No description provided for @clear.
  ///
  /// In en, this message translates to:
  /// **'Clear'**
  String get clear;

  /// No description provided for @apply.
  ///
  /// In en, this message translates to:
  /// **'Apply'**
  String get apply;

  /// No description provided for @close.
  ///
  /// In en, this message translates to:
  /// **'Close'**
  String get close;

  /// No description provided for @sessionDetails.
  ///
  /// In en, this message translates to:
  /// **'Session Details'**
  String get sessionDetails;

  /// No description provided for @sessionNumber.
  ///
  /// In en, this message translates to:
  /// **'Session {number}'**
  String sessionNumber(int number);

  /// No description provided for @multipleSessionsDetails.
  ///
  /// In en, this message translates to:
  /// **'Multiple Sessions Details ({count})'**
  String multipleSessionsDetails(int count);

  /// No description provided for @sessionCompleted.
  ///
  /// In en, this message translates to:
  /// **'Completed'**
  String get sessionCompleted;

  /// No description provided for @sessionInProgress.
  ///
  /// In en, this message translates to:
  /// **'In Progress'**
  String get sessionInProgress;

  /// No description provided for @location.
  ///
  /// In en, this message translates to:
  /// **'Location'**
  String get location;

  /// No description provided for @unknownLocation.
  ///
  /// In en, this message translates to:
  /// **'Unknown location'**
  String get unknownLocation;

  /// No description provided for @statusOnTime.
  ///
  /// In en, this message translates to:
  /// **'On Time'**
  String get statusOnTime;

  /// No description provided for @statusLate.
  ///
  /// In en, this message translates to:
  /// **'Late'**
  String get statusLate;

  /// No description provided for @statusAbsent.
  ///
  /// In en, this message translates to:
  /// **'Absent'**
  String get statusAbsent;

  /// No description provided for @statusOnLeave.
  ///
  /// In en, this message translates to:
  /// **'On Leave'**
  String get statusOnLeave;

  /// No description provided for @statusWeekend.
  ///
  /// In en, this message translates to:
  /// **'Weekend'**
  String get statusWeekend;

  /// No description provided for @statusNoRecord.
  ///
  /// In en, this message translates to:
  /// **'No Record'**
  String get statusNoRecord;

  /// No description provided for @sunday.
  ///
  /// In en, this message translates to:
  /// **'Sunday'**
  String get sunday;

  /// No description provided for @monday.
  ///
  /// In en, this message translates to:
  /// **'Monday'**
  String get monday;

  /// No description provided for @tuesday.
  ///
  /// In en, this message translates to:
  /// **'Tuesday'**
  String get tuesday;

  /// No description provided for @wednesday.
  ///
  /// In en, this message translates to:
  /// **'Wednesday'**
  String get wednesday;

  /// No description provided for @thursday.
  ///
  /// In en, this message translates to:
  /// **'Thursday'**
  String get thursday;

  /// No description provided for @friday.
  ///
  /// In en, this message translates to:
  /// **'Friday'**
  String get friday;

  /// No description provided for @saturday.
  ///
  /// In en, this message translates to:
  /// **'Saturday'**
  String get saturday;

  /// No description provided for @january.
  ///
  /// In en, this message translates to:
  /// **'January'**
  String get january;

  /// No description provided for @february.
  ///
  /// In en, this message translates to:
  /// **'February'**
  String get february;

  /// No description provided for @march.
  ///
  /// In en, this message translates to:
  /// **'March'**
  String get march;

  /// No description provided for @april.
  ///
  /// In en, this message translates to:
  /// **'April'**
  String get april;

  /// No description provided for @may.
  ///
  /// In en, this message translates to:
  /// **'May'**
  String get may;

  /// No description provided for @june.
  ///
  /// In en, this message translates to:
  /// **'June'**
  String get june;

  /// No description provided for @july.
  ///
  /// In en, this message translates to:
  /// **'July'**
  String get july;

  /// No description provided for @august.
  ///
  /// In en, this message translates to:
  /// **'August'**
  String get august;

  /// No description provided for @september.
  ///
  /// In en, this message translates to:
  /// **'September'**
  String get september;

  /// No description provided for @october.
  ///
  /// In en, this message translates to:
  /// **'October'**
  String get october;

  /// No description provided for @november.
  ///
  /// In en, this message translates to:
  /// **'November'**
  String get november;

  /// No description provided for @december.
  ///
  /// In en, this message translates to:
  /// **'December'**
  String get december;

  /// No description provided for @createOvertimeRequest.
  ///
  /// In en, this message translates to:
  /// **'Create Overtime Request'**
  String get createOvertimeRequest;

  /// No description provided for @overtimeDate.
  ///
  /// In en, this message translates to:
  /// **'Overtime Date'**
  String get overtimeDate;

  /// No description provided for @approver.
  ///
  /// In en, this message translates to:
  /// **'Approver'**
  String get approver;

  /// No description provided for @pleaseSelectDate.
  ///
  /// In en, this message translates to:
  /// **'Please select date'**
  String get pleaseSelectDate;

  /// No description provided for @pleaseSelectStartTime.
  ///
  /// In en, this message translates to:
  /// **'Please select start time'**
  String get pleaseSelectStartTime;

  /// No description provided for @pleaseSelectEndTime.
  ///
  /// In en, this message translates to:
  /// **'Please select end time'**
  String get pleaseSelectEndTime;

  /// No description provided for @pleaseEnterReason.
  ///
  /// In en, this message translates to:
  /// **'Please enter reason'**
  String get pleaseEnterReason;

  /// No description provided for @pleaseSelectApprover.
  ///
  /// In en, this message translates to:
  /// **'Please select approver'**
  String get pleaseSelectApprover;

  /// No description provided for @overtimeRequestSubmitted.
  ///
  /// In en, this message translates to:
  /// **'Overtime request submitted successfully'**
  String get overtimeRequestSubmitted;

  /// No description provided for @failedToSubmitRequest.
  ///
  /// In en, this message translates to:
  /// **'Failed to submit request'**
  String get failedToSubmitRequest;

  /// No description provided for @loadingOvertimeRequests.
  ///
  /// In en, this message translates to:
  /// **'Loading overtime requests...'**
  String get loadingOvertimeRequests;

  /// No description provided for @overtimeRequestDetails.
  ///
  /// In en, this message translates to:
  /// **'Overtime Request Details'**
  String get overtimeRequestDetails;

  /// No description provided for @requestDate.
  ///
  /// In en, this message translates to:
  /// **'Request Date'**
  String get requestDate;

  /// No description provided for @hours.
  ///
  /// In en, this message translates to:
  /// **'hours'**
  String get hours;

  /// No description provided for @minutes.
  ///
  /// In en, this message translates to:
  /// **'minutes'**
  String get minutes;

  /// No description provided for @pendingApproval.
  ///
  /// In en, this message translates to:
  /// **'Pending approval'**
  String get pendingApproval;

  /// No description provided for @submittedOn.
  ///
  /// In en, this message translates to:
  /// **'Submitted on'**
  String get submittedOn;

  /// No description provided for @fillDetailsToSubmit.
  ///
  /// In en, this message translates to:
  /// **'Fill in the details below to submit your overtime request'**
  String get fillDetailsToSubmit;

  /// No description provided for @leaveHistory.
  ///
  /// In en, this message translates to:
  /// **'Leave History'**
  String get leaveHistory;

  /// No description provided for @leaveRequest.
  ///
  /// In en, this message translates to:
  /// **'Leave Request'**
  String get leaveRequest;

  /// No description provided for @leaveRequestDetails.
  ///
  /// In en, this message translates to:
  /// **'Leave Request Details'**
  String get leaveRequestDetails;

  /// No description provided for @myLeave.
  ///
  /// In en, this message translates to:
  /// **'My Leave'**
  String get myLeave;

  /// No description provided for @submitLeaveRequest.
  ///
  /// In en, this message translates to:
  /// **'Submit Leave Request'**
  String get submitLeaveRequest;

  /// No description provided for @fillLeaveDetailsToSubmit.
  ///
  /// In en, this message translates to:
  /// **'Fill in the details below to submit your leave request'**
  String get fillLeaveDetailsToSubmit;

  /// No description provided for @remainingLeaveDays.
  ///
  /// In en, this message translates to:
  /// **'Remaining Leave Days'**
  String get remainingLeaveDays;

  /// No description provided for @outOfDays.
  ///
  /// In en, this message translates to:
  /// **'out of {totalDays} days'**
  String outOfDays(int totalDays);

  /// No description provided for @used.
  ///
  /// In en, this message translates to:
  /// **'Used'**
  String get used;

  /// No description provided for @cancelled.
  ///
  /// In en, this message translates to:
  /// **'Cancelled'**
  String get cancelled;

  /// No description provided for @days.
  ///
  /// In en, this message translates to:
  /// **'days'**
  String get days;

  /// No description provided for @day.
  ///
  /// In en, this message translates to:
  /// **'day'**
  String get day;

  /// No description provided for @leavePolicy.
  ///
  /// In en, this message translates to:
  /// **'Leave Policy'**
  String get leavePolicy;

  /// No description provided for @recentLeaveRequests.
  ///
  /// In en, this message translates to:
  /// **'Recent Leave Requests'**
  String get recentLeaveRequests;

  /// No description provided for @viewAllLeaveHistory.
  ///
  /// In en, this message translates to:
  /// **'View All History'**
  String get viewAllLeaveHistory;

  /// No description provided for @noLeaveRequestsYet.
  ///
  /// In en, this message translates to:
  /// **'No leave requests yet'**
  String get noLeaveRequestsYet;

  /// No description provided for @noFilteredRequests.
  ///
  /// In en, this message translates to:
  /// **'No {status} requests'**
  String noFilteredRequests(String status);

  /// No description provided for @yourLeaveRequestsWillAppearHere.
  ///
  /// In en, this message translates to:
  /// **'Your leave requests will appear here'**
  String get yourLeaveRequestsWillAppearHere;

  /// No description provided for @showAll.
  ///
  /// In en, this message translates to:
  /// **'Show All'**
  String get showAll;

  /// No description provided for @editRequest.
  ///
  /// In en, this message translates to:
  /// **'Edit Request'**
  String get editRequest;

  /// No description provided for @cancelRequest.
  ///
  /// In en, this message translates to:
  /// **'Cancel Request'**
  String get cancelRequest;

  /// No description provided for @requestDetails.
  ///
  /// In en, this message translates to:
  /// **'Request Details'**
  String get requestDetails;

  /// No description provided for @leaveType.
  ///
  /// In en, this message translates to:
  /// **'Leave Type'**
  String get leaveType;

  /// No description provided for @startDate.
  ///
  /// In en, this message translates to:
  /// **'Start Date'**
  String get startDate;

  /// No description provided for @endDate.
  ///
  /// In en, this message translates to:
  /// **'End Date'**
  String get endDate;

  /// No description provided for @status.
  ///
  /// In en, this message translates to:
  /// **'Status'**
  String get status;

  /// No description provided for @approvedOn.
  ///
  /// In en, this message translates to:
  /// **'Approved on'**
  String get approvedOn;

  /// No description provided for @rejectedOn.
  ///
  /// In en, this message translates to:
  /// **'Rejected on'**
  String get rejectedOn;

  /// No description provided for @cancelRequestConfirm.
  ///
  /// In en, this message translates to:
  /// **'Are you sure you want to cancel this leave request?'**
  String get cancelRequestConfirm;

  /// No description provided for @no.
  ///
  /// In en, this message translates to:
  /// **'No'**
  String get no;

  /// No description provided for @yes.
  ///
  /// In en, this message translates to:
  /// **'Yes'**
  String get yes;

  /// No description provided for @requestCancelled.
  ///
  /// In en, this message translates to:
  /// **'Request cancelled'**
  String get requestCancelled;

  /// No description provided for @editFunctionalityComingSoon.
  ///
  /// In en, this message translates to:
  /// **'Edit functionality coming soon'**
  String get editFunctionalityComingSoon;

  /// No description provided for @annualLeave.
  ///
  /// In en, this message translates to:
  /// **'Annual Leave'**
  String get annualLeave;

  /// No description provided for @sickLeave.
  ///
  /// In en, this message translates to:
  /// **'Sick Leave'**
  String get sickLeave;

  /// No description provided for @personalLeave.
  ///
  /// In en, this message translates to:
  /// **'Personal Leave'**
  String get personalLeave;

  /// No description provided for @maternityLeave.
  ///
  /// In en, this message translates to:
  /// **'Maternity Leave'**
  String get maternityLeave;

  /// No description provided for @unpaidLeave.
  ///
  /// In en, this message translates to:
  /// **'Unpaid Leave'**
  String get unpaidLeave;

  /// No description provided for @annualLeaveDescription.
  ///
  /// In en, this message translates to:
  /// **'Vacation, holiday, or personal time off'**
  String get annualLeaveDescription;

  /// No description provided for @sickLeaveDescription.
  ///
  /// In en, this message translates to:
  /// **'Medical appointments or illness'**
  String get sickLeaveDescription;

  /// No description provided for @personalLeaveDescription.
  ///
  /// In en, this message translates to:
  /// **'Personal matters or emergencies'**
  String get personalLeaveDescription;

  /// No description provided for @maternityLeaveDescription.
  ///
  /// In en, this message translates to:
  /// **'Maternity or paternity leave'**
  String get maternityLeaveDescription;

  /// No description provided for @unpaidLeaveDescription.
  ///
  /// In en, this message translates to:
  /// **'Extended leave without pay'**
  String get unpaidLeaveDescription;

  /// No description provided for @dateRange.
  ///
  /// In en, this message translates to:
  /// **'Date Range'**
  String get dateRange;

  /// No description provided for @durationInfo.
  ///
  /// In en, this message translates to:
  /// **'Duration: {duration} {durationUnit}'**
  String durationInfo(int duration, String durationUnit);

  /// No description provided for @reasonForLeave.
  ///
  /// In en, this message translates to:
  /// **'Reason for Leave'**
  String get reasonForLeave;

  /// No description provided for @leaveReasonHint.
  ///
  /// In en, this message translates to:
  /// **'Please provide a detailed reason for your leave request...\n\nExample:\n• Family vacation\n• Medical appointment\n• Personal emergency'**
  String get leaveReasonHint;

  /// No description provided for @pleaseProvideReason.
  ///
  /// In en, this message translates to:
  /// **'Please provide a reason for your leave'**
  String get pleaseProvideReason;

  /// No description provided for @pleaseFillAllFields.
  ///
  /// In en, this message translates to:
  /// **'Please fill in all required fields'**
  String get pleaseFillAllFields;

  /// No description provided for @pleaseSelectStartDate.
  ///
  /// In en, this message translates to:
  /// **'Please select a start date'**
  String get pleaseSelectStartDate;

  /// No description provided for @pleaseSelectEndDate.
  ///
  /// In en, this message translates to:
  /// **'Please select an end date'**
  String get pleaseSelectEndDate;

  /// No description provided for @leaveRequestSubmittedSuccess.
  ///
  /// In en, this message translates to:
  /// **'Leave request submitted successfully!'**
  String get leaveRequestSubmittedSuccess;

  /// No description provided for @failedToLoadLeaveData.
  ///
  /// In en, this message translates to:
  /// **'Failed to load leave data: {error}'**
  String failedToLoadLeaveData(String error);

  /// No description provided for @failedToSubmitLeaveRequest.
  ///
  /// In en, this message translates to:
  /// **'Failed to submit leave request: {error}'**
  String failedToSubmitLeaveRequest(String error);

  /// No description provided for @adminUserManagement.
  ///
  /// In en, this message translates to:
  /// **'User Management'**
  String get adminUserManagement;

  /// No description provided for @adminUsers.
  ///
  /// In en, this message translates to:
  /// **'Users'**
  String get adminUsers;

  /// No description provided for @adminStatistics.
  ///
  /// In en, this message translates to:
  /// **'Statistics'**
  String get adminStatistics;

  /// No description provided for @adminSettings.
  ///
  /// In en, this message translates to:
  /// **'Settings'**
  String get adminSettings;

  /// No description provided for @addNewUser.
  ///
  /// In en, this message translates to:
  /// **'Add New User'**
  String get addNewUser;

  /// No description provided for @createNewUserAccount.
  ///
  /// In en, this message translates to:
  /// **'Create a new user account'**
  String get createNewUserAccount;

  /// No description provided for @createUser.
  ///
  /// In en, this message translates to:
  /// **'Create User'**
  String get createUser;

  /// No description provided for @editUser.
  ///
  /// In en, this message translates to:
  /// **'Edit User'**
  String get editUser;

  /// No description provided for @editUserDetails.
  ///
  /// In en, this message translates to:
  /// **'Edit User Details'**
  String get editUserDetails;

  /// No description provided for @userDetails.
  ///
  /// In en, this message translates to:
  /// **'User Details'**
  String get userDetails;

  /// No description provided for @firstName.
  ///
  /// In en, this message translates to:
  /// **'First Name'**
  String get firstName;

  /// No description provided for @lastName.
  ///
  /// In en, this message translates to:
  /// **'Last Name'**
  String get lastName;

  /// No description provided for @email.
  ///
  /// In en, this message translates to:
  /// **'Email'**
  String get email;

  /// No description provided for @password.
  ///
  /// In en, this message translates to:
  /// **'Password'**
  String get password;

  /// No description provided for @confirmPassword.
  ///
  /// In en, this message translates to:
  /// **'Confirm Password'**
  String get confirmPassword;

  /// No description provided for @phoneNumber.
  ///
  /// In en, this message translates to:
  /// **'Phone Number'**
  String get phoneNumber;

  /// No description provided for @role.
  ///
  /// In en, this message translates to:
  /// **'Role'**
  String get role;

  /// No description provided for @department.
  ///
  /// In en, this message translates to:
  /// **'Department'**
  String get department;

  /// No description provided for @position.
  ///
  /// In en, this message translates to:
  /// **'Position'**
  String get position;

  /// No description provided for @organization.
  ///
  /// In en, this message translates to:
  /// **'Organization'**
  String get organization;

  /// No description provided for @active.
  ///
  /// In en, this message translates to:
  /// **'Active'**
  String get active;

  /// No description provided for @inactive.
  ///
  /// In en, this message translates to:
  /// **'Inactive'**
  String get inactive;

  /// No description provided for @disabled.
  ///
  /// In en, this message translates to:
  /// **'Disabled'**
  String get disabled;

  /// No description provided for @deleted.
  ///
  /// In en, this message translates to:
  /// **'Deleted'**
  String get deleted;

  /// No description provided for @enabled.
  ///
  /// In en, this message translates to:
  /// **'Enabled'**
  String get enabled;

  /// No description provided for @roleAdmin.
  ///
  /// In en, this message translates to:
  /// **'Administrator'**
  String get roleAdmin;

  /// No description provided for @roleManager.
  ///
  /// In en, this message translates to:
  /// **'Manager'**
  String get roleManager;

  /// No description provided for @roleHR.
  ///
  /// In en, this message translates to:
  /// **'Human Resources'**
  String get roleHR;

  /// No description provided for @roleEmployee.
  ///
  /// In en, this message translates to:
  /// **'Employee'**
  String get roleEmployee;

  /// No description provided for @roleUser.
  ///
  /// In en, this message translates to:
  /// **'User'**
  String get roleUser;

  /// No description provided for @departmentIT.
  ///
  /// In en, this message translates to:
  /// **'Information Technology'**
  String get departmentIT;

  /// No description provided for @departmentHR.
  ///
  /// In en, this message translates to:
  /// **'Human Resources'**
  String get departmentHR;

  /// No description provided for @departmentFinance.
  ///
  /// In en, this message translates to:
  /// **'Finance'**
  String get departmentFinance;

  /// No description provided for @departmentMarketing.
  ///
  /// In en, this message translates to:
  /// **'Marketing'**
  String get departmentMarketing;

  /// No description provided for @departmentOperations.
  ///
  /// In en, this message translates to:
  /// **'Operations'**
  String get departmentOperations;

  /// No description provided for @departmentSales.
  ///
  /// In en, this message translates to:
  /// **'Sales'**
  String get departmentSales;

  /// No description provided for @departmentSupport.
  ///
  /// In en, this message translates to:
  /// **'Support'**
  String get departmentSupport;

  /// No description provided for @searchUsers.
  ///
  /// In en, this message translates to:
  /// **'Search users...'**
  String get searchUsers;

  /// No description provided for @filterAndSort.
  ///
  /// In en, this message translates to:
  /// **'Filter & Sort'**
  String get filterAndSort;

  /// No description provided for @bulkActions.
  ///
  /// In en, this message translates to:
  /// **'Bulk Actions'**
  String get bulkActions;

  /// No description provided for @selectAll.
  ///
  /// In en, this message translates to:
  /// **'Select All'**
  String get selectAll;

  /// No description provided for @deselectAll.
  ///
  /// In en, this message translates to:
  /// **'Deselect All'**
  String get deselectAll;

  /// No description provided for @clearSelection.
  ///
  /// In en, this message translates to:
  /// **'Clear Selection'**
  String get clearSelection;

  /// No description provided for @usersSelected.
  ///
  /// In en, this message translates to:
  /// **'{count} users selected'**
  String usersSelected(int count);

  /// No description provided for @allRoles.
  ///
  /// In en, this message translates to:
  /// **'All Roles'**
  String get allRoles;

  /// No description provided for @allDepartments.
  ///
  /// In en, this message translates to:
  /// **'All Departments'**
  String get allDepartments;

  /// No description provided for @sortBy.
  ///
  /// In en, this message translates to:
  /// **'Sort By'**
  String get sortBy;

  /// No description provided for @sortByName.
  ///
  /// In en, this message translates to:
  /// **'Name'**
  String get sortByName;

  /// No description provided for @sortByEmail.
  ///
  /// In en, this message translates to:
  /// **'Email'**
  String get sortByEmail;

  /// No description provided for @sortByRole.
  ///
  /// In en, this message translates to:
  /// **'Role'**
  String get sortByRole;

  /// No description provided for @sortByDepartment.
  ///
  /// In en, this message translates to:
  /// **'Department'**
  String get sortByDepartment;

  /// No description provided for @sortByCreatedDate.
  ///
  /// In en, this message translates to:
  /// **'Created Date'**
  String get sortByCreatedDate;

  /// No description provided for @sortByLastLogin.
  ///
  /// In en, this message translates to:
  /// **'Last Login'**
  String get sortByLastLogin;

  /// No description provided for @sortAscending.
  ///
  /// In en, this message translates to:
  /// **'Ascending'**
  String get sortAscending;

  /// No description provided for @sortDescending.
  ///
  /// In en, this message translates to:
  /// **'Descending'**
  String get sortDescending;

  /// No description provided for @viewUser.
  ///
  /// In en, this message translates to:
  /// **'View User'**
  String get viewUser;

  /// No description provided for @editUserAction.
  ///
  /// In en, this message translates to:
  /// **'Edit User'**
  String get editUserAction;

  /// No description provided for @deleteUser.
  ///
  /// In en, this message translates to:
  /// **'Delete User'**
  String get deleteUser;

  /// No description provided for @restoreUser.
  ///
  /// In en, this message translates to:
  /// **'Restore User'**
  String get restoreUser;

  /// No description provided for @disableUser.
  ///
  /// In en, this message translates to:
  /// **'Disable User'**
  String get disableUser;

  /// No description provided for @enableUser.
  ///
  /// In en, this message translates to:
  /// **'Enable User'**
  String get enableUser;

  /// No description provided for @resetPassword.
  ///
  /// In en, this message translates to:
  /// **'Reset Password'**
  String get resetPassword;

  /// No description provided for @toggleUserStatus.
  ///
  /// In en, this message translates to:
  /// **'Toggle Status'**
  String get toggleUserStatus;

  /// No description provided for @confirmDeleteUser.
  ///
  /// In en, this message translates to:
  /// **'Are you sure you want to delete this user?'**
  String get confirmDeleteUser;

  /// No description provided for @confirmRestoreUser.
  ///
  /// In en, this message translates to:
  /// **'Are you sure you want to restore this user?'**
  String get confirmRestoreUser;

  /// No description provided for @confirmDisableUser.
  ///
  /// In en, this message translates to:
  /// **'Are you sure you want to disable this user?'**
  String get confirmDisableUser;

  /// No description provided for @confirmEnableUser.
  ///
  /// In en, this message translates to:
  /// **'Are you sure you want to enable this user?'**
  String get confirmEnableUser;

  /// No description provided for @confirmResetPassword.
  ///
  /// In en, this message translates to:
  /// **'Are you sure you want to reset this user\'s password?'**
  String get confirmResetPassword;

  /// No description provided for @bulkDelete.
  ///
  /// In en, this message translates to:
  /// **'Bulk Delete'**
  String get bulkDelete;

  /// No description provided for @bulkRestore.
  ///
  /// In en, this message translates to:
  /// **'Bulk Restore'**
  String get bulkRestore;

  /// No description provided for @bulkDisable.
  ///
  /// In en, this message translates to:
  /// **'Bulk Disable'**
  String get bulkDisable;

  /// No description provided for @bulkEnable.
  ///
  /// In en, this message translates to:
  /// **'Bulk Enable'**
  String get bulkEnable;

  /// No description provided for @confirmBulkDelete.
  ///
  /// In en, this message translates to:
  /// **'Are you sure you want to delete {count} users?'**
  String confirmBulkDelete(int count);

  /// No description provided for @confirmBulkRestore.
  ///
  /// In en, this message translates to:
  /// **'Are you sure you want to restore {count} users?'**
  String confirmBulkRestore(int count);

  /// No description provided for @userOverview.
  ///
  /// In en, this message translates to:
  /// **'User Overview'**
  String get userOverview;

  /// No description provided for @totalUsers.
  ///
  /// In en, this message translates to:
  /// **'Total Users'**
  String get totalUsers;

  /// No description provided for @activeUsers.
  ///
  /// In en, this message translates to:
  /// **'Active Users'**
  String get activeUsers;

  /// No description provided for @disabledUsers.
  ///
  /// In en, this message translates to:
  /// **'Disabled Users'**
  String get disabledUsers;

  /// No description provided for @deletedUsers.
  ///
  /// In en, this message translates to:
  /// **'Deleted Users'**
  String get deletedUsers;

  /// No description provided for @roleDistribution.
  ///
  /// In en, this message translates to:
  /// **'Role Distribution'**
  String get roleDistribution;

  /// No description provided for @analytics.
  ///
  /// In en, this message translates to:
  /// **'Analytics'**
  String get analytics;

  /// No description provided for @userGrowth.
  ///
  /// In en, this message translates to:
  /// **'User Growth'**
  String get userGrowth;

  /// No description provided for @userActivity.
  ///
  /// In en, this message translates to:
  /// **'User Activity'**
  String get userActivity;

  /// No description provided for @departmentBreakdown.
  ///
  /// In en, this message translates to:
  /// **'Department Breakdown'**
  String get departmentBreakdown;

  /// No description provided for @userCreatedSuccessfully.
  ///
  /// In en, this message translates to:
  /// **'User created successfully!'**
  String get userCreatedSuccessfully;

  /// No description provided for @userUpdatedSuccessfully.
  ///
  /// In en, this message translates to:
  /// **'User updated successfully!'**
  String get userUpdatedSuccessfully;

  /// No description provided for @userDeletedSuccessfully.
  ///
  /// In en, this message translates to:
  /// **'User deleted successfully!'**
  String get userDeletedSuccessfully;

  /// No description provided for @userRestoredSuccessfully.
  ///
  /// In en, this message translates to:
  /// **'User restored successfully!'**
  String get userRestoredSuccessfully;

  /// No description provided for @userDisabledSuccessfully.
  ///
  /// In en, this message translates to:
  /// **'User disabled successfully!'**
  String get userDisabledSuccessfully;

  /// No description provided for @userEnabledSuccessfully.
  ///
  /// In en, this message translates to:
  /// **'User enabled successfully!'**
  String get userEnabledSuccessfully;

  /// No description provided for @failedToCreateUser.
  ///
  /// In en, this message translates to:
  /// **'Failed to create user: {error}'**
  String failedToCreateUser(String error);

  /// No description provided for @failedToUpdateUser.
  ///
  /// In en, this message translates to:
  /// **'Failed to update user: {error}'**
  String failedToUpdateUser(String error);

  /// No description provided for @failedToDeleteUser.
  ///
  /// In en, this message translates to:
  /// **'Failed to delete user: {error}'**
  String failedToDeleteUser(String error);

  /// No description provided for @failedToLoadUsers.
  ///
  /// In en, this message translates to:
  /// **'Failed to load users: {error}'**
  String failedToLoadUsers(String error);

  /// No description provided for @noUsersFound.
  ///
  /// In en, this message translates to:
  /// **'No users found'**
  String get noUsersFound;

  /// No description provided for @loadingUsers.
  ///
  /// In en, this message translates to:
  /// **'Loading users...'**
  String get loadingUsers;

  /// No description provided for @refreshUsers.
  ///
  /// In en, this message translates to:
  /// **'Refresh Users'**
  String get refreshUsers;

  /// No description provided for @addUser.
  ///
  /// In en, this message translates to:
  /// **'Add User'**
  String get addUser;

  /// No description provided for @firstNameRequired.
  ///
  /// In en, this message translates to:
  /// **'First name is required'**
  String get firstNameRequired;

  /// No description provided for @lastNameRequired.
  ///
  /// In en, this message translates to:
  /// **'Last name is required'**
  String get lastNameRequired;

  /// No description provided for @emailRequired.
  ///
  /// In en, this message translates to:
  /// **'Email is required'**
  String get emailRequired;

  /// No description provided for @emailInvalid.
  ///
  /// In en, this message translates to:
  /// **'Please enter a valid email address'**
  String get emailInvalid;

  /// No description provided for @passwordRequired.
  ///
  /// In en, this message translates to:
  /// **'Password is required'**
  String get passwordRequired;

  /// No description provided for @passwordTooShort.
  ///
  /// In en, this message translates to:
  /// **'Password must be at least 8 characters'**
  String get passwordTooShort;

  /// No description provided for @passwordTooWeak.
  ///
  /// In en, this message translates to:
  /// **'Password must contain uppercase, lowercase, number and special character'**
  String get passwordTooWeak;

  /// No description provided for @phoneInvalid.
  ///
  /// In en, this message translates to:
  /// **'Please enter a valid phone number'**
  String get phoneInvalid;

  /// No description provided for @roleRequired.
  ///
  /// In en, this message translates to:
  /// **'Role is required'**
  String get roleRequired;

  /// No description provided for @departmentRequired.
  ///
  /// In en, this message translates to:
  /// **'Department is required'**
  String get departmentRequired;

  /// No description provided for @requiredField.
  ///
  /// In en, this message translates to:
  /// **'Required field'**
  String get requiredField;

  /// No description provided for @optionalField.
  ///
  /// In en, this message translates to:
  /// **'Optional field'**
  String get optionalField;

  /// No description provided for @enterFirstName.
  ///
  /// In en, this message translates to:
  /// **'Enter first name'**
  String get enterFirstName;

  /// No description provided for @enterLastName.
  ///
  /// In en, this message translates to:
  /// **'Enter last name'**
  String get enterLastName;

  /// No description provided for @enterEmail.
  ///
  /// In en, this message translates to:
  /// **'Enter email address'**
  String get enterEmail;

  /// No description provided for @enterPassword.
  ///
  /// In en, this message translates to:
  /// **'Enter password'**
  String get enterPassword;

  /// No description provided for @enterPhoneNumber.
  ///
  /// In en, this message translates to:
  /// **'Enter phone number'**
  String get enterPhoneNumber;

  /// No description provided for @selectRole.
  ///
  /// In en, this message translates to:
  /// **'Select role'**
  String get selectRole;

  /// No description provided for @selectDepartment.
  ///
  /// In en, this message translates to:
  /// **'Select department'**
  String get selectDepartment;

  /// No description provided for @enterPosition.
  ///
  /// In en, this message translates to:
  /// **'Enter position'**
  String get enterPosition;

  /// No description provided for @enterOrganization.
  ///
  /// In en, this message translates to:
  /// **'Enter organization'**
  String get enterOrganization;

  /// No description provided for @personalInformation.
  ///
  /// In en, this message translates to:
  /// **'Personal Information'**
  String get personalInformation;

  /// No description provided for @workInformation.
  ///
  /// In en, this message translates to:
  /// **'Work Information'**
  String get workInformation;

  /// No description provided for @roleAndStatus.
  ///
  /// In en, this message translates to:
  /// **'Role & Status'**
  String get roleAndStatus;

  /// No description provided for @saveChanges.
  ///
  /// In en, this message translates to:
  /// **'Save Changes'**
  String get saveChanges;

  /// No description provided for @roleManagement.
  ///
  /// In en, this message translates to:
  /// **'Role Management'**
  String get roleManagement;

  /// No description provided for @departmentManagement.
  ///
  /// In en, this message translates to:
  /// **'Department Management'**
  String get departmentManagement;

  /// No description provided for @createNewRole.
  ///
  /// In en, this message translates to:
  /// **'Create New Role'**
  String get createNewRole;

  /// No description provided for @editRole.
  ///
  /// In en, this message translates to:
  /// **'Edit Role'**
  String get editRole;

  /// No description provided for @createNewDepartment.
  ///
  /// In en, this message translates to:
  /// **'Create New Department'**
  String get createNewDepartment;

  /// No description provided for @editDepartment.
  ///
  /// In en, this message translates to:
  /// **'Edit Department'**
  String get editDepartment;

  /// No description provided for @roleName.
  ///
  /// In en, this message translates to:
  /// **'Role Name'**
  String get roleName;

  /// No description provided for @departmentName.
  ///
  /// In en, this message translates to:
  /// **'Department Name'**
  String get departmentName;

  /// No description provided for @departmentCode.
  ///
  /// In en, this message translates to:
  /// **'Department Code'**
  String get departmentCode;

  /// No description provided for @departmentDescription.
  ///
  /// In en, this message translates to:
  /// **'Department Description'**
  String get departmentDescription;

  /// No description provided for @parentDepartment.
  ///
  /// In en, this message translates to:
  /// **'Parent Department'**
  String get parentDepartment;

  /// No description provided for @noParentTopLevel.
  ///
  /// In en, this message translates to:
  /// **'No Parent (Top Level)'**
  String get noParentTopLevel;

  /// No description provided for @selectParentDepartment.
  ///
  /// In en, this message translates to:
  /// **'Select parent department (optional)'**
  String get selectParentDepartment;

  /// No description provided for @noDepartment.
  ///
  /// In en, this message translates to:
  /// **'No Department'**
  String get noDepartment;

  /// No description provided for @basicInformation.
  ///
  /// In en, this message translates to:
  /// **'Basic Information'**
  String get basicInformation;

  /// No description provided for @hierarchy.
  ///
  /// In en, this message translates to:
  /// **'Hierarchy'**
  String get hierarchy;

  /// No description provided for @departmentStatus.
  ///
  /// In en, this message translates to:
  /// **'Department Status'**
  String get departmentStatus;

  /// No description provided for @enable.
  ///
  /// In en, this message translates to:
  /// **'Enable'**
  String get enable;

  /// No description provided for @disable.
  ///
  /// In en, this message translates to:
  /// **'Disable'**
  String get disable;

  /// No description provided for @restore.
  ///
  /// In en, this message translates to:
  /// **'Restore'**
  String get restore;

  /// No description provided for @deleteRole.
  ///
  /// In en, this message translates to:
  /// **'Delete Role'**
  String get deleteRole;

  /// No description provided for @deleteDepartment.
  ///
  /// In en, this message translates to:
  /// **'Delete Department'**
  String get deleteDepartment;

  /// No description provided for @deleteRoleConfirm.
  ///
  /// In en, this message translates to:
  /// **'Are you sure you want to delete the role \"{roleName}\"?'**
  String deleteRoleConfirm(Object roleName);

  /// No description provided for @deleteDepartmentConfirm.
  ///
  /// In en, this message translates to:
  /// **'Are you sure you want to delete the department \"{departmentName}\"?'**
  String deleteDepartmentConfirm(Object departmentName);

  /// No description provided for @roleCreatedSuccessfully.
  ///
  /// In en, this message translates to:
  /// **'Role created successfully'**
  String get roleCreatedSuccessfully;

  /// No description provided for @roleUpdatedSuccessfully.
  ///
  /// In en, this message translates to:
  /// **'Role updated successfully'**
  String get roleUpdatedSuccessfully;

  /// No description provided for @roleDeletedSuccessfully.
  ///
  /// In en, this message translates to:
  /// **'Role deleted successfully'**
  String get roleDeletedSuccessfully;

  /// No description provided for @departmentCreatedSuccessfully.
  ///
  /// In en, this message translates to:
  /// **'Department created successfully'**
  String get departmentCreatedSuccessfully;

  /// No description provided for @departmentUpdatedSuccessfully.
  ///
  /// In en, this message translates to:
  /// **'Department updated successfully'**
  String get departmentUpdatedSuccessfully;

  /// No description provided for @departmentDeletedSuccessfully.
  ///
  /// In en, this message translates to:
  /// **'Department deleted successfully'**
  String get departmentDeletedSuccessfully;

  /// No description provided for @departmentRestoredSuccessfully.
  ///
  /// In en, this message translates to:
  /// **'Department restored successfully'**
  String get departmentRestoredSuccessfully;

  /// No description provided for @departmentStatusUpdatedSuccessfully.
  ///
  /// In en, this message translates to:
  /// **'Department status updated successfully'**
  String get departmentStatusUpdatedSuccessfully;

  /// No description provided for @noRolesFound.
  ///
  /// In en, this message translates to:
  /// **'No roles found'**
  String get noRolesFound;

  /// No description provided for @noDepartmentsFound.
  ///
  /// In en, this message translates to:
  /// **'No departments found'**
  String get noDepartmentsFound;

  /// No description provided for @createFirstRole.
  ///
  /// In en, this message translates to:
  /// **'Create your first role to get started'**
  String get createFirstRole;

  /// No description provided for @createFirstDepartment.
  ///
  /// In en, this message translates to:
  /// **'Create your first department to get started'**
  String get createFirstDepartment;

  /// No description provided for @addRole.
  ///
  /// In en, this message translates to:
  /// **'Add Role'**
  String get addRole;

  /// No description provided for @addDepartment.
  ///
  /// In en, this message translates to:
  /// **'Add Department'**
  String get addDepartment;

  /// No description provided for @updateRole.
  ///
  /// In en, this message translates to:
  /// **'Update Role'**
  String get updateRole;

  /// No description provided for @updateDepartment.
  ///
  /// In en, this message translates to:
  /// **'Update Department'**
  String get updateDepartment;

  /// No description provided for @createRole.
  ///
  /// In en, this message translates to:
  /// **'Create Role'**
  String get createRole;

  /// No description provided for @createDepartment.
  ///
  /// In en, this message translates to:
  /// **'Create Department'**
  String get createDepartment;

  /// No description provided for @searchRoles.
  ///
  /// In en, this message translates to:
  /// **'Search roles...'**
  String get searchRoles;

  /// No description provided for @searchDepartments.
  ///
  /// In en, this message translates to:
  /// **'Search departments...'**
  String get searchDepartments;

  /// No description provided for @includeDeleted.
  ///
  /// In en, this message translates to:
  /// **'Include Deleted'**
  String get includeDeleted;

  /// No description provided for @roleInformation.
  ///
  /// In en, this message translates to:
  /// **'Role Information'**
  String get roleInformation;

  /// No description provided for @roleDetails.
  ///
  /// In en, this message translates to:
  /// **'Role Details'**
  String get roleDetails;

  /// No description provided for @created.
  ///
  /// In en, this message translates to:
  /// **'Created'**
  String get created;

  /// No description provided for @lastUpdated.
  ///
  /// In en, this message translates to:
  /// **'Last Updated'**
  String get lastUpdated;

  /// No description provided for @roleId.
  ///
  /// In en, this message translates to:
  /// **'Role ID'**
  String get roleId;

  /// No description provided for @departmentId.
  ///
  /// In en, this message translates to:
  /// **'Department ID'**
  String get departmentId;

  /// No description provided for @enterRoleName.
  ///
  /// In en, this message translates to:
  /// **'Enter role name (e.g., Manager, HR, Developer)'**
  String get enterRoleName;

  /// No description provided for @enterDepartmentName.
  ///
  /// In en, this message translates to:
  /// **'Enter department name (e.g., Human Resources, IT)'**
  String get enterDepartmentName;

  /// No description provided for @enterDepartmentCode.
  ///
  /// In en, this message translates to:
  /// **'Enter department code (e.g., HR, IT, FIN)'**
  String get enterDepartmentCode;

  /// No description provided for @enterDepartmentDescription.
  ///
  /// In en, this message translates to:
  /// **'Enter department description (optional)'**
  String get enterDepartmentDescription;

  /// No description provided for @roleNameRequired.
  ///
  /// In en, this message translates to:
  /// **'Role name is required'**
  String get roleNameRequired;

  /// No description provided for @departmentNameRequired.
  ///
  /// In en, this message translates to:
  /// **'Department name is required'**
  String get departmentNameRequired;

  /// No description provided for @roleNameMinLength.
  ///
  /// In en, this message translates to:
  /// **'Role name must be at least 2 characters'**
  String get roleNameMinLength;

  /// No description provided for @departmentNameMinLength.
  ///
  /// In en, this message translates to:
  /// **'Department name must be at least 2 characters'**
  String get departmentNameMinLength;

  /// No description provided for @roleNameMaxLength.
  ///
  /// In en, this message translates to:
  /// **'Role name must be less than 50 characters'**
  String get roleNameMaxLength;

  /// No description provided for @departmentNameMaxLength.
  ///
  /// In en, this message translates to:
  /// **'Department name must be less than 100 characters'**
  String get departmentNameMaxLength;

  /// No description provided for @departmentCodeMinLength.
  ///
  /// In en, this message translates to:
  /// **'Department code must be at least 2 characters'**
  String get departmentCodeMinLength;

  /// No description provided for @departmentCodeMaxLength.
  ///
  /// In en, this message translates to:
  /// **'Department code must be less than 10 characters'**
  String get departmentCodeMaxLength;

  /// No description provided for @roleNameInvalidCharacters.
  ///
  /// In en, this message translates to:
  /// **'Role name contains invalid characters'**
  String get roleNameInvalidCharacters;

  /// No description provided for @departmentCodeInvalidCharacters.
  ///
  /// In en, this message translates to:
  /// **'Department code contains invalid characters'**
  String get departmentCodeInvalidCharacters;

  /// No description provided for @roleNameAlreadyExists.
  ///
  /// In en, this message translates to:
  /// **'Role name already exists'**
  String get roleNameAlreadyExists;

  /// No description provided for @departmentNameAlreadyExists.
  ///
  /// In en, this message translates to:
  /// **'Department name already exists'**
  String get departmentNameAlreadyExists;

  /// No description provided for @departmentCodeAlreadyExists.
  ///
  /// In en, this message translates to:
  /// **'Department code already exists'**
  String get departmentCodeAlreadyExists;

  /// No description provided for @changingRoleNameWarning.
  ///
  /// In en, this message translates to:
  /// **'Changing the role name may affect users assigned to this role.'**
  String get changingRoleNameWarning;

  /// No description provided for @roleAndDepartmentInfo.
  ///
  /// In en, this message translates to:
  /// **'Role names and codes should be unique. They will be used throughout the system for access control.'**
  String get roleAndDepartmentInfo;

  /// No description provided for @departmentHierarchyInfo.
  ///
  /// In en, this message translates to:
  /// **'Department names and codes should be unique. You can create a hierarchy by selecting a parent department.'**
  String get departmentHierarchyInfo;
}

class _AppLocalizationsDelegate
    extends LocalizationsDelegate<AppLocalizations> {
  const _AppLocalizationsDelegate();

  @override
  Future<AppLocalizations> load(Locale locale) {
    return SynchronousFuture<AppLocalizations>(lookupAppLocalizations(locale));
  }

  @override
  bool isSupported(Locale locale) =>
      <String>['en', 'vi'].contains(locale.languageCode);

  @override
  bool shouldReload(_AppLocalizationsDelegate old) => false;
}

AppLocalizations lookupAppLocalizations(Locale locale) {
  // Lookup logic when only language code is specified.
  switch (locale.languageCode) {
    case 'en':
      return AppLocalizationsEn();
    case 'vi':
      return AppLocalizationsVi();
  }

  throw FlutterError(
    'AppLocalizations.delegate failed to load unsupported locale "$locale". This is likely '
    'an issue with the localizations generation tool. Please file an issue '
    'on GitHub with a reproducible sample app and the gen-l10n configuration '
    'that was used.',
  );
}
