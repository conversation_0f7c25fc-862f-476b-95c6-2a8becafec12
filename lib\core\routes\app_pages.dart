import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:golderhr/core/extensions/l10n_extension.dart';
import 'package:golderhr/core/routes/app_routes.dart';
import 'package:golderhr/features/auth/presentation/pages/forgot_password_page.dart';
import 'package:golderhr/features/auth/presentation/pages/login_page.dart';
import 'package:golderhr/features/auth/presentation/pages/new_password_page.dart';
import 'package:golderhr/features/auth/presentation/pages/otp_page.dart';
import 'package:golderhr/features/auth/presentation/pages/register_page.dart';
import 'package:golderhr/features/customer/presentation/pages/customer_page.dart';
import 'package:golderhr/features/home/<USER>/pages/home_page.dart';
import 'package:golderhr/features/more/presentation/pages/more_page.dart';
import 'package:golderhr/features/notification/presentation/pages/notification_page.dart';

import 'package:golderhr/features/profile/presentation/pages/profile_page.dart';
import 'package:golderhr/features/profile/presentation/pages/edit_profile_page.dart';
import 'package:golderhr/features/profile/presentation/pages/change_password_page.dart';
import 'package:golderhr/features/setting/presentation/pages/setting_page.dart';
import 'package:golderhr/features/work/presentation/pages/work_page.dart';

import '../../features/attendance/presentation/pages/attendance_history_page.dart';
import '../../features/attendance/presentation/pages/attendance_page.dart';
import '../../features/calendar/presentation/pages/calendar_main_page.dart';
import '../../features/faceDetection/presentation/pages/face_detection_page.dart';
import '../../features/leave/presentation/pages/leave_view.dart';
import '../../features/message/presentation/pages/message_page.dart';
import '../../features/navigationBar/main_screen.dart';
import '../../features/notification/data/model/notification_model.dart';
import '../../features/notification/presentation/pages/notification_detail_page.dart';
import '../../features/overtime/presentation/pages/overtime_view.dart';
import '../../features/overtime/presentation/pages/overtime_admin_page.dart';
import '../../features/leave/presentation/pages/leave_admin_page.dart';
import '../../features/overtime/presentation/cubit/overtime_cubit.dart';
import '../../features/leave/presentation/cubit/leave_cubit.dart';
import '../../features/profile/presentation/cubit/profile_cubit.dart';
import '../../features/upload_employee_face/presentation/page/upload_employee_face_page.dart';
import '../../features/admin/presentation/pages/admin_user_page.dart';
import '../../features/role/presentation/pages/role_management_page.dart';
import '../../features/department/presentation/pages/department_management_page.dart';
import '../../features/role/presentation/cubit/role_cubit.dart';
import '../../features/department/presentation/cubit/department_cubit.dart';
import '../../injection_container.dart';

final GoRouter appRouter = GoRouter(
  initialLocation: AppRoutes.login,
  routes: [
    GoRoute(
      path: AppRoutes.login,
      name: AppRoutes.login,
      builder: (context, state) => const LoginPage(),
    ),
    GoRoute(
      path: AppRoutes.register,
      name: AppRoutes.register,
      builder: (context, state) => const RegisterPage(),
    ),

    GoRoute(
      path: AppRoutes.forgotPassword,
      name: AppRoutes.forgotPassword,
      builder: (context, state) => const ForgotPasswordPage(),
    ),

    GoRoute(
      path: AppRoutes.verifyOtp,
      name: AppRoutes.verifyOtp,
      builder: (context, state) => const OtpPage(),
    ),

    GoRoute(
      path: AppRoutes.newPassword,
      name: AppRoutes.newPassword,
      builder: (context, state) => const NewPasswordPage(),
    ),

    GoRoute(
      path: AppRoutes.profile,
      name: AppRoutes.profile,
      builder: (context, state) => BlocProvider<ProfileCubit>(
        create: (_) => sl<ProfileCubit>(),
        child: const ProfilePage(),
      ),
    ),
    GoRoute(
      path: AppRoutes.editProfile,
      name: AppRoutes.editProfile,
      builder: (context, state) => BlocProvider<ProfileCubit>(
        create: (_) => sl<ProfileCubit>(),
        child: const EditProfilePage(),
      ),
    ),
    GoRoute(
      path: AppRoutes.changePassword,
      name: AppRoutes.changePassword,
      builder: (context, state) => BlocProvider<ProfileCubit>(
        create: (_) => sl<ProfileCubit>(),
        child: const ChangePasswordPage(),
      ),
    ),
    GoRoute(
      path: AppRoutes.setting,
      name: AppRoutes.setting,
      builder: (context, state) => const SettingPage(),
    ),
    GoRoute(
      path: AppRoutes.attendance,
      name: AppRoutes.attendance,
      builder: (context, state) => const AttendancePage(),
    ),

    GoRoute(
      path: AppRoutes.attendanceHistory,
      name: AppRoutes.attendanceHistory,
      builder: (context, state) => const AttendanceHistoryPage(),
    ),

    GoRoute(
      path: AppRoutes.overtimeRequest,
      name: AppRoutes.overtimeRequest,
      builder: (context, state) => BlocProvider(
        create: (_) => sl<OvertimeCubit>()..loadInitialData(),
        child: const OvertimeView(),
      ),
    ),

    GoRoute(
      path: AppRoutes.overtimeAdmin,
      name: AppRoutes.overtimeAdmin,
      builder: (context, state) => const OvertimeAdminPage(),
    ),

    GoRoute(
      path: AppRoutes.leaveAdmin,
      name: AppRoutes.leaveAdmin,
      builder: (context, state) => const LeaveAdminPage(),
    ),

    GoRoute(
      path: '/face-detection/:mode',
      name: 'faceDetection',
      builder: (context, state) {
        final modeString = state.pathParameters['mode'] ?? 'checkIn';
        final mode = modeString == 'checkOut'
            ? AttendanceMode.checkOut
            : AttendanceMode.checkIn;
        return FaceDetectionPage(mode: mode);
      },
    ),
    GoRoute(
      path: AppRoutes.message,
      name: AppRoutes.message,
      builder: (context, state) => const MessagePage(),
    ),

    GoRoute(
      path: AppRoutes.leave,
      name: AppRoutes.leave,
      builder: (context, state) => BlocProvider(
        create: (_) => sl<LeaveCubit>()..loadLeaveData(l10n: context.l10n),
        child: const LeaveView(),
      ),
    ),

    GoRoute(
      path: AppRoutes.calendar,
      name: AppRoutes.calendar,
      builder: (context, state) => const CalendarPage(),
    ),

    // admin
    GoRoute(
      path: AppRoutes.uploadEmployeeFace,
      name: AppRoutes.uploadEmployeeFace,
      builder: (context, state) => const UploadEmployeeFacePage(),
    ),

    GoRoute(
      path: AppRoutes.adminUserManagement,
      name: AppRoutes.adminUserManagement,
      builder: (context, state) => const AdminUserPage(),
    ),

    GoRoute(
      path: AppRoutes.roleManagement,
      name: AppRoutes.roleManagement,
      builder: (context, state) => BlocProvider(
        create: (context) => sl<RoleCubit>(),
        child: const RoleManagementPage(),
      ),
    ),

    GoRoute(
      path: AppRoutes.departmentManagement,
      name: AppRoutes.departmentManagement,
      builder: (context, state) => BlocProvider(
        create: (context) => sl<DepartmentCubit>(),
        child: const DepartmentManagementPage(),
      ),
    ),

    StatefulShellRoute.indexedStack(
      builder: (context, state, navigationShell) {
        return MainScreen(navigationShell: navigationShell);
      },
      branches: <StatefulShellBranch>[
        StatefulShellBranch(
          routes: <RouteBase>[
            GoRoute(
              path: AppRoutes.home,
              name: 'home',
              builder: (context, state) => const HomePage(),
            ),
          ],
        ),

        StatefulShellBranch(
          routes: <RouteBase>[
            GoRoute(
              path: AppRoutes.customers,
              name: 'customer',
              builder: (context, state) => const CustomerPage(),
            ),
          ],
        ),

        StatefulShellBranch(
          routes: <RouteBase>[
            GoRoute(
              path: AppRoutes.work, // Hãy định nghĩa path này trong AppRoutes
              name: 'work',
              builder: (context, state) => const WorkPage(),
            ),
          ],
        ),

        StatefulShellBranch(
          routes: <RouteBase>[
            GoRoute(
              path: AppRoutes.notifications,
              name: AppRoutes.notifications,
              builder: (context, state) => const NotificationPage(),
              // Các trang con của Thông báo (như Chi tiết) được định nghĩa ở đây
              routes: [
                GoRoute(
                  // Path này sẽ được GoRouter hiểu là '/notifications/detail/:id'
                  path: '${AppRoutes.notificationDetailRelative}/:id',
                  name: AppRoutes.notificationDetailRelative,
                  // Trang chi tiết sẽ được đẩy LÊN TRÊN Shell, che mất NavBar, là hành vi đúng.
                  builder: (context, state) {
                    final notification = state.extra as NotificationModel;
                    return NotificationDetailPage(notification: notification);
                  },
                ),
              ],
            ),
          ],
        ),

        StatefulShellBranch(
          routes: <RouteBase>[
            GoRoute(
              path: AppRoutes.more, // Hãy định nghĩa path này trong AppRoutes
              name: AppRoutes.more,
              builder: (context, state) => const MorePage(),
            ),
          ],
        ),
      ],
    ),
  ],
);
