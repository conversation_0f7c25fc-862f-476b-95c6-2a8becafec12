import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:golderhr/core/extensions/app_theme_extension.dart';
import 'package:golderhr/core/extensions/l10n_extension.dart';
import 'package:golderhr/core/extensions/responsive_extension.dart';
import 'package:golderhr/core/routes/app_routes.dart';
import 'package:golderhr/features/auth/presentation/cubit/auth_cubit.dart';
import 'package:golderhr/features/auth/presentation/cubit/auth_state.dart';
import 'package:golderhr/features/auth/presentation/widgets/field_label.dart';
import 'package:golderhr/shared/theme/app_colors.dart';
import 'package:golderhr/shared/utils/validators.dart';
import 'package:golderhr/shared/widgets/button_custom.dart';
import 'package:golderhr/shared/widgets/responsive_spacer.dart';
import 'package:golderhr/shared/widgets/text_field_custom.dart';

import '../../../../core/responsive/responsive.dart';
import '../../../../shared/widgets/show_custom_snackBar.dart';

// => bổ sung: Mock Responsive class

class RegisterForm extends StatefulWidget {
  const RegisterForm({super.key});

  @override
  State<RegisterForm> createState() => _RegisterFormState();
}

class _RegisterFormState extends State<RegisterForm> {
  final _formKey = GlobalKey<FormState>();
  final _fullNameController = TextEditingController();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();

  // => bổ sung: Add acceptTerms ValueNotifier
  final acceptTerms = ValueNotifier<bool>(false);

  final _fullNameFocusNode = FocusNode();
  final _emailFocusNode = FocusNode();
  final _passwordFocusNode = FocusNode();
  final _confirmPasswordFocusNode = FocusNode();
  bool _isPasswordObscured = true;
  bool _isConfirmPasswordObscured = true;

  @override
  void dispose() {
    _fullNameController.dispose();
    _emailController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    _fullNameFocusNode.dispose();
    _emailFocusNode.dispose();
    _passwordFocusNode.dispose();
    _confirmPasswordFocusNode.dispose();
    // => bổ sung: Dispose acceptTerms
    acceptTerms.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Form(
      key: _formKey,
      autovalidateMode: AutovalidateMode.onUserInteraction,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          FieldLabel(label: context.l10n.registerFullName),
          ResponsiveSpacer(
            mobileSize: 4,
            tabletSize: 4,
            mobileLandscapeSize: 4,
            tabletLandscapeSize: 4,
          ),
          _buildFullNameField(),
          ResponsiveSpacer(
            mobileSize: 10,
            tabletSize: 10,
            mobileLandscapeSize: 10,
            tabletLandscapeSize: 10,
          ),
          FieldLabel(label: context.l10n.registerEmail),
          _buildEmailField(),
          ResponsiveSpacer(
            mobileSize: 10,
            tabletSize: 10,
            mobileLandscapeSize: 10,
            tabletLandscapeSize: 10,
          ),
          FieldLabel(label: context.l10n.registerPassword),
          ResponsiveSpacer(
            mobileSize: 4,
            tabletSize: 4,
            mobileLandscapeSize: 4,
          ),
          _buildPasswordField(),
          ResponsiveSpacer(
            mobileSize: 10,
            tabletSize: 10,
            mobileLandscapeSize: 10,
            tabletLandscapeSize: 10,
          ),
          FieldLabel(label: context.l10n.registerConfirmPassword),
          ResponsiveSpacer(
            mobileSize: 4,
            tabletSize: 4,
            mobileLandscapeSize: 4,
          ),
          _buildConfirmPasswordField(),
          ResponsiveSpacer(
            mobileSize: 4,
            tabletSize: 4,
            mobileLandscapeSize: 4,
          ),
          // => bổ sung: Add terms checkbox
          ValueListenableBuilder<bool>(
            valueListenable: acceptTerms,
            builder: (context, value, child) {
              return CheckboxListTile(
                title: Text(
                  context.l10n.acceptTerms,
                  style: context.lightTheme.textTheme.bodyMedium,
                ),
                value: value,
                checkboxScaleFactor: context.responsive.fontSize(1),
                onChanged: (newValue) {
                  acceptTerms.value = newValue!;
                },
              );
            },
          ),
          ResponsiveSpacer(
            mobileSize: 10,
            tabletSize: 15,
            mobileLandscapeSize: 15,
            tabletLandscapeSize: 15,
          ),
          _buildRegisterButton(Responsive.of(context)),
        ],
      ),
    );
  }

  Widget _buildEmailField() {
    final responsive = Responsive.of(context);
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(responsive.scaleRadius(12)),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha((0.04 * 255).round()),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: TextFieldCustom(
        hintText: context.l10n.registerEmail,
        controller: _emailController,
        prefixIcon: Icons.email_outlined,
        validator: (value) => Validators.validateEmail(value, context),
        focusNode: _emailFocusNode,
      ),
    );
  }

  Widget _buildFullNameField() {
    final responsive = Responsive.of(context);
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(responsive.scaleRadius(12)),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha((0.04 * 255).round()),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: TextFieldCustom(
        hintText: context.l10n.registerFullName,
        controller: _fullNameController,
        prefixIcon: Icons.person_2_outlined,
        validator: (value) => Validators.validateFullName(value, context),
        focusNode: _fullNameFocusNode,
      ),
    );
  }

  Widget _buildPasswordField() {
    final responsive = Responsive.of(context);
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(responsive.scaleRadius(12)),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha((0.04 * 255).round()),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: TextFieldCustom(
        hintText: context.l10n.registerPassword,
        controller: _passwordController,
        prefixIcon: Icons.lock_outline_rounded,
        suffixIcon: _isPasswordObscured
            ? Icons.visibility_off_outlined
            : Icons.visibility_outlined,
        obscureText: _isPasswordObscured,
        validator: (value) => Validators.validatePassword(value, context),
        focusNode: _passwordFocusNode,
        onSuffixIconPressed: () =>
            setState(() => _isPasswordObscured = !_isPasswordObscured),
      ),
    );
  }

  Widget _buildConfirmPasswordField() {
    final responsive = Responsive.of(context);
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(responsive.scaleRadius(12)),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha((0.04 * 255).round()),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: TextFieldCustom(
        hintText: context.l10n.registerConfirmPassword,
        controller: _confirmPasswordController,
        prefixIcon: Icons.lock_outline_rounded,
        suffixIcon: _isConfirmPasswordObscured
            ? Icons.visibility_off_outlined
            : Icons.visibility_outlined,
        obscureText: _isConfirmPasswordObscured,
        validator: (value) => Validators.validateConfirmPassword(
          value,
          _passwordController.text,
          context,
        ),
        focusNode: _confirmPasswordFocusNode,
        onSuffixIconPressed: () => setState(
          () => _isConfirmPasswordObscured = !_isConfirmPasswordObscured,
        ),
      ),
    );
  }

  Widget _buildRegisterButton(Responsive responsive) {
    final buttonHeight = responsive.adaptiveValue(
      mobile: responsive.scaleHeight(52),
      tablet: responsive.scaleHeight(60),
      mobileLandscape: responsive.scaleHeight(52, landscapeScaleFactor: 1.7),
      tabletLandscape: responsive.scaleHeight(60),
    );
    return ValueListenableBuilder<bool>(
      valueListenable: acceptTerms,
      builder: (context, acceptTermsValue, child) {
        return BlocConsumer<AuthCubit, AuthState>(
          listener: (context, state) {
            if (state is AuthError) {
              showTopSnackBar(
                context,
                title: context.l10n.error,
                message: state.message,
                isError: true,
              );
            } else if (state is Authenticated) {
              showTopSnackBar(
                context,
                title: context.l10n.success,
                message: context.l10n.registerSuccess,
              );

              context.goNamed(AppRoutes.login);
            }
          },
          builder: (context, state) {
            final isLoading = state is AuthLoading;
            return Container(
              height: buttonHeight,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: AppColors.primary.withAlpha((0.3 * 255).round()),
                    blurRadius: 12,
                    offset: const Offset(0, 6),
                  ),
                ],
              ),
              child: ButtonCustom(
                text: context.l10n.loginSignUp,
                isLoading: isLoading,
                onPressed: (isLoading || !acceptTermsValue)
                    ? null
                    : () async {
                        if (_formKey.currentState!.validate()) {
                          FocusScope.of(context).unfocus();
                          await context.read<AuthCubit>().register(
                            fullName: _fullNameController.text.trim(),
                            email: _emailController.text.trim(),
                            password: _passwordController.text.trim(),
                          );
                          if (context.read<AuthCubit>().state
                              is Authenticated) {
                            _formKey.currentState!.reset();
                            _fullNameController.clear();
                            _emailController.clear();
                            _passwordController.clear();
                            _confirmPasswordController.clear();
                            acceptTerms.value = false;
                            _isPasswordObscured = true;
                            _isConfirmPasswordObscured = true;
                          }
                        }
                      },
              ),
            );
          },
        );
      },
    );
  }
}
