import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:golderhr/features/auth/domain/entities/user_entity.dart';
import 'package:golderhr/shared/widgets/responsive_spacer.dart';
import 'package:iconsax/iconsax.dart';

import '../../../../core/routes/app_routes.dart';
import '../../../../shared/widgets/action_item_model.dart';
import '../../../cubit/user_cubit.dart';
import '../../../faceDetection/presentation/cubit/face_checkin_cubit.dart';
import '../../../more/presentation/pages/more_page.dart';
import '../../../notification/data/model/notification_model.dart';
import '../widgets/attendance_section.dart';
import '../widgets/chat_head_widget.dart';
import '../widgets/greeting_section.dart';
import '../widgets/home_announcements_section.dart';
import '../widgets/home_app_bar.dart';
import '../widgets/home_top_actions.dart';
import '../widgets/quick_stats_section.dart';

final List<NotificationModel> mockAnnouncements = [
  NotificationModel(
    id: '1',
    title: 'Cập nhật chính sách nghỉ phép năm 2024',
    message: 'Nội dung chi tiết về chính sách mới...',
    icon: Iconsax.document_text_1,
    color: Colors.blue.shade700,
    timestamp: DateTime.now().subtract(const Duration(hours: 5)),
    category: NotificationCategory.customer,
  ),
  NotificationModel(
    id: '2',
    title: 'Thông báo bảo trì hệ thống định kỳ',
    message: 'Hệ thống sẽ tạm ngưng từ 2:00-3:00 sáng...',
    icon: Iconsax.monitor_mobbile,
    color: Colors.orange.shade700,
    timestamp: DateTime.now().subtract(const Duration(days: 1)),
    category: NotificationCategory.customer,
  ),
  NotificationModel(
    id: '3',
    title: 'Tiệc cuối năm và Team Building',
    message: 'Mọi người hãy đăng ký tham gia nhé!',
    icon: Icons.celebration_rounded,
    color: Colors.pink.shade500,
    timestamp: DateTime.now().subtract(const Duration(days: 2)),
    category: NotificationCategory.customer,
  ),
];

class HomeView extends StatefulWidget {
  const HomeView({super.key});

  @override
  State<HomeView> createState() => _HomeViewState();
}

class _HomeViewState extends State<HomeView> with WidgetsBindingObserver {
  Offset _chatHeadPosition = const Offset(10, 100);
  final bool _isChatHeadVisible = true;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    // Refresh dữ liệu khi app quay về foreground
    if (state == AppLifecycleState.resumed) {
      _refreshAttendanceData();
    }
  }

  Future<void> _refreshAttendanceData() async {
    // Refresh dữ liệu attendance khi quay về trang home
    final faceDetectionCubit = context.read<FaceDetectionCubit>();
    await faceDetectionCubit.checkAttendanceStatus();
  }

  @override
  Widget build(BuildContext context) {
    final UserEntity? user = context.watch<UserCubit>().state;
    final List<ActionItem> actions = MorePage.getAllActions(context, user);
    return Scaffold(
      appBar: const HomeAppBar(),
      body: SafeArea(
        top: false,
        bottom: true,
        child: Stack(
          children: [
            RefreshIndicator(
              onRefresh: _refreshAttendanceData,
              child: SingleChildScrollView(
                physics: const AlwaysScrollableScrollPhysics(),
                padding: const EdgeInsets.fromLTRB(16.0, 16.0, 16.0, 0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const GreetingSection(),
                    const ResponsiveSpacer(mobileSize: 24, tabletSize: 26),
                    const QuickStatsSection(),
                    const ResponsiveSpacer(mobileSize: 24, tabletSize: 26),
                    const AttendanceSection(),
                    const ResponsiveSpacer(mobileSize: 24, tabletSize: 26),
                    HomeAnnouncementsSection(announcements: mockAnnouncements),
                    const ResponsiveSpacer(mobileSize: 24, tabletSize: 26),

                    HomeTopActions(actions: actions),
                  ],
                ),
              ),
            ),
            if (_isChatHeadVisible)
              ChatHeadWidget(
                initialPosition: _chatHeadPosition,
                onTap: () {
                  context.push(AppRoutes.message);
                },
                onDragEnd: (finalPosition) {
                  setState(() {
                    _chatHeadPosition = finalPosition;
                  });
                },
              ),
          ],
        ),
      ),
    );
  }
}
