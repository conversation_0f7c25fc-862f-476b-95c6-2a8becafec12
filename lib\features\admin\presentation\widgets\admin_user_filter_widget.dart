import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:golderhr/core/extensions/app_theme_extension.dart';
import 'package:golderhr/core/extensions/responsive_extension.dart';
import 'package:golderhr/shared/widgets/responsive_layout.dart';
import 'package:golderhr/shared/theme/app_text_styles.dart';
import 'package:iconsax/iconsax.dart';
import '../cubit/admin_user_cubit.dart';
import '../../domain/entities/admin_user_entity.dart';
import '../../../role/presentation/cubit/role_cubit.dart';
import '../../../department/presentation/cubit/department_cubit.dart';

class AdminUserFilterWidget extends StatefulWidget {
  final bool isDialog;

  const AdminUserFilterWidget({super.key, this.isDialog = false});

  @override
  State<AdminUserFilterWidget> createState() => _AdminUserFilterWidgetState();
}

class _AdminUserFilterWidgetState extends State<AdminUserFilterWidget> {
  late TextEditingController _searchController;
  late UserFilterOptions _tempFilters;

  @override
  void initState() {
    super.initState();
    _searchController = TextEditingController();
    // Initialize with default filters, will be updated in didChangeDependencies
    _tempFilters = const UserFilterOptions();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // Safely access the cubit after the widget tree is built
    final cubit = context.read<AdminUserCubit>();
    _tempFilters = cubit.state.filterOptions;
    _searchController.text = _tempFilters.search ?? '';
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = context.lightTheme;
    final responsive = context.responsive;

    return BlocBuilder<AdminUserCubit, AdminUserState>(
      builder: (context, state) {
        if (widget.isDialog) {
          return _buildDialogContent(context, theme, responsive);
        }
        return _buildInlineContent(context, theme, responsive);
      },
    );
  }

  Widget _buildDialogContent(
    BuildContext context,
    ThemeData theme,
    dynamic responsive,
  ) {
    return Container(
      padding: EdgeInsets.only(
        bottom: MediaQuery.of(context).viewInsets.bottom,
      ),
      child: DraggableScrollableSheet(
        initialChildSize: 0.7,
        minChildSize: 0.5,
        maxChildSize: 0.9,
        expand: false,
        builder: (context, scrollController) {
          return Container(
            decoration: BoxDecoration(
              color: theme.scaffoldBackgroundColor,
              borderRadius: const BorderRadius.vertical(
                top: Radius.circular(20),
              ),
            ),
            child: Column(
              children: [
                // Handle
                Container(
                  width: 40,
                  height: 4,
                  margin: const EdgeInsets.symmetric(vertical: 12),
                  decoration: BoxDecoration(
                    color: Colors.grey[300],
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),

                // Header
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  child: Row(
                    children: [
                      Text(
                        'Filter & Sort',
                        style: theme.textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const Spacer(),
                      TextButton(
                        onPressed: _resetFilters,
                        child: const Text('Reset'),
                      ),
                      TextButton(
                        onPressed: () {
                          _applyFilters(context);
                          Navigator.pop(context);
                        },
                        child: const Text('Apply'),
                      ),
                    ],
                  ),
                ),

                const Divider(),

                // Content
                Expanded(
                  child: SingleChildScrollView(
                    controller: scrollController,
                    padding: const EdgeInsets.all(16),
                    child: _buildFilterContent(context, theme, responsive),
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildInlineContent(
    BuildContext context,
    ThemeData theme,
    dynamic responsive,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.cardColor,
        border: Border(bottom: BorderSide(color: Colors.grey.shade200)),
      ),
      child: Column(
        children: [
          // Search Bar
          BlocBuilder<AdminUserCubit, AdminUserState>(
            builder: (context, state) {
              return TextField(
                controller: _searchController,
                decoration: InputDecoration(
                  hintText: 'Search users...', // l10n.searchUsers,
                  prefixIcon: Icon(
                    Iconsax.search_normal,
                    size: responsive.adaptiveValue<double>(
                      mobile: 20.0,
                      tablet: 22.0,
                      mobileLandscape: 21.0,
                      tabletLandscape: 24.0,
                    ),
                  ),
                  suffixIcon: _searchController.text.isNotEmpty
                      ? IconButton(
                          onPressed: () {
                            _searchController.clear();
                            context.read<AdminUserCubit>().searchUsers('');
                            setState(
                              () {},
                            ); // Force rebuild to hide clear button
                          },
                          icon: Icon(
                            Icons.clear,
                            size: responsive.adaptiveValue<double>(
                              mobile: 20.0,
                              tablet: 22.0,
                              mobileLandscape: 21.0,
                              tabletLandscape: 22.0,
                            ),
                          ),
                        )
                      : null,
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(
                      responsive.defaultRadius,
                    ),
                    borderSide: BorderSide.none,
                  ),
                  filled: true,
                  fillColor: Colors.grey.shade100,
                  contentPadding: responsive.padding(
                    horizontal: 16.0,
                    vertical: 12.0,
                  ),
                ),
                style: AppTextStyle.regular(context, size: 14),
                onChanged: (value) {
                  setState(() {}); // Update UI to show/hide clear button
                  context.read<AdminUserCubit>().searchUsers(value);
                },
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildFilterContent(
    BuildContext context,
    ThemeData theme,
    dynamic responsive,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Search
        _buildSection(
          'Search',
          TextField(
            controller: _searchController,
            decoration: InputDecoration(
              hintText: 'Search by name or email...',
              prefixIcon: const Icon(Iconsax.search_normal),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          ),
        ),

        // Status Filter
        _buildSection(
          'Status',
          DropdownButtonFormField<UserStatus>(
            value: _tempFilters.status,
            decoration: InputDecoration(
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            items: UserStatus.values.map((status) {
              return DropdownMenuItem(
                value: status,
                child: Text(_getStatusDisplayName(status)),
              );
            }).toList(),
            onChanged: (value) {
              if (value != null) {
                setState(() {
                  _tempFilters = _tempFilters.copyWith(status: value);
                });
              }
            },
          ),
        ),

        // Role Filter
        _buildSection(
          'Role',
          BlocBuilder<RoleCubit, RoleState>(
            builder: (context, state) {
              return DropdownButtonFormField<String>(
                value: _tempFilters.role,
                decoration: InputDecoration(
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                hint: const Text('All Roles'),
                items: [
                  const DropdownMenuItem<String>(
                    value: null,
                    child: Text('All Roles'),
                  ),
                  ...state.dropdownRoles.map((role) {
                    return DropdownMenuItem<String>(
                      value: role.name,
                      child: Text(role.name),
                    );
                  }),
                ],
                onChanged: (value) {
                  setState(() {
                    _tempFilters = _tempFilters.copyWith(
                      role: value,
                      clearRole: value == null,
                    );
                  });
                },
              );
            },
          ),
        ),

        // Department Filter
        _buildSection(
          'Department',
          BlocBuilder<DepartmentCubit, DepartmentState>(
            builder: (context, state) {
              return DropdownButtonFormField<String>(
                value: _tempFilters.department,
                decoration: InputDecoration(
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                hint: const Text('All Departments'),
                items: [
                  const DropdownMenuItem<String>(
                    value: null,
                    child: Text('All Departments'),
                  ),
                  ...state.departments.map((dept) {
                    return DropdownMenuItem<String>(
                      value: dept.name,
                      child: Text(dept.name),
                    );
                  }),
                ],
                onChanged: (value) {
                  setState(() {
                    _tempFilters = _tempFilters.copyWith(
                      department: value,
                      clearDepartment: value == null,
                    );
                  });
                },
              );
            },
          ),
        ),

        // Sort Options
        _buildSection(
          'Sort By',
          Column(
            children: [
              DropdownButtonFormField<UserSortField>(
                value: _tempFilters.sortBy,
                decoration: InputDecoration(
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                items: UserSortField.values.map((field) {
                  return DropdownMenuItem(
                    value: field,
                    child: Text(field.displayName),
                  );
                }).toList(),
                onChanged: (value) {
                  if (value != null) {
                    setState(() {
                      _tempFilters = _tempFilters.copyWith(sortBy: value);
                    });
                  }
                },
              ),
              const SizedBox(height: 8),
              Row(
                children: [
                  Expanded(
                    child: RadioListTile<SortOrder>(
                      title: const Text('Ascending'),
                      value: SortOrder.asc,
                      groupValue: _tempFilters.sortOrder,
                      onChanged: (value) {
                        setState(() {
                          _tempFilters = _tempFilters.copyWith(
                            sortOrder: value,
                          );
                        });
                      },
                    ),
                  ),
                  Expanded(
                    child: RadioListTile<SortOrder>(
                      title: const Text('Descending'),
                      value: SortOrder.desc,
                      groupValue: _tempFilters.sortOrder,
                      onChanged: (value) {
                        setState(() {
                          _tempFilters = _tempFilters.copyWith(
                            sortOrder: value,
                          );
                        });
                      },
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildSection(String title, Widget content) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
        ),
        const SizedBox(height: 8),
        content,
        const SizedBox(height: 24),
      ],
    );
  }

  void _resetFilters() {
    setState(() {
      _tempFilters = const UserFilterOptions();
      _searchController.clear();
    });
  }

  void _applyFilters(BuildContext context) {
    final finalFilters = _tempFilters.copyWith(
      search: _searchController.text.isEmpty ? null : _searchController.text,
    );
    context.read<AdminUserCubit>().applyFilters(finalFilters);
  }

  String _getStatusDisplayName(UserStatus status) {
    switch (status) {
      case UserStatus.all:
        return 'All Users';
      case UserStatus.active:
        return 'Active';
      case UserStatus.disabled:
        return 'Disabled';
      case UserStatus.deleted:
        return 'Deleted';
    }
  }
}
