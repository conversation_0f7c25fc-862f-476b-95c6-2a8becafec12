import 'package:dio/dio.dart';
import '../../../../core/error/exceptions.dart';
import '../../../../core/network/dio_client.dart';
import '../models/role_model.dart';
import '../../domain/entities/role_entity.dart';

abstract class RoleRemoteDataSource {
  Future<RoleListResult> getAllRoles({
    int page = 1,
    int limit = 10,
    String? search,
  });

  Future<RoleModel> getRoleById(String roleId);

  Future<RoleModel> createRole({required String name});

  Future<RoleModel> updateRole(String roleId, {String? name});

  Future<void> deleteRole(String roleId);

  Future<List<RoleModel>> getRolesForDropdown();

  Future<bool> checkRoleNameExists(String name, {String? excludeId});
}

class RoleRemoteDataSourceImpl implements RoleRemoteDataSource {
  final DioClient dioClient;

  RoleRemoteDataSourceImpl({required this.dioClient});

  @override
  Future<RoleListResult> getAllRoles({
    int page = 1,
    int limit = 10,
    String? search,
  }) async {
    try {
      final queryParams = <String, dynamic>{
        'page': page,
        'limit': limit,
        if (search != null && search.isNotEmpty) 'search': search,
      };

      final response = await dioClient.get(
        '/api/admin/roles',
        queryParameters: queryParams,
      );

      if (response.statusCode == 200) {
        return RoleListResult.fromJson(response.data['data']);
      } else {
        throw ServerException('Failed to fetch roles');
      }
    } catch (e) {
      if (e is ServerException) rethrow;
      throw ServerException('Network error: $e');
    }
  }

  @override
  Future<RoleModel> getRoleById(String roleId) async {
    try {
      final response = await dioClient.get('/api/admin/roles/$roleId');

      if (response.statusCode == 200) {
        return RoleModel.fromJson(response.data['data']);
      } else {
        throw ServerException('Failed to fetch role');
      }
    } catch (e) {
      if (e is ServerException) rethrow;
      throw ServerException('Network error: $e');
    }
  }

  @override
  Future<RoleModel> createRole({required String name}) async {
    try {
      final response = await dioClient.post(
        '/api/admin/roles',
        data: CreateRoleParams(name: name).toJson(),
      );

      if (response.statusCode == 201) {
        return RoleModel.fromJson(response.data['data']);
      } else {
        throw ServerException('Failed to create role');
      }
    } catch (e) {
      if (e is ServerException) rethrow;
      throw ServerException('Network error: $e');
    }
  }

  @override
  Future<RoleModel> updateRole(String roleId, {String? name}) async {
    try {
      final response = await dioClient.put(
        '/api/admin/roles/$roleId',
        data: UpdateRoleParams(roleId: roleId, name: name).toJson(),
      );

      if (response.statusCode == 200) {
        return RoleModel.fromJson(response.data['data']);
      } else {
        throw ServerException('Failed to update role');
      }
    } catch (e) {
      if (e is ServerException) rethrow;
      throw ServerException('Network error: $e');
    }
  }

  @override
  Future<void> deleteRole(String roleId) async {
    try {
      final response = await dioClient.delete('/api/admin/roles/$roleId');

      if (response.statusCode != 200) {
        throw ServerException('Failed to delete role');
      }
    } catch (e) {
      if (e is ServerException) rethrow;
      throw ServerException('Network error: $e');
    }
  }

  @override
  Future<List<RoleModel>> getRolesForDropdown() async {
    try {
      final response = await dioClient.get('/api/admin/roles/dropdown');

      if (response.statusCode == 200) {
        final List<dynamic> rolesJson = response.data['data'];
        return rolesJson.map((role) => RoleModel.fromJson(role)).toList();
      } else {
        throw ServerException('Failed to fetch roles');
      }
    } catch (e) {
      if (e is ServerException) rethrow;
      throw ServerException('Network error: $e');
    }
  }

  @override
  Future<bool> checkRoleNameExists(String name, {String? excludeId}) async {
    try {
      final queryParams = <String, dynamic>{
        'name': name,
        if (excludeId != null) 'excludeId': excludeId,
      };

      final response = await dioClient.get(
        '/api/admin/roles/check-name',
        queryParameters: queryParams,
      );

      if (response.statusCode == 200) {
        return response.data['data']['exists'] ?? false;
      } else {
        throw ServerException('Failed to check role name');
      }
    } catch (e) {
      if (e is ServerException) rethrow;
      throw ServerException('Network error: $e');
    }
  }
}
