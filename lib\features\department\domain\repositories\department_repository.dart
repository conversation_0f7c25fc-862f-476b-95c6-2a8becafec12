import 'package:dartz/dartz.dart';
import '../../../../core/error/failures.dart';
import '../entities/department_entity.dart';
import '../../data/models/department_model.dart';

abstract class DepartmentRepository {
  /// Get all departments with pagination and filtering
  Future<Either<Failure, DepartmentListResult>> getAllDepartments({
    int page = 1,
    int limit = 10,
    String? search,
    bool includeDeleted = false,
  });

  /// Get department by ID
  Future<Either<Failure, DepartmentEntity>> getDepartmentById(
    String departmentId,
  );

  /// Create a new department
  Future<Either<Failure, DepartmentEntity>> createDepartment({
    required String name,
    String? description,
    String? code,
    String? parentId,
  });

  /// Update department information
  Future<Either<Failure, DepartmentEntity>> updateDepartment(
    String departmentId, {
    String? name,
    String? description,
    String? code,
    bool? isActive,
    bool? isDisabled,
    String? parentId,
  });

  /// Delete department (soft delete)
  Future<Either<Failure, void>> deleteDepartment(String departmentId);

  /// Restore deleted department
  Future<Either<Failure, DepartmentEntity>> restoreDepartment(
    String departmentId,
  );

  /// Toggle department status (active/disabled)
  Future<Either<Failure, DepartmentEntity>> toggleDepartmentStatus(
    String departmentId,
  );

  /// Get departments for dropdown (active departments only)
  Future<Either<Failure, List<DepartmentEntity>>> getDepartmentsForDropdown();

  /// Check if department name exists
  Future<Either<Failure, bool>> checkDepartmentNameExists(
    String name, {
    String? excludeId,
  });

  /// Check if department code exists
  Future<Either<Failure, bool>> checkDepartmentCodeExists(
    String code, {
    String? excludeId,
  });

  /// Get department hierarchy (parent-child relationships)
  Future<Either<Failure, List<DepartmentEntity>>> getDepartmentHierarchy();
}
