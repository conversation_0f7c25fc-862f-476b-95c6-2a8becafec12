import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../l10n/app_localizations.dart';
import '../../domain/entities/leave_balance.dart';
import '../../domain/entities/leave_request.dart';
import '../../domain/entities/leave_policy.dart';
import '../../domain/usecases/get_leave_summary.dart';
import '../../domain/usecases/get_leave_history.dart';
import '../../domain/usecases/submit_leave_request.dart';
import '../../domain/usecases/get_approvers.dart';
import '../../domain/usecases/usecase.dart';
import 'leave_state.dart';

class LeaveCubit extends Cubit<LeaveState> {
  final GetLeaveSummary getLeaveSummary;
  final GetLeaveHistory getLeaveHistory;
  final SubmitLeaveRequest submitLeaveRequestUseCase;
  final GetApprovers getApprovers;

  LeaveCubit({
    required this.getLeaveSummary,
    required this.getLeaveHistory,
    required this.submitLeaveRequestUseCase,
    required this.getApprovers,
  }) : super(LeaveInitial());

  Future<void> loadLeaveData({AppLocalizations? l10n}) async {
    emit(LeaveLoading());

    try {
      // Get leave summary/balance
      final balanceResult = await getLeaveSummary(NoParams());

      // Get recent leave requests (first 5)
      final historyResult = await getLeaveHistory(
        const GetLeaveHistoryParams(page: 1, limit: 5),
      );

      balanceResult.fold((failure) => emit(LeaveError(failure.message)), (
        balance,
      ) {
        historyResult.fold((failure) => emit(LeaveError(failure.message)), (
          recentRequests,
        ) {
          // Mock policies for now
          final policies = [
            LeavePolicy(
              title: l10n?.annualLeave ?? 'Annual Leave',
              description:
                  l10n?.annualLeaveDescription ??
                  'Vacation, holiday, or personal time off',
              maxDays: 12,
            ),
            LeavePolicy(
              title: l10n?.sickLeave ?? 'Sick Leave',
              description:
                  l10n?.sickLeaveDescription ??
                  'Medical appointments or illness',
            ),
            LeavePolicy(
              title: l10n?.personalLeave ?? 'Personal Leave',
              description:
                  l10n?.personalLeaveDescription ??
                  'Personal matters or emergencies',
              maxDays: 3,
            ),
            const LeavePolicy(
              title: 'Advance Notice',
              description: 'Need to notify at least 2 days in advance',
              advanceNoticeDays: 2,
            ),
          ];

          emit(
            LeaveLoaded(
              balance: balance,
              recentRequests: recentRequests,
              policies: policies,
            ),
          );
        });
      });
    } catch (e) {
      emit(
        LeaveError(
          l10n?.failedToLoadLeaveData(e.toString()) ??
              'Failed to load leave data: ${e.toString()}',
        ),
      );
    }
  }

  Future<void> submitLeaveRequest({
    required LeaveType type,
    required DateTime startDate,
    required DateTime endDate,
    required String reason,
    String? approverId,
    AppLocalizations? l10n,
  }) async {
    emit(LeaveRequestSubmitting());

    try {
      final result = await submitLeaveRequestUseCase(
        SubmitLeaveRequestParams(
          type: type,
          startDate: startDate,
          endDate: endDate,
          reason: reason,
          approverId: approverId,
        ),
      );

      result.fold((failure) => emit(LeaveRequestError(failure.message)), (
        leaveRequest,
      ) {
        emit(
          LeaveRequestSubmitted(
            l10n?.leaveRequestSubmittedSuccess ??
                'Leave request submitted successfully!',
          ),
        );
        // Reload data after successful submission
        loadLeaveData(l10n: l10n);
      });
    } catch (e) {
      emit(
        LeaveRequestError(
          l10n?.failedToSubmitLeaveRequest(e.toString()) ??
              'Failed to submit leave request: ${e.toString()}',
        ),
      );
    }
  }

  Future<void> refreshData() async {
    await loadLeaveData();
  }

  Future<List<Map<String, dynamic>>> getApproversList() async {
    final result = await getApprovers(NoParams());
    return result.fold((failure) => [], (approvers) => approvers);
  }
}
