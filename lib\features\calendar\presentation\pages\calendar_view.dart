import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../core/extensions/l10n_extension.dart';
import '../../../../core/extensions/responsive_extension.dart';
import '../../../../shared/theme/app_colors.dart';
import '../cubit/calendar_cubit.dart';
import '../cubit/calendar_state.dart';
import '../widgets/calendar_header_widget.dart';
import '../widgets/calendar_tab_bar_widget.dart';
import '../widgets/month_view_widget.dart';
import '../widgets/week_view_widget.dart';
import '../widgets/agenda_view_widget.dart';
import '../widgets/add_event_dialog.dart';
import '../widgets/search_events_dialog.dart';

/// Calendar View chính với UI hiện đại
class CalendarView extends StatefulWidget {
  const CalendarView({super.key});

  @override
  State<CalendarView> createState() => _CalendarViewState();
}

class _CalendarViewState extends State<CalendarView>
    with TickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<CalendarCubit, CalendarState>(
      listener: _handleStateChanges,
      builder: (context, state) {
        if (state is CalendarLoading) {
          return _buildLoadingView(context);
        }

        return Column(
          children: [
            CalendarHeaderWidget(),
            SizedBox(height: context.rh(16)),
            CalendarTabBarWidget(tabController: _tabController),
            Expanded(
              child: TabBarView(
                controller: _tabController,
                children: [
                  MonthViewWidget(),
                  WeekViewWidget(),
                  AgendaViewWidget(),
                ],
              ),
            ),
          ],
        );
      },
    );
  }

  void _handleStateChanges(BuildContext context, CalendarState state) {
    final l10n = context.l10n;

    if (state is CalendarError) {
      _showSnackBar(context, state.message, AppColors.error);
    } else if (state is CalendarEventAdded) {
      _showSnackBar(context, l10n.eventAddedSuccessfully, AppColors.success);
    } else if (state is CalendarEventUpdated) {
      _showSnackBar(context, l10n.eventUpdatedSuccessfully, AppColors.success);
    } else if (state is CalendarEventDeleted) {
      _showSnackBar(context, l10n.eventDeletedSuccessfully, AppColors.success);
    }
  }

  void _showSnackBar(
    BuildContext context,
    String message,
    Color backgroundColor,
  ) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message, style: const TextStyle(color: Colors.white)),
        backgroundColor: backgroundColor,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        margin: EdgeInsets.all(context.rw(16)),
      ),
    );
  }

  Widget _buildLoadingView(BuildContext context) {
    final l10n = context.l10n;

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const CircularProgressIndicator(
            color: AppColors.primary,
            strokeWidth: 3,
          ),
          SizedBox(height: context.rh(16)),
          Text(
            l10n.loadingEvents,
            style: Theme.of(
              context,
            ).textTheme.bodyMedium?.copyWith(color: AppColors.textSecondary),
          ),
        ],
      ),
    );
  }

  /// Hiển thị dialog thêm event
  static void showAddEventDialog(BuildContext context) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (dialogContext) => BlocProvider.value(
        value: context.read<CalendarCubit>(),
        child: const AddEventDialog(),
      ),
    );
  }

  /// Hiển thị dialog tìm kiếm
  static void showSearchDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (dialogContext) => BlocProvider.value(
        value: context.read<CalendarCubit>(),
        child: const SearchEventsDialog(),
      ),
    );
  }

  /// Hiển thị dialog xác nhận xóa
  static Future<bool> showDeleteConfirmDialog(
    BuildContext context,
    String eventTitle,
  ) async {
    final l10n = context.l10n;

    final result = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        title: Text(
          l10n.deleteEvent,
          style: const TextStyle(
            color: AppColors.textPrimary,
            fontWeight: FontWeight.bold,
          ),
        ),
        content: Text(
          l10n.confirmDeleteEvent,
          style: const TextStyle(color: AppColors.textSecondary),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: Text(
              l10n.cancel,
              style: const TextStyle(color: AppColors.textSecondary),
            ),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.error,
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: Text(l10n.delete),
          ),
        ],
      ),
    );

    return result ?? false;
  }

  /// Hiển thị bottom sheet với options
  static void showEventOptionsBottomSheet(
    BuildContext context,
    String eventId,
    String eventTitle,
  ) {
    final l10n = context.l10n;

    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => Container(
        padding: EdgeInsets.all(context.rw(20)),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: AppColors.textSecondary.withOpacity(0.3),
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            SizedBox(height: context.rh(20)),
            Text(
              eventTitle,
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: AppColors.textPrimary,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: context.rh(24)),
            _buildBottomSheetOption(
              context,
              icon: Icons.edit_outlined,
              title: l10n.editEvent,
              onTap: () {
                Navigator.pop(context);
                // TODO: Implement edit event
              },
            ),
            _buildBottomSheetOption(
              context,
              icon: Icons.delete_outline,
              title: l10n.deleteEvent,
              isDestructive: true,
              onTap: () async {
                Navigator.pop(context);
                final shouldDelete = await showDeleteConfirmDialog(
                  context,
                  eventTitle,
                );
                if (shouldDelete) {
                  context.read<CalendarCubit>().deleteEvent(eventId);
                }
              },
            ),
            SizedBox(height: context.rh(16)),
          ],
        ),
      ),
    );
  }

  static Widget _buildBottomSheetOption(
    BuildContext context, {
    required IconData icon,
    required String title,
    required VoidCallback onTap,
    bool isDestructive = false,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        width: double.infinity,
        padding: EdgeInsets.symmetric(
          horizontal: context.rw(16),
          vertical: context.rh(16),
        ),
        child: Row(
          children: [
            Icon(
              icon,
              color: isDestructive ? AppColors.error : AppColors.textPrimary,
              size: context.rf(24),
            ),
            SizedBox(width: context.rw(16)),
            Text(
              title,
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                color: isDestructive ? AppColors.error : AppColors.textPrimary,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
