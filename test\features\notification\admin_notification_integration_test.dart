import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';
import 'package:dartz/dartz.dart';

import 'package:golderhr/features/notification/domain/usecases/notify_admin_new_request.dart';
import 'package:golderhr/features/leave/domain/usecases/submit_leave_request.dart';
import 'package:golderhr/features/leave/domain/entities/leave_request.dart';
import 'package:golderhr/injection_container.dart';

import 'admin_notification_integration_test.mocks.dart';

@GenerateMocks([NotifyAdminNewRequest])
void main() {
  late MockNotifyAdminNewRequest mockNotifyAdminUseCase;

  setUpAll(() {
    // Initialize service locator for testing
    // Note: In a real test, you would set up the full DI container
    mockNotifyAdminUseCase = MockNotifyAdminNewRequest();
  });

  group('Admin Notification Integration Tests', () {
    test('should call notify admin when leave request is submitted successfully', () async {
      // arrange
      when(mockNotifyAdminUseCase(any)).thenAnswer((_) async => const Right(null));

      const testParams = NotifyAdminNewRequestParams(
        requestType: 'leave',
        requestId: 'test-leave-123',
        employeeName: 'Test Employee',
        requestDetails: 'annual từ 1/1/2024 đến 5/1/2024',
      );

      // act
      final result = await mockNotifyAdminUseCase(testParams);

      // assert
      expect(result, const Right(null));
      verify(mockNotifyAdminUseCase(testParams)).called(1);
    });

    test('should call notify admin when overtime request is submitted successfully', () async {
      // arrange
      when(mockNotifyAdminUseCase(any)).thenAnswer((_) async => const Right(null));

      const testParams = NotifyAdminNewRequestParams(
        requestType: 'overtime',
        requestId: 'test-overtime-456',
        employeeName: 'Test Employee',
        requestDetails: 'regular vào 1/1/2024 từ 18:00 đến 20:00',
      );

      // act
      final result = await mockNotifyAdminUseCase(testParams);

      // assert
      expect(result, const Right(null));
      verify(mockNotifyAdminUseCase(testParams)).called(1);
    });

    test('should handle notification failure gracefully', () async {
      // arrange
      when(mockNotifyAdminUseCase(any)).thenAnswer(
        (_) async => const Left(ServerFailure('Failed to send notification')),
      );

      const testParams = NotifyAdminNewRequestParams(
        requestType: 'leave',
        requestId: 'test-leave-789',
        employeeName: 'Test Employee',
        requestDetails: 'sick từ 1/1/2024 đến 2/1/2024',
      );

      // act
      final result = await mockNotifyAdminUseCase(testParams);

      // assert
      expect(result.isLeft(), true);
      result.fold(
        (failure) => expect(failure.message, 'Failed to send notification'),
        (_) => fail('Expected failure but got success'),
      );
      verify(mockNotifyAdminUseCase(testParams)).called(1);
    });
  });
}

// Mock imports for the test
import 'package:golderhr/core/error/failures.dart';
