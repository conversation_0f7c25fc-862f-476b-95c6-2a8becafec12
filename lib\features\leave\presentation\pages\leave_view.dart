import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../core/extensions/l10n_extension.dart';
import '../../../../core/responsive/responsive.dart';
import '../../../../shared/theme/app_colors.dart';
import '../../../../shared/widgets/show_custom_snackBar.dart';
import '../../../../shared/widgets/error_display_widget.dart';
import '../cubit/leave_cubit.dart';
import '../cubit/leave_state.dart';
import '../widgets/leave_balance_card.dart';
import '../widgets/new_leave_tab.dart';
import '../widgets/my_leave_tab.dart';

class LeaveView extends StatelessWidget {
  const LeaveView({super.key});

  @override
  Widget build(BuildContext context) {
    final responsive = Responsive.of(context);
    final theme = Theme.of(context);

    return DefaultTabController(
      length: 2,
      child: Scaffold(
        backgroundColor: AppColors.background,
        appBar: AppBar(
          elevation: 0,
          backgroundColor: Colors.white,
          leading: IconButton(
            icon: const Icon(Icons.arrow_back, color: AppColors.textPrimary),
            onPressed: () => Navigator.of(context).pop(),
          ),
          title: Text(
            context.l10n.leave,
            style: theme.textTheme.titleLarge?.copyWith(
              color: AppColors.textPrimary,
            ),
          ),
        ),
        body: BlocConsumer<LeaveCubit, LeaveState>(
          listener: (context, state) {
            if (state is LeaveRequestSubmitted) {
              showTopSnackBar(
                context,
                title: context.l10n.success,
                message: state.message,
                isError: false,
              );
            } else if (state is LeaveRequestError) {
              showTopSnackBar(
                context,
                title: context.l10n.error,
                message: state.message,
                isError: true,
              );
            } else if (state is LeaveError) {
              showTopSnackBar(
                context,
                title: context.l10n.error,
                message: state.message,
                isError: true,
              );
            }
          },
          builder: (context, state) {
            if (state is LeaveLoading) {
              return const Center(child: CircularProgressIndicator());
            }

            if (state is LeaveLoaded) {
              return RefreshIndicator(
                onRefresh: () async {
                  context.read<LeaveCubit>().refreshData();
                },
                child: Column(
                  children: [
                    // Balance Card
                    Padding(
                      padding: responsive.padding(all: 16),
                      child: LeaveBalanceCard(balance: state.balance),
                    ),

                    // Tab Bar
                    Container(
                      margin: responsive.padding(horizontal: 16),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(12),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withValues(alpha: 0.05),
                            blurRadius: 10,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: TabBar(
                        labelColor: AppColors.primaryBlue,
                        unselectedLabelColor: AppColors.textSecondary,
                        indicator: BoxDecoration(
                          color: AppColors.primaryBlue.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        tabs: [
                          Tab(text: context.l10n.newRequest),
                          Tab(text: context.l10n.myLeave),
                        ],
                      ),
                    ),

                    // Tab Views
                    const Expanded(
                      child: TabBarView(
                        children: [NewLeaveTab(), MyLeaveTab()],
                      ),
                    ),
                  ],
                ),
              );
            }

            return ErrorDisplayWidget(
              errorMessage: state is LeaveError ? state.message : null,
              onRetry: () {
                context.read<LeaveCubit>().loadLeaveData(l10n: context.l10n);
              },
            );
          },
        ),
      ),
    );
  }
}
