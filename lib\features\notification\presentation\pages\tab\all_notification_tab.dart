import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:golderhr/core/extensions/responsive_extension.dart';

import '../../../../../shared/widgets/responsive_spacer.dart';
import '../../cubit/notification_cubit.dart';
import '../../cubit/notification_state.dart';
import '../../widgets/empty_notification_widget.dart';
import '../../widgets/notification_card.dart';
import '../../widgets/notification_section_header.dart';

class AllNotificationsTab extends StatelessWidget {
  const AllNotificationsTab({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<NotificationCubit, NotificationState>(
      builder: (context, state) {
        if (state is NotificationLoading) {
          return const Center(child: CircularProgressIndicator());
        }

        if (state is NotificationEmpty) {
          return const EmptyNotificationWidget();
        }

        if (state is NotificationError) {
          return Center(child: Text(state.message));
        }

        if (state is NotificationLoaded) {
          final sortedKeys = ['Hôm nay', 'Hôm qua', 'Tuần này', 'Cũ hơn']
              .where((key) => state.groupedNotifications.containsKey(key))
              .toList();

          return RefreshIndicator(
            onRefresh: () =>
                context.read<NotificationCubit>().fetchNotifications(),
            child: ListView.builder(
              padding: context.responsive.padding(
                horizontal: 16.0,
                vertical: 8.0,
              ),
              itemCount: sortedKeys.length,
              itemBuilder: (context, index) {
                final key = sortedKeys[index];
                final notifications = state.groupedNotifications[key]!;

                return Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    NotificationSectionHeader(title: key),
                    ListView.separated(
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      itemCount: notifications.length,
                      itemBuilder: (_, notifIndex) {
                        final notif = notifications[notifIndex];
                        return NotificationCard(
                          notification: notif,
                          onMarkAsRead: () {
                            if (!notif.isRead) {
                              context.read<NotificationCubit>().markAsRead(
                                notif.id,
                              );
                            }
                          },
                        );
                      },
                      separatorBuilder: (_, __) => ResponsiveSpacer(
                        mobileSize: 12,
                        tabletSize: 14,
                        mobileLandscapeSize: 12,
                        tabletLandscapeSize: 14,
                      ),
                    ),
                  ],
                );
              },
            ),
          );
        }
        return const SizedBox.shrink();
      },
    );
  }
}
