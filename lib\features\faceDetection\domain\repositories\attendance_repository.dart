import 'dart:io';
import 'package:dartz/dartz.dart';
import 'package:golderhr/features/faceDetection/domain/entities/attendance_status_entity.dart';
import 'package:golderhr/features/faceDetection/domain/entities/location_entity.dart';

import '../../../../core/error/failures.dart';
import '../entities/attendance_record_entity.dart';

abstract class AttendanceRepository {
  Future<Either<Failure, AttendanceRecordEntity>> checkIn({
    required File image,
    required LocationEntity location,
  });

  Future<Either<Failure, AttendanceRecordEntity>> checkOut({
    required File image,
    required LocationEntity location,
  });
  Future<Either<Failure, AttendanceStatusEntity?>> getTodayAttendance();
}
