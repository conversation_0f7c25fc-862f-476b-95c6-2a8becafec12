import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../core/extensions/l10n_extension.dart';
import '../../../../core/extensions/responsive_extension.dart';
import '../../../../shared/theme/app_colors.dart';
import '../../domain/entities/calendar_event.dart';
import '../cubit/calendar_cubit.dart';
import '../cubit/calendar_state.dart';
import 'event_list_widget.dart';

/// Widget hiển thị agenda view với upcoming events
class AgendaViewWidget extends StatelessWidget {
  const AgendaViewWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<CalendarCubit, CalendarState>(
      builder: (context, state) {
        if (state is CalendarEventsLoaded) {
          return _buildAgendaContent(context, state);
        }

        return _buildLoadingState(context);
      },
    );
  }

  Widget _buildAgendaContent(BuildContext context, CalendarEventsLoaded state) {
    final cubit = context.read<CalendarCubit>();
    final upcomingEvents = _getUpcomingEvents(cubit.allEvents);

    if (upcomingEvents.isEmpty) {
      return _buildEmptyState(context);
    }

    // Group events by date
    final groupedEvents = _groupEventsByDate(upcomingEvents);

    return SingleChildScrollView(
      padding: context.responsive.padding(all: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildAgendaHeader(context, upcomingEvents.length),
          SizedBox(height: context.rh(16)),
          _buildEventTypeFilters(context),
          SizedBox(height: context.rh(16)),
          _buildEventsList(context, groupedEvents),
        ],
      ),
    );
  }

  Widget _buildAgendaHeader(BuildContext context, int eventCount) {
    final l10n = context.l10n;

    return Container(
      padding: context.responsive.padding(all: 20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            AppColors.primaryBlue.withOpacity(0.1),
            AppColors.primaryGreen.withOpacity(0.1),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: AppColors.primaryBlue.withOpacity(0.2),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: EdgeInsets.all(context.rw(12)),
            decoration: BoxDecoration(
              color: AppColors.primaryBlue.withOpacity(0.15),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(
              Icons.schedule,
              color: AppColors.primaryBlue,
              size: context.rf(24),
            ),
          ),
          SizedBox(width: context.rw(16)),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Upcoming Events',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    color: AppColors.textPrimary,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(height: context.rh(4)),
                Text(
                  '$eventCount events scheduled',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: AppColors.textSecondary,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEventTypeFilters(BuildContext context) {
    return SizedBox(
      height: context.rh(40),
      child: ListView(
        scrollDirection: Axis.horizontal,
        children: EventType.values.map((type) {
          return Container(
            margin: EdgeInsets.only(right: context.rw(8)),
            child: FilterChip(
              label: Text(type.displayName),
              selected: false, // TODO: Implement filter state
              onSelected: (selected) {
                // TODO: Implement filter logic
              },
              backgroundColor: Colors.white,
              selectedColor: AppColors.primaryBlue.withOpacity(0.1),
              checkmarkColor: AppColors.primaryBlue,
              labelStyle: TextStyle(
                color: AppColors.textPrimary,
                fontSize: context.rf(12),
              ),
              side: BorderSide(
                color: AppColors.textSecondary.withOpacity(0.3),
                width: 1,
              ),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(20),
              ),
            ),
          );
        }).toList(),
      ),
    );
  }

  Widget _buildEventsList(
    BuildContext context,
    Map<DateTime, List<CalendarEvent>> groupedEvents,
  ) {
    return GroupedEventListWidget(
      groupedEvents: groupedEvents,
      showDateHeaders: true,
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    final l10n = context.l10n;

    return Center(
      child: Container(
        padding: context.responsive.padding(all: 32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: EdgeInsets.all(context.rw(24)),
              decoration: BoxDecoration(
                color: AppColors.primaryBlue.withOpacity(0.1),
                shape: BoxShape.circle,
              ),
              child: Icon(
                Icons.event_available_outlined,
                size: context.rf(64),
                color: AppColors.primaryBlue.withOpacity(0.7),
              ),
            ),
            SizedBox(height: context.rh(24)),
            Text(
              l10n.noUpcomingEvents,
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: AppColors.textPrimary,
                fontWeight: FontWeight.w600,
              ),
            ),
            SizedBox(height: context.rh(8)),
            Text(
              'Your schedule is clear for the upcoming days',
              style: Theme.of(
                context,
              ).textTheme.bodyMedium?.copyWith(color: AppColors.textSecondary),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: context.rh(24)),
            ElevatedButton.icon(
              onPressed: () {
                // TODO: Navigate to add event
              },
              icon: const Icon(Icons.add),
              label: Text(l10n.addEvent),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primaryBlue,
                foregroundColor: Colors.white,
                padding: EdgeInsets.symmetric(
                  horizontal: context.rw(24),
                  vertical: context.rh(12),
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLoadingState(BuildContext context) {
    final l10n = context.l10n;

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const CircularProgressIndicator(color: AppColors.primary),
          SizedBox(height: context.rh(16)),
          Text(
            l10n.loadingEvents,
            style: Theme.of(
              context,
            ).textTheme.bodyMedium?.copyWith(color: AppColors.textSecondary),
          ),
        ],
      ),
    );
  }

  List<CalendarEvent> _getUpcomingEvents(List<CalendarEvent> allEvents) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);

    return allEvents.where((event) => !event.date.isBefore(today)).toList()
      ..sort((a, b) => a.date.compareTo(b.date));
  }

  Map<DateTime, List<CalendarEvent>> _groupEventsByDate(
    List<CalendarEvent> events,
  ) {
    final grouped = <DateTime, List<CalendarEvent>>{};

    for (final event in events) {
      final eventDate = DateTime(
        event.date.year,
        event.date.month,
        event.date.day,
      );
      grouped.putIfAbsent(eventDate, () => []).add(event);
    }

    return grouped;
  }
}
