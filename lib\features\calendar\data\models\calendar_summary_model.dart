import 'dart:convert';
import '../../domain/entities/calendar_summary.dart';

/// Model cho CalendarSummary, kế thừa từ entity và thêm các phương thức serialization
class CalendarSummaryModel extends CalendarSummary {
  const CalendarSummaryModel({
    required super.totalEvents,
    required super.totalMeetings,
    required super.totalHolidays,
    required super.totalLeaves,
    required super.totalTrainings,
    required super.upcomingEvents,
    required super.todayEvents,
    required super.thisWeekEvents,
    required super.thisMonthEvents,
    required super.lastUpdated,
  });

  /// Tạo CalendarSummaryModel từ JSON
  factory CalendarSummaryModel.fromJson(Map<String, dynamic> json) {
    return CalendarSummaryModel(
      totalEvents: json['totalEvents'] as int? ?? 0,
      totalMeetings: json['totalMeetings'] as int? ?? 0,
      totalHolidays: json['totalHolidays'] as int? ?? 0,
      totalLeaves: json['totalLeaves'] as int? ?? 0,
      totalTrainings: json['totalTrainings'] as int? ?? 0,
      upcomingEvents: json['upcomingEvents'] as int? ?? 0,
      todayEvents: json['todayEvents'] as int? ?? 0,
      thisWeekEvents: json['thisWeekEvents'] as int? ?? 0,
      thisMonthEvents: json['thisMonthEvents'] as int? ?? 0,
      lastUpdated: json['lastUpdated'] != null
          ? DateTime.parse(json['lastUpdated'] as String)
          : DateTime.now(),
    );
  }

  /// Chuyển đổi CalendarSummaryModel thành JSON
  Map<String, dynamic> toJson() {
    return {
      'totalEvents': totalEvents,
      'totalMeetings': totalMeetings,
      'totalHolidays': totalHolidays,
      'totalLeaves': totalLeaves,
      'totalTrainings': totalTrainings,
      'upcomingEvents': upcomingEvents,
      'todayEvents': todayEvents,
      'thisWeekEvents': thisWeekEvents,
      'thisMonthEvents': thisMonthEvents,
      'lastUpdated': lastUpdated.toIso8601String(),
    };
  }

  /// Tạo CalendarSummaryModel từ CalendarSummary entity
  factory CalendarSummaryModel.fromEntity(CalendarSummary summary) {
    return CalendarSummaryModel(
      totalEvents: summary.totalEvents,
      totalMeetings: summary.totalMeetings,
      totalHolidays: summary.totalHolidays,
      totalLeaves: summary.totalLeaves,
      totalTrainings: summary.totalTrainings,
      upcomingEvents: summary.upcomingEvents,
      todayEvents: summary.todayEvents,
      thisWeekEvents: summary.thisWeekEvents,
      thisMonthEvents: summary.thisMonthEvents,
      lastUpdated: summary.lastUpdated,
    );
  }

  /// Chuyển đổi thành CalendarSummary entity
  CalendarSummary toEntity() {
    return CalendarSummary(
      totalEvents: totalEvents,
      totalMeetings: totalMeetings,
      totalHolidays: totalHolidays,
      totalLeaves: totalLeaves,
      totalTrainings: totalTrainings,
      upcomingEvents: upcomingEvents,
      todayEvents: todayEvents,
      thisWeekEvents: thisWeekEvents,
      thisMonthEvents: thisMonthEvents,
      lastUpdated: lastUpdated,
    );
  }

  /// Copy với các thay đổi, trả về CalendarSummaryModel
  @override
  CalendarSummaryModel copyWith({
    int? totalEvents,
    int? totalMeetings,
    int? totalHolidays,
    int? totalLeaves,
    int? totalTrainings,
    int? upcomingEvents,
    int? todayEvents,
    int? thisWeekEvents,
    int? thisMonthEvents,
    DateTime? lastUpdated,
  }) {
    return CalendarSummaryModel(
      totalEvents: totalEvents ?? this.totalEvents,
      totalMeetings: totalMeetings ?? this.totalMeetings,
      totalHolidays: totalHolidays ?? this.totalHolidays,
      totalLeaves: totalLeaves ?? this.totalLeaves,
      totalTrainings: totalTrainings ?? this.totalTrainings,
      upcomingEvents: upcomingEvents ?? this.upcomingEvents,
      todayEvents: todayEvents ?? this.todayEvents,
      thisWeekEvents: thisWeekEvents ?? this.thisWeekEvents,
      thisMonthEvents: thisMonthEvents ?? this.thisMonthEvents,
      lastUpdated: lastUpdated ?? this.lastUpdated,
    );
  }

  /// Chuyển đổi từ JSON string
  factory CalendarSummaryModel.fromJsonString(String jsonString) {
    return CalendarSummaryModel.fromJson(json.decode(jsonString));
  }

  /// Chuyển đổi thành JSON string
  String toJsonString() {
    return json.encode(toJson());
  }

  /// Tạo CalendarSummaryModel rỗng
  factory CalendarSummaryModel.empty() {
    return CalendarSummaryModel(
      totalEvents: 0,
      totalMeetings: 0,
      totalHolidays: 0,
      totalLeaves: 0,
      totalTrainings: 0,
      upcomingEvents: 0,
      todayEvents: 0,
      thisWeekEvents: 0,
      thisMonthEvents: 0,
      lastUpdated: DateTime.now(),
    );
  }
}
