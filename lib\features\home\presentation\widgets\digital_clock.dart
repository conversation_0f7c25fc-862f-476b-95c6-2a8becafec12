import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:golderhr/core/extensions/responsive_extension.dart';

// Import ClockCubit
import '../../../cubit/clock_cubit.dart';

class DigitalClock extends StatelessWidget {
  const DigitalClock({super.key});

  @override
  Widget build(BuildContext context) {
    final responsive = context.responsive;

    return Center(
      child: Container(
        padding: responsive.padding(all: 20),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [Colors.grey[100]!, Colors.grey[200]!],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          borderRadius: BorderRadius.circular(15),
          border: Border.all(color: Colors.grey.shade300),
        ),
        // DÙNG BlocBuilder ĐỂ LẮNG NGHE CLOCKCUBIT
        // <Tên Cubit, Kiểu State của nó>
        child: BlocBuilder<ClockCubit, String>(
          builder: (context, currentTime) {
            // `currentTime` chính là state (chuỗi thời gian "HH:mm:ss") mà Cubit phát ra.
            return Text(
              currentTime,
              style:
                  Theme.of(context).textTheme.displayMedium?.copyWith(
                    color: const Color(0xFF1E40AF),
                    fontFamily: 'monospace',
                    fontWeight: FontWeight.w600,
                    letterSpacing: 2.0,
                  ) ??
                  const TextStyle(
                    fontSize: 45,
                    color: Color(0xFF1E40AF),
                    fontFamily: 'monospace',
                  ),
            );
          },
        ),
      ),
    );
  }
}
