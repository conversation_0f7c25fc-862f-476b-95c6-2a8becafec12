import 'package:dartz/dartz.dart';

import '../../../../core/error/failures.dart';
import '../entities/leave_balance.dart';
import '../entities/leave_request.dart';

abstract class LeaveRepository {
  Future<Either<Failure, LeaveBalance>> getLeaveSummary();
  
  Future<Either<Failure, List<LeaveRequest>>> getLeaveHistory({
    int page = 1,
    int limit = 10,
    String? status,
  });
  
  Future<Either<Failure, LeaveRequest>> submitLeaveRequest({
    required LeaveType type,
    required DateTime startDate,
    required DateTime endDate,
    required String reason,
    String? approverId,
  });
  
  Future<Either<Failure, LeaveRequest>> updateLeaveRequest({
    required String id,
    required LeaveType type,
    required DateTime startDate,
    required DateTime endDate,
    required String reason,
  });
  
  Future<Either<Failure, void>> cancelLeaveRequest(String id);
  
  Future<Either<Failure, LeaveRequest>> getLeaveRequestById(String id);
  
  Future<Either<Failure, List<Map<String, dynamic>>>> getApprovers();
}
