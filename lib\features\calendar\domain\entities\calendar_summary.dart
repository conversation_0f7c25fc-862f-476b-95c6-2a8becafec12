import 'package:equatable/equatable.dart';

/// Entity đại diện cho thống kê tổng quan của calendar
class CalendarSummary extends Equatable {
  final int totalEvents;
  final int totalMeetings;
  final int totalHolidays;
  final int totalLeaves;
  final int totalTrainings;
  final int upcomingEvents;
  final int todayEvents;
  final int thisWeekEvents;
  final int thisMonthEvents;
  final DateTime lastUpdated;

  const CalendarSummary({
    required this.totalEvents,
    required this.totalMeetings,
    required this.totalHolidays,
    required this.totalLeaves,
    required this.totalTrainings,
    required this.upcomingEvents,
    required this.todayEvents,
    required this.thisWeekEvents,
    required this.thisMonthEvents,
    required this.lastUpdated,
  });

  /// Tạo CalendarSummary rỗng
  factory CalendarSummary.empty() {
    return CalendarSummary(
      totalEvents: 0,
      totalMeetings: 0,
      totalHolidays: 0,
      totalLeaves: 0,
      totalTrainings: 0,
      upcomingEvents: 0,
      todayEvents: 0,
      thisWeekEvents: 0,
      thisMonthEvents: 0,
      lastUpdated: DateTime.now(),
    );
  }

  /// Copy với các thay đổi
  CalendarSummary copyWith({
    int? totalEvents,
    int? totalMeetings,
    int? totalHolidays,
    int? totalLeaves,
    int? totalTrainings,
    int? upcomingEvents,
    int? todayEvents,
    int? thisWeekEvents,
    int? thisMonthEvents,
    DateTime? lastUpdated,
  }) {
    return CalendarSummary(
      totalEvents: totalEvents ?? this.totalEvents,
      totalMeetings: totalMeetings ?? this.totalMeetings,
      totalHolidays: totalHolidays ?? this.totalHolidays,
      totalLeaves: totalLeaves ?? this.totalLeaves,
      totalTrainings: totalTrainings ?? this.totalTrainings,
      upcomingEvents: upcomingEvents ?? this.upcomingEvents,
      todayEvents: todayEvents ?? this.todayEvents,
      thisWeekEvents: thisWeekEvents ?? this.thisWeekEvents,
      thisMonthEvents: thisMonthEvents ?? this.thisMonthEvents,
      lastUpdated: lastUpdated ?? this.lastUpdated,
    );
  }

  @override
  List<Object?> get props => [
    totalEvents,
    totalMeetings,
    totalHolidays,
    totalLeaves,
    totalTrainings,
    upcomingEvents,
    todayEvents,
    thisWeekEvents,
    thisMonthEvents,
    lastUpdated,
  ];
}
