import 'package:flutter/material.dart';
import 'package:golderhr/shared/utils/validators.dart';
import 'package:golderhr/l10n/app_localizations.dart';

class OvertimeRejectionDialog extends StatefulWidget {
  final Function(String reason) onReject;

  const OvertimeRejectionDialog({super.key, required this.onReject});

  @override
  State<OvertimeRejectionDialog> createState() =>
      _OvertimeRejectionDialogState();
}

class _OvertimeRejectionDialogState extends State<OvertimeRejectionDialog> {
  final TextEditingController _reasonController = TextEditingController();
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  bool _isSubmitting = false;

  @override
  void dispose() {
    _reasonController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;

    return GestureDetector(
      onTap: () {
        // Dismiss keyboard when tapping outside
        FocusScope.of(context).unfocus();
      },
      child: AlertDialog(
        title: Row(
          children: [
            Icon(Icons.cancel, color: Colors.red[700], size: 24),
            const SizedBox(width: 8),
            Text(
              l10n.rejectRequest,
              style: Theme.of(
                context,
              ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.w600),
            ),
          ],
        ),
        content: SingleChildScrollView(
          child: Form(
            key: _formKey,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  l10n.rejectionReasonDescription,
                  style: Theme.of(
                    context,
                  ).textTheme.bodyMedium?.copyWith(color: Colors.grey[600]),
                ),
                const SizedBox(height: 16),
                TextFormField(
                  controller: _reasonController,
                  maxLines: 4,
                  decoration: InputDecoration(
                    labelText: l10n.rejectionReason,
                    hintText: l10n.enterRejectionReason,
                    helperText: l10n.rejectionReasonTooShort,
                    border: const OutlineInputBorder(),
                    alignLabelWithHint: true,
                  ),
                  validator: (value) =>
                      Validators.validateRejectionReason(value, context),
                ),
              ],
            ),
          ),
        ),
        actions: [
          TextButton(
            onPressed: _isSubmitting
                ? null
                : () {
                    Navigator.of(context).pop();
                  },
            child: Text(l10n.cancel),
          ),
          ElevatedButton(
            onPressed: _isSubmitting
                ? null
                : () async {
                    if (_formKey.currentState!.validate()) {
                      setState(() {
                        _isSubmitting = true;
                      });

                      try {
                        widget.onReject(_reasonController.text.trim());
                        Navigator.of(context).pop();
                      } catch (e) {
                        setState(() {
                          _isSubmitting = false;
                        });
                      }
                    }
                  },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: _isSubmitting
                ? const SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  )
                : Text(l10n.reject),
          ),
        ],
      ),
    );
  }
}
