import 'package:dio/dio.dart';
import 'package:golderhr/core/error/exceptions.dart';
import 'package:golderhr/core/network/dio_client.dart';
import '../model/notification_model.dart';

abstract class NotificationRemoteDataSource {
  Future<List<NotificationModel>> getNotifications({
    int page = 1,
    int limit = 20,
    String? type,
    bool? isRead,
    String? priority,
  });

  Future<int> getUnreadCount();
  Future<void> markAsRead(String notificationId);
  Future<void> markAllAsRead();
  Future<void> registerFCMToken({
    required String token,
    required String deviceType,
    String? deviceId,
    Map<String, dynamic>? deviceInfo,
  });
  Future<void> removeFCMToken(String token);
}

class NotificationRemoteDataSourceImpl implements NotificationRemoteDataSource {
  final DioClient dioClient;

  NotificationRemoteDataSourceImpl({required this.dioClient});

  @override
  Future<List<NotificationModel>> getNotifications({
    int page = 1,
    int limit = 20,
    String? type,
    bool? isRead,
    String? priority,
  }) async {
    try {
      final queryParameters = <String, dynamic>{'page': page, 'limit': limit};

      if (type != null) queryParameters['type'] = type;
      if (isRead != null) queryParameters['isRead'] = isRead.toString();
      if (priority != null) queryParameters['priority'] = priority;

      final response = await dioClient.get(
        '/api/notifications',
        queryParameters: queryParameters,
      );

      if (response.data['success'] == true) {
        final notificationsData =
            response.data['data']['notifications'] as List;
        return notificationsData
            .map((json) => NotificationModel.fromJson(json))
            .toList();
      } else {
        throw ServerException(
          response.data['message'] ?? 'Failed to get notifications',
        );
      }
    } on DioException catch (e) {
      throw ServerException(e.response?.data['message'] ?? 'Server error');
    } catch (e) {
      throw NetworkException('Network error: $e');
    }
  }

  @override
  Future<int> getUnreadCount() async {
    try {
      final response = await dioClient.get('/api/notifications');

      if (response.data['success'] == true) {
        return response.data['data']['unreadCount'] ?? 0;
      } else {
        throw ServerException(
          response.data['message'] ?? 'Failed to get unread count',
        );
      }
    } on DioException catch (e) {
      throw ServerException(e.response?.data['message'] ?? 'Server error');
    } catch (e) {
      throw NetworkException('Network error: $e');
    }
  }

  @override
  Future<void> markAsRead(String notificationId) async {
    try {
      final response = await dioClient.get(
        '/api/notifications/$notificationId/read',
      );

      if (response.data['success'] != true) {
        throw ServerException(
          response.data['message'] ?? 'Failed to mark as read',
        );
      }
    } on DioException catch (e) {
      throw ServerException(e.response?.data['message'] ?? 'Server error');
    } catch (e) {
      throw NetworkException('Network error: $e');
    }
  }

  @override
  Future<void> markAllAsRead() async {
    try {
      final response = await dioClient.get('/api/notifications/read-all');

      if (response.data['success'] != true) {
        throw ServerException(
          response.data['message'] ?? 'Failed to mark all as read',
        );
      }
    } on DioException catch (e) {
      throw ServerException(e.response?.data['message'] ?? 'Server error');
    } catch (e) {
      throw NetworkException('Network error: $e');
    }
  }

  @override
  Future<void> registerFCMToken({
    required String token,
    required String deviceType,
    String? deviceId,
    Map<String, dynamic>? deviceInfo,
  }) async {
    try {
      final data = {
        'token': token,
        'deviceType': deviceType,
        if (deviceId != null) 'deviceId': deviceId,
        if (deviceInfo != null) 'deviceInfo': deviceInfo,
      };

      final response = await dioClient.post(
        '/api/notifications/fcm-token',
        data: data,
      );

      if (response.data['success'] != true) {
        throw ServerException(
          response.data['message'] ?? 'Failed to register FCM token',
        );
      }
    } on DioException catch (e) {
      throw ServerException(e.response?.data['message'] ?? 'Server error');
    } catch (e) {
      throw NetworkException('Network error: $e');
    }
  }

  @override
  Future<void> removeFCMToken(String token) async {
    try {
      final response = await dioClient.delete(
        '/api/notifications/fcm-token',
        data: {'token': token},
      );

      if (response.data['success'] != true) {
        throw ServerException(
          response.data['message'] ?? 'Failed to remove FCM token',
        );
      }
    } on DioException catch (e) {
      throw ServerException(e.response?.data['message'] ?? 'Server error');
    } catch (e) {
      throw NetworkException('Network error: $e');
    }
  }
}
