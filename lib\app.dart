import 'package:device_preview/device_preview.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:golderhr/core/extensions/app_theme_extension.dart';
import 'package:golderhr/core/routes/app_pages.dart';
import 'features/auth/presentation/cubit/auth_cubit.dart';
import 'features/cubit/language_cubit.dart';
import 'features/cubit/user_cubit.dart';
import 'injection_container.dart';
import 'l10n/app_localizations.dart';

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider(create: (_) => sl<AuthCubit>()),
        BlocProvider(create: (_) => sl<LanguageCubit>()),
        BlocProvider(create: (_) => sl<UserCubit>()),
      ],
      child: <PERSON><PERSON>uilder<LanguageCubit, String>(
        builder: (context, languageCode) {
          return MaterialApp.router(
            debugShowCheckedModeBanner: false,
            locale: Locale(languageCode),
            builder: DevicePreview.appBuilder,
            theme: context.lightTheme,
            title: 'Golden HR',
            routerConfig: appRouter,
            localizationsDelegates: const [
              AppLocalizations.delegate,
              GlobalMaterialLocalizations.delegate,
              GlobalWidgetsLocalizations.delegate,
              GlobalCupertinoLocalizations.delegate,
            ],
            supportedLocales: const [Locale('en'), Locale('vi')],
          );
        },
      ),
    );
  }
}
