import 'package:equatable/equatable.dart';

import '../../data/model/notification_model.dart';

abstract class NotificationState extends Equatable {
  const NotificationState();

  @override
  List<Object> get props => [];
}

class NotificationInitial extends NotificationState {}

class NotificationLoading extends NotificationState {}

class NotificationLoaded extends NotificationState {
  final Map<String, List<NotificationModel>> groupedNotifications;

  // Dùng Map để truy cập dễ hơn khi cập nhật
  final Map<String, NotificationModel> allNotifications;
  final List<NotificationModel> importantNotifications;
  final List<NotificationModel> customerNotifications;
  final List<NotificationModel> unreadNotifications;

  const NotificationLoaded({
    required this.groupedNotifications,
    required this.allNotifications,
    required this.importantNotifications,
    required this.customerNotifications,
    required this.unreadNotifications,
  });

  @override
  List<Object> get props => [groupedNotifications];

  NotificationLoaded copyWith({
    Map<String, List<NotificationModel>>? groupedNotifications,
    Map<String, NotificationModel>? allNotifications,
    List<NotificationModel>? importantNotifications,
    List<NotificationModel>? customerNotifications,
    List<NotificationModel>? unreadNotifications,
  }) {
    return NotificationLoaded(
      groupedNotifications: groupedNotifications ?? this.groupedNotifications,
      allNotifications: allNotifications ?? this.allNotifications,
      importantNotifications:
          importantNotifications ?? this.importantNotifications,
      customerNotifications:
          customerNotifications ?? this.customerNotifications,
      unreadNotifications: unreadNotifications ?? this.unreadNotifications,
    );
  }
}

class NotificationEmpty extends NotificationState {}

class NotificationError extends NotificationState {
  final String message;

  const NotificationError(this.message);

  @override
  List<Object> get props => [message];
}
