import 'package:flutter/material.dart';
import 'package:golderhr/core/extensions/app_theme_extension.dart';
import 'package:golderhr/core/extensions/responsive_extension.dart';
import 'package:iconsax/iconsax.dart';
import '../../domain/entities/admin_user_entity.dart';
import 'admin_user_card.dart';

class AdminUserListWidget extends StatelessWidget {
  final List<AdminUserEntity> users;
  final ScrollController scrollController;
  final bool isLoadingMore;
  final bool isProcessing;
  final List<String> selectedUserIds;
  final Function(AdminUserEntity) onUserTap;
  final Function(String) onUserSelect;
  final Function(String action, AdminUserEntity user) onUserAction;

  const AdminUserListWidget({
    super.key,
    required this.users,
    required this.scrollController,
    required this.isLoadingMore,
    required this.isProcessing,
    required this.selectedUserIds,
    required this.onUserTap,
    required this.onUserSelect,
    required this.onUserAction,
  });

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      controller: scrollController,
      padding: const EdgeInsets.all(16),
      itemCount: users.length + (isLoadingMore ? 1 : 0),
      itemBuilder: (context, index) {
        if (index == users.length) {
          // Loading more indicator
          return const Padding(
            padding: EdgeInsets.all(16),
            child: Center(child: CircularProgressIndicator()),
          );
        }

        final user = users[index];
        final isSelected = selectedUserIds.contains(user.id);

        return Padding(
          padding: const EdgeInsets.only(bottom: 12),
          child: AdminUserCard(
            user: user,
            isSelected: isSelected,
            isProcessing: isProcessing,
            onTap: () => onUserTap(user),
            onSelect: () => onUserSelect(user.id),
            onAction: (action) => onUserAction(action, user),
          ),
        );
      },
    );
  }
}
