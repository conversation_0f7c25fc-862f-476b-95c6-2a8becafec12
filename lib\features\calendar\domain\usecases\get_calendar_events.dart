import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../entities/calendar_event.dart';
import '../repositories/calendar_repository.dart';

/// Use case để lấy tất cả calendar events
class GetCalendarEvents implements UseCase<List<CalendarEvent>, NoParams> {
  final CalendarRepository repository;

  GetCalendarEvents(this.repository);

  @override
  Future<Either<Failure, List<CalendarEvent>>> call(NoParams params) async {
    return await repository.getAllEvents();
  }
}

/// Use case để lấy events theo ngày
class GetEventsByDate
    implements UseCase<List<CalendarEvent>, GetEventsByDateParams> {
  final CalendarRepository repository;

  GetEventsByDate(this.repository);

  @override
  Future<Either<Failure, List<CalendarEvent>>> call(
    GetEventsByDateParams params,
  ) async {
    return await repository.getEventsByDate(params.date);
  }
}

/// Parameters cho GetEventsByDate
class GetEventsByDateParams extends Equatable {
  final DateTime date;

  const GetEventsByDateParams({required this.date});

  @override
  List<Object> get props => [date];
}

/// Use case để lấy events trong khoảng thời gian
class GetEventsByDateRange
    implements UseCase<List<CalendarEvent>, GetEventsByDateRangeParams> {
  final CalendarRepository repository;

  GetEventsByDateRange(this.repository);

  @override
  Future<Either<Failure, List<CalendarEvent>>> call(
    GetEventsByDateRangeParams params,
  ) async {
    return await repository.getEventsByDateRange(
      params.startDate,
      params.endDate,
    );
  }
}

/// Parameters cho GetEventsByDateRange
class GetEventsByDateRangeParams extends Equatable {
  final DateTime startDate;
  final DateTime endDate;

  const GetEventsByDateRangeParams({
    required this.startDate,
    required this.endDate,
  });

  @override
  List<Object> get props => [startDate, endDate];
}

/// Use case để lấy events theo tháng
class GetEventsByMonth
    implements UseCase<List<CalendarEvent>, GetEventsByMonthParams> {
  final CalendarRepository repository;

  GetEventsByMonth(this.repository);

  @override
  Future<Either<Failure, List<CalendarEvent>>> call(
    GetEventsByMonthParams params,
  ) async {
    return await repository.getEventsByMonth(params.year, params.month);
  }
}

/// Parameters cho GetEventsByMonth
class GetEventsByMonthParams extends Equatable {
  final int year;
  final int month;

  const GetEventsByMonthParams({required this.year, required this.month});

  @override
  List<Object> get props => [year, month];
}

/// Use case để lấy events sắp tới
class GetUpcomingEvents
    implements UseCase<List<CalendarEvent>, GetUpcomingEventsParams> {
  final CalendarRepository repository;

  GetUpcomingEvents(this.repository);

  @override
  Future<Either<Failure, List<CalendarEvent>>> call(
    GetUpcomingEventsParams params,
  ) async {
    return await repository.getUpcomingEvents(limit: params.limit);
  }
}

/// Parameters cho GetUpcomingEvents
class GetUpcomingEventsParams extends Equatable {
  final int limit;

  const GetUpcomingEventsParams({this.limit = 10});

  @override
  List<Object> get props => [limit];
}

/// Use case để lấy events theo loại
class GetEventsByType
    implements UseCase<List<CalendarEvent>, GetEventsByTypeParams> {
  final CalendarRepository repository;

  GetEventsByType(this.repository);

  @override
  Future<Either<Failure, List<CalendarEvent>>> call(
    GetEventsByTypeParams params,
  ) async {
    return await repository.getEventsByType(params.type);
  }
}

/// Parameters cho GetEventsByType
class GetEventsByTypeParams extends Equatable {
  final EventType type;

  const GetEventsByTypeParams({required this.type});

  @override
  List<Object> get props => [type];
}
