import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:golderhr/core/extensions/l10n_extension.dart';
import 'package:golderhr/core/extensions/responsive_extension.dart';

import '../../../../shared/theme/app_colors.dart';
import '../../../../shared/widgets/show_custom_snackBar.dart';
import '../cubit/overtime_cubit.dart';
import '../cubit/overtime_state.dart';
import '../widgets/my_overtime_tab.dart';
import '../widgets/new_overtime_tab.dart';
import '../widgets/overtime_summary_card.dart';

class OvertimeView extends StatelessWidget {
  const OvertimeView({super.key});

  @override
  Widget build(BuildContext context) {
    final responsive = context.responsive;
    final l10n = context.l10n;
    final theme = Theme.of(context);

    return DefaultTabController(
      length: 2,
      child: Scaffold(
        backgroundColor: AppColors.background,
        appBar: AppBar(
          elevation: 0,
          backgroundColor: Colors.white,
          leading: IconButton(
            icon: const Icon(Icons.arrow_back, color: AppColors.textPrimary),
            onPressed: () => Navigator.of(context).pop(),
          ),
          title: Text(
            l10n.overtimeRequest,
            style: theme.textTheme.titleLarge?.copyWith(
              color: AppColors.textPrimary,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        body: BlocListener<OvertimeCubit, OvertimeState>(
          listener: (context, state) {
            if (state.errorMessage != null) {
              showTopSnackBar(
                context,
                title: l10n.error,
                message: state.errorMessage!,
                isError: true,
              );
              context.read<OvertimeCubit>().clearMessages();
            }

            if (state.successMessage != null) {
              showTopSnackBar(
                context,
                title: l10n.success,
                message: state.successMessage!,
                isError: false,
              );
              context.read<OvertimeCubit>().clearMessages();
            }
          },
          child: BlocBuilder<OvertimeCubit, OvertimeState>(
            builder: (context, state) {
              if (state.status == OvertimeStateStatus.loading) {
                return const Center(child: CircularProgressIndicator());
              }

              return RefreshIndicator(
                onRefresh: () => context.read<OvertimeCubit>().refreshData(),
                child: Column(
                  children: [
                    const OvertimeSummaryCard(),
                    Container(
                      margin: responsive.padding(horizontal: 16, vertical: 8),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(16),
                        boxShadow: [
                          BoxShadow(
                            color: AppColors.primaryBlue.withValues(
                              alpha: 0.08,
                            ),
                            blurRadius: 20,
                            offset: const Offset(0, 4),
                            spreadRadius: 0,
                          ),
                          BoxShadow(
                            color: Colors.black.withValues(alpha: 0.05),
                            blurRadius: 10,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: TabBar(
                        labelColor: AppColors.primaryBlue,
                        unselectedLabelColor: AppColors.textSecondary,
                        labelStyle: theme.textTheme.titleSmall?.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                        unselectedLabelStyle: theme.textTheme.titleSmall
                            ?.copyWith(fontWeight: FontWeight.w500),
                        indicator: BoxDecoration(
                          color: AppColors.primaryBlue.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(
                            color: AppColors.primaryBlue.withValues(alpha: 0.2),
                            width: 1,
                          ),
                        ),
                        indicatorSize: TabBarIndicatorSize.tab,
                        dividerColor: Colors.transparent,
                        tabs: [
                          Tab(
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                const Icon(Icons.add_circle_outline, size: 18),
                                const SizedBox(width: 8),
                                Text(l10n.newRequest),
                              ],
                            ),
                          ),
                          Tab(
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                const Icon(Icons.history_rounded, size: 18),
                                const SizedBox(width: 8),
                                Text(l10n.myOvertime),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                    const Expanded(
                      child: TabBarView(
                        children: [NewOvertimeTab(), MyOvertimeTab()],
                      ),
                    ),
                  ],
                ),
              );
            },
          ),
        ),
      ),
    );
  }
}
