import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:golderhr/core/extensions/app_theme_extension.dart';
import 'package:golderhr/core/extensions/l10n_extension.dart';
import 'package:golderhr/core/extensions/responsive_extension.dart';
import 'package:golderhr/shared/theme/app_text_styles.dart';
import 'package:iconsax/iconsax.dart';

import '../cubit/department_cubit.dart';
import '../../domain/entities/department_entity.dart';

class DepartmentDeleteConfirmationDialog extends StatelessWidget {
  final DepartmentEntity department;

  const DepartmentDeleteConfirmationDialog({
    super.key,
    required this.department,
  });

  @override
  Widget build(BuildContext context) {
    final responsive = context.responsive;
    final l10n = context.l10n;

    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(responsive.defaultRadius * 1.5),
      ),
      elevation: 10,
      child: Container(
        width: responsive.adaptiveValue<double>(
          mobile: 320,
          tablet: 400,
          mobileLandscape: 360,
          tabletLandscape: 440,
        ),
        padding: responsive.padding(all: 24),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(
            responsive.defaultRadius * 1.5,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              spreadRadius: 0,
              blurRadius: 20,
              offset: const Offset(0, 10),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Warning Icon
            Container(
              width: responsive.scaleWidth(64),
              height: responsive.scaleHeight(64),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    Colors.red.withValues(alpha: 0.15),
                    Colors.red.withValues(alpha: 0.05),
                  ],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(
                  responsive.defaultRadius * 2,
                ),
                border: Border.all(
                  color: Colors.red.withValues(alpha: 0.2),
                  width: 2,
                ),
              ),
              child: Icon(
                Iconsax.warning_2,
                color: Colors.red[600],
                size: responsive.scaleRadius(32),
              ),
            ),

            SizedBox(height: responsive.scaleHeight(20)),

            // Title
            Text(
              'Delete Department',
              style: AppTextStyle.bold(
                context,
                size: responsive.adaptiveValue<double>(
                  mobile: 20,
                  tablet: 22,
                  mobileLandscape: 21,
                  tabletLandscape: 24,
                ),
                color: Colors.grey[800],
              ),
              textAlign: TextAlign.center,
            ),

            SizedBox(height: responsive.scaleHeight(12)),

            // Content
            RichText(
              textAlign: TextAlign.center,
              text: TextSpan(
                style: AppTextStyle.regular(
                  context,
                  size: responsive.adaptiveValue<double>(
                    mobile: 14,
                    tablet: 15,
                    mobileLandscape: 14.5,
                    tabletLandscape: 16,
                  ),
                  color: Colors.grey[600],
                ),
                children: [
                  const TextSpan(
                    text: 'Are you sure you want to delete the department ',
                  ),
                  TextSpan(
                    text: '"${department.name}"',
                    style: AppTextStyle.bold(
                      context,
                      size: responsive.adaptiveValue<double>(
                        mobile: 14,
                        tablet: 15,
                        mobileLandscape: 14.5,
                        tabletLandscape: 16,
                      ),
                      color: Colors.red[600],
                    ),
                  ),
                  const TextSpan(text: '?\n\n'),
                  const TextSpan(
                    text: '⚠️ Warning: ',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  const TextSpan(
                    text:
                        'If users are assigned to this department, the deletion will fail. Please reassign users to other departments first.\n\n',
                  ),
                  const TextSpan(
                    text: 'This action cannot be undone.',
                    style: TextStyle(fontWeight: FontWeight.w500),
                  ),
                ],
              ),
            ),

            SizedBox(height: responsive.scaleHeight(28)),

            // Action Buttons
            Row(
              children: [
                Expanded(
                  child: OutlinedButton(
                    onPressed: () => Navigator.pop(context),
                    style: OutlinedButton.styleFrom(
                      padding: responsive.padding(vertical: 14),
                      side: BorderSide(
                        color: Colors.grey.withValues(alpha: 0.3),
                        width: 1.5,
                      ),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(
                          responsive.defaultRadius,
                        ),
                      ),
                      elevation: 0,
                    ),
                    child: Text(
                      l10n.cancel,
                      style: AppTextStyle.medium(
                        context,
                        size: 15,
                        color: Colors.grey[700],
                      ),
                    ),
                  ),
                ),

                SizedBox(width: responsive.scaleWidth(12)),

                Expanded(
                  child: ElevatedButton(
                    onPressed: () {
                      Navigator.pop(context);
                      context.read<DepartmentCubit>().deleteExistingDepartment(department.id);
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.red[600],
                      foregroundColor: Colors.white,
                      padding: responsive.padding(vertical: 14),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(
                          responsive.defaultRadius,
                        ),
                      ),
                      elevation: 2,
                      shadowColor: Colors.red.withValues(alpha: 0.3),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Iconsax.trash,
                          size: responsive.scaleRadius(16),
                          color: Colors.white,
                        ),
                        SizedBox(width: responsive.scaleWidth(8)),
                        Text(
                          'Delete',
                          style: AppTextStyle.medium(
                            context,
                            size: 15,
                            color: Colors.white,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
