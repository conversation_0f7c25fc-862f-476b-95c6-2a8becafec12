import 'package:dartz/dartz.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';

import 'package:golderhr/core/error/failures.dart';
import 'package:golderhr/features/notification/domain/repositories/notification_repository.dart';
import 'package:golderhr/features/notification/domain/usecases/notify_admin_new_request.dart';

import 'notify_admin_new_request_test.mocks.dart';

@GenerateMocks([NotificationRepository])
void main() {
  late NotifyAdminNewRequest usecase;
  late MockNotificationRepository mockRepository;

  setUp(() {
    mockRepository = MockNotificationRepository();
    usecase = NotifyAdminNewRequest(mockRepository);
  });

  const tParams = NotifyAdminNewRequestParams(
    requestType: 'leave',
    requestId: 'test-request-id',
    employeeName: '<PERSON>',
    requestDetails: 'Annual leave from 01/01/2024 to 05/01/2024',
  );

  test('should notify admin successfully when repository call succeeds', () async {
    // arrange
    when(mockRepository.notifyAdminNewRequest(
      requestType: anyNamed('requestType'),
      requestId: anyNamed('requestId'),
      employeeName: anyNamed('employeeName'),
      requestDetails: anyNamed('requestDetails'),
    )).thenAnswer((_) async => const Right(null));

    // act
    final result = await usecase(tParams);

    // assert
    expect(result, const Right(null));
    verify(mockRepository.notifyAdminNewRequest(
      requestType: tParams.requestType,
      requestId: tParams.requestId,
      employeeName: tParams.employeeName,
      requestDetails: tParams.requestDetails,
    ));
    verifyNoMoreInteractions(mockRepository);
  });

  test('should return failure when repository call fails', () async {
    // arrange
    const tFailure = ServerFailure('Server error');
    when(mockRepository.notifyAdminNewRequest(
      requestType: anyNamed('requestType'),
      requestId: anyNamed('requestId'),
      employeeName: anyNamed('employeeName'),
      requestDetails: anyNamed('requestDetails'),
    )).thenAnswer((_) async => const Left(tFailure));

    // act
    final result = await usecase(tParams);

    // assert
    expect(result, const Left(tFailure));
    verify(mockRepository.notifyAdminNewRequest(
      requestType: tParams.requestType,
      requestId: tParams.requestId,
      employeeName: tParams.employeeName,
      requestDetails: tParams.requestDetails,
    ));
    verifyNoMoreInteractions(mockRepository);
  });
}
