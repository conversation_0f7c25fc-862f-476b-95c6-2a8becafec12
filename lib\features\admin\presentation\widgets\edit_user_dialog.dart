import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:golderhr/core/extensions/app_theme_extension.dart';
import 'package:golderhr/core/extensions/l10n_extension.dart';
import 'package:golderhr/core/extensions/responsive_extension.dart';

import 'package:golderhr/shared/theme/app_text_styles.dart';
import 'package:golderhr/shared/utils/validators.dart';

import 'package:iconsax/iconsax.dart';
import '../cubit/admin_user_cubit.dart';
import '../../../role/presentation/cubit/role_cubit.dart';
import '../../../department/presentation/cubit/department_cubit.dart';
import '../../domain/entities/admin_user_entity.dart';
import '../../../role/domain/entities/role_entity.dart';
import '../../../department/domain/entities/department_entity.dart';
import '../../domain/usecases/admin_user_usecases.dart';
import '../../../../shared/widgets/show_custom_snackBar.dart';

class EditUserDialog extends StatefulWidget {
  final AdminUserEntity user;

  const EditUserDialog({super.key, required this.user});

  @override
  State<EditUserDialog> createState() => _EditUserDialogState();
}

class _EditUserDialogState extends State<EditUserDialog> {
  final _formKey = GlobalKey<FormState>();
  late TextEditingController _fullnameController;
  late TextEditingController _emailController;
  late TextEditingController _phoneController;

  late TextEditingController _positionController;

  String _selectedRole = '';
  String? _selectedDepartment;
  bool _isDisabled = false;

  List<RoleEntity> _availableRoles = [];
  List<DepartmentEntity> _availableDepartments = [];

  @override
  void initState() {
    super.initState();
    _initializeControllers();
    _loadDropdownData();
  }

  void _initializeControllers() {
    _fullnameController = TextEditingController(text: widget.user.fullname);
    _emailController = TextEditingController(text: widget.user.email);
    _phoneController = TextEditingController(text: widget.user.phone ?? '');

    _positionController = TextEditingController(
      text: widget.user.position ?? '',
    );
    _selectedRole = widget.user.role.name;
    _selectedDepartment = widget.user.department;
    _isDisabled = widget.user.isdisable;
  }

  void _loadDropdownData() {
    // Load roles and departments for dropdowns
    context.read<RoleCubit>().loadRolesForDropdown();
    context.read<DepartmentCubit>().loadDepartments();
  }

  @override
  void dispose() {
    _fullnameController.dispose();
    _emailController.dispose();
    _phoneController.dispose();

    _positionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = context.lightTheme;
    final responsive = context.responsive;
    final l10n = context.l10n;

    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(responsive.defaultRadius),
      ),
      child: Container(
        width: responsive.adaptiveValue<double>(
          mobile: responsive.widthPercentage(90),
          tablet: responsive.widthPercentage(70),
          mobileLandscape: responsive.widthPercentage(80),
          tabletLandscape: responsive.widthPercentage(60),
        ),
        constraints: BoxConstraints(
          maxWidth: responsive.adaptiveValue<double>(
            mobile: 500,
            tablet: 600,
            mobileLandscape: 550,
            tabletLandscape: 650,
          ),
          maxHeight: responsive.heightPercentage(85),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header
            Container(
              padding: responsive.padding(all: 20),
              decoration: BoxDecoration(
                color: theme.primaryColor,
                borderRadius: BorderRadius.vertical(
                  top: Radius.circular(responsive.defaultRadius),
                ),
              ),
              child: Row(
                children: [
                  CircleAvatar(
                    radius: responsive.adaptiveValue<double>(
                      mobile: 18,
                      tablet: 20,
                      mobileLandscape: 19,
                      tabletLandscape: 22,
                    ),
                    backgroundColor: Colors.white.withValues(alpha: 0.2),
                    backgroundImage: widget.user.avatar != null
                        ? NetworkImage(widget.user.avatar!)
                        : null,
                    child: widget.user.avatar == null
                        ? Text(
                            widget.user.initials,
                            style: AppTextStyle.bold(
                              context,
                              size: responsive.adaptiveValue<double>(
                                mobile: 14,
                                tablet: 16,
                                mobileLandscape: 15,
                                tabletLandscape: 17,
                              ),
                              color: Colors.white,
                            ),
                          )
                        : null,
                  ),
                  SizedBox(width: responsive.scaleWidth(16)),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          l10n.editUser,
                          style: AppTextStyle.bold(
                            context,
                            size: 18,
                            color: Colors.white,
                          ),
                        ),
                        Text(
                          widget.user.displayName,
                          style: AppTextStyle.regular(
                            context,
                            size: 14,
                            color: Colors.white.withValues(alpha: 0.9),
                          ),
                        ),
                      ],
                    ),
                  ),
                  IconButton(
                    onPressed: () => Navigator.pop(context),
                    icon: const Icon(Icons.close, color: Colors.white),
                  ),
                ],
              ),
            ),

            // Form Content
            Expanded(
              child: MultiBlocListener(
                listeners: [
                  BlocListener<AdminUserCubit, AdminUserState>(
                    listener: (context, state) {
                      if (state.hasError) {
                        showTopSnackBar(
                          context,
                          title: context.l10n.error,
                          message: state.error!,
                          isError: true,
                        );
                      } else if (!state.isProcessing && !state.hasError) {
                        // Success - close dialog
                        Navigator.pop(context);
                        showTopSnackBar(
                          context,
                          title: context.l10n.success,
                          message: context.l10n.userUpdatedSuccessfully,
                          isError: false,
                        );
                      }
                    },
                  ),
                  BlocListener<RoleCubit, RoleState>(
                    listener: (context, state) {
                      if (state.dropdownRoles.isNotEmpty) {
                        setState(() {
                          _availableRoles = state.dropdownRoles;
                        });
                      }
                    },
                  ),
                  BlocListener<DepartmentCubit, DepartmentState>(
                    listener: (context, state) {
                      if (state.departments.isNotEmpty) {
                        setState(() {
                          _availableDepartments = state.departments;
                        });
                      }
                      if (state.error != null) {
                        print('Department loading error: ${state.error}');
                      }
                    },
                  ),
                ],
                child: BlocBuilder<AdminUserCubit, AdminUserState>(
                  builder: (context, state) {
                    if (state.isProcessing) {
                      return const Center(child: CircularProgressIndicator());
                    }

                    return SingleChildScrollView(
                      padding: responsive.responsivePadding,
                      child: Form(
                        key: _formKey,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // Personal Information Section
                            _buildSectionHeader(
                              context.l10n.personalInformation,
                              Iconsax.user,
                            ),
                            SizedBox(height: responsive.defaultSpacing),

                            _buildTextField(
                              controller: _fullnameController,
                              label: context.l10n.fullName,
                              icon: Iconsax.user,
                              validator: (value) =>
                                  Validators.validateFullName(value, context),
                            ),

                            SizedBox(height: responsive.defaultSpacing),

                            _buildTextField(
                              controller: _emailController,
                              label: context.l10n.email,
                              icon: Iconsax.sms,
                              keyboardType: TextInputType.emailAddress,
                              validator: (value) =>
                                  Validators.validateEmail(value, context),
                            ),

                            SizedBox(height: responsive.defaultSpacing),

                            _buildTextField(
                              controller: _phoneController,
                              label: context.l10n.phoneNumber,
                              icon: Iconsax.call,
                              keyboardType: TextInputType.phone,
                            ),

                            SizedBox(height: responsive.defaultSpacing * 1.5),

                            // Work Information Section
                            _buildSectionHeader(
                              l10n.workInformation,
                              Iconsax.building,
                            ),
                            SizedBox(height: responsive.defaultSpacing),

                            // Department Dropdown
                            DropdownButtonFormField<String>(
                              value: _selectedDepartment,
                              decoration: InputDecoration(
                                labelText: context.l10n.department,
                                labelStyle: AppTextStyle.regular(
                                  context,
                                  size: responsive.adaptiveValue<double>(
                                    mobile: 12,
                                    tablet: 14,
                                    mobileLandscape: 13,
                                    tabletLandscape: 15,
                                  ),
                                ),
                                prefixIcon: Icon(
                                  Iconsax.building,
                                  size: responsive.adaptiveValue<double>(
                                    mobile: 20,
                                    tablet: 22,
                                    mobileLandscape: 21,
                                    tabletLandscape: 24,
                                  ),
                                ),
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(
                                    responsive.defaultRadius,
                                  ),
                                ),
                                filled: true,
                                fillColor: Colors.grey.shade50,
                                contentPadding: responsive.padding(
                                  horizontal: 16,
                                  vertical: 12,
                                ),
                              ),
                              hint: const Text('Select department'),
                              items: [
                                const DropdownMenuItem<String>(
                                  value: null,
                                  child: Text('No Department'),
                                ),
                                ..._availableDepartments.map((dept) {
                                  return DropdownMenuItem<String>(
                                    value: dept.name,
                                    child: Text(dept.name),
                                  );
                                }),
                              ],
                              onChanged: (value) {
                                setState(() {
                                  _selectedDepartment = value;
                                });
                              },
                            ),

                            SizedBox(height: responsive.defaultSpacing),

                            _buildTextField(
                              controller: _positionController,
                              label: context.l10n.position,
                              icon: Iconsax.user_tag,
                            ),

                            SizedBox(height: responsive.defaultSpacing * 1.5),

                            // Role & Status Section
                            _buildSectionHeader(
                              l10n.roleAndStatus,
                              Iconsax.security_user,
                            ),
                            SizedBox(height: responsive.defaultSpacing),

                            // Role Dropdown
                            DropdownButtonFormField<String>(
                              value: _selectedRole,
                              decoration: InputDecoration(
                                labelText: context.l10n.role,
                                labelStyle: AppTextStyle.regular(
                                  context,
                                  size: responsive.adaptiveValue<double>(
                                    mobile: 12,
                                    tablet: 14,
                                    mobileLandscape: 13,
                                    tabletLandscape: 15,
                                  ),
                                ),
                                prefixIcon: Icon(
                                  _getRoleIcon(_selectedRole),
                                  size: responsive.adaptiveValue<double>(
                                    mobile: 20,
                                    tablet: 22,
                                    mobileLandscape: 21,
                                    tabletLandscape: 24,
                                  ),
                                ),
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(
                                    responsive.defaultRadius,
                                  ),
                                ),
                                filled: true,
                                fillColor: Colors.grey.shade50,
                                contentPadding: responsive.padding(
                                  horizontal: 16,
                                  vertical: 12,
                                ),
                              ),
                              items: _availableRoles.map((role) {
                                return DropdownMenuItem(
                                  value: role.name,
                                  child: Row(
                                    children: [
                                      Icon(
                                        _getRoleIcon(role.name),
                                        size: responsive.adaptiveValue<double>(
                                          mobile: 18,
                                          tablet: 20,
                                          mobileLandscape: 19,
                                          tabletLandscape: 22,
                                        ),
                                        color: _getRoleColor(role.name),
                                      ),
                                      SizedBox(width: responsive.scaleWidth(8)),
                                      Text(
                                        _getRoleDisplayName(role.name, context),
                                        style: AppTextStyle.regular(
                                          context,
                                          size: responsive
                                              .adaptiveValue<double>(
                                                mobile: 14,
                                                tablet: 16,
                                                mobileLandscape: 15,
                                                tabletLandscape: 17,
                                              ),
                                        ),
                                      ),
                                    ],
                                  ),
                                );
                              }).toList(),
                              onChanged: (value) {
                                setState(() {
                                  _selectedRole = value!;
                                });
                              },
                              validator: (value) =>
                                  Validators.validateRole(value, context),
                            ),

                            SizedBox(height: responsive.defaultSpacing),

                            // Status Switch
                            Container(
                              padding: responsive.padding(all: 16),
                              decoration: BoxDecoration(
                                color: Colors.grey.shade50,
                                borderRadius: BorderRadius.circular(
                                  responsive.defaultRadius,
                                ),
                                border: Border.all(color: Colors.grey.shade300),
                              ),
                              child: Row(
                                children: [
                                  Icon(
                                    _isDisabled ? Iconsax.lock : Iconsax.unlock,
                                    size: responsive.adaptiveValue<double>(
                                      mobile: 20,
                                      tablet: 22,
                                      mobileLandscape: 21,
                                      tabletLandscape: 24,
                                    ),
                                    color: _isDisabled
                                        ? Colors.red
                                        : Colors.green,
                                  ),
                                  SizedBox(width: responsive.scaleWidth(12)),
                                  Expanded(
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          context.l10n.status,
                                          style: AppTextStyle.bold(
                                            context,
                                            size: responsive
                                                .adaptiveValue<double>(
                                                  mobile: 14,
                                                  tablet: 16,
                                                  mobileLandscape: 15,
                                                  tabletLandscape: 17,
                                                ),
                                          ),
                                        ),
                                        Text(
                                          _isDisabled
                                              ? context.l10n.disabled
                                              : context.l10n.active,
                                          style: AppTextStyle.regular(
                                            context,
                                            size: responsive
                                                .adaptiveValue<double>(
                                                  mobile: 12,
                                                  tablet: 14,
                                                  mobileLandscape: 13,
                                                  tabletLandscape: 15,
                                                ),
                                            color: Colors.grey[600],
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                  Switch(
                                    value: !_isDisabled,
                                    onChanged: (value) {
                                      setState(() {
                                        _isDisabled = !value;
                                      });
                                    },
                                    activeColor: Colors.green,
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                    );
                  },
                ),
              ),
            ),

            // Action Buttons
            Container(
              padding: responsive.padding(all: 20),
              decoration: BoxDecoration(
                color: Colors.grey.shade50,
                borderRadius: BorderRadius.vertical(
                  bottom: Radius.circular(responsive.defaultRadius),
                ),
              ),
              child: Row(
                children: [
                  Expanded(
                    child: OutlinedButton(
                      onPressed: () => Navigator.pop(context),
                      style: OutlinedButton.styleFrom(
                        padding: responsive.padding(vertical: 16),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(
                            responsive.defaultRadius,
                          ),
                        ),
                      ),
                      child: Text(
                        l10n.cancel,
                        style: AppTextStyle.medium(
                          context,
                          size: responsive.adaptiveValue<double>(
                            mobile: 14,
                            tablet: 16,
                            mobileLandscape: 15,
                            tabletLandscape: 17,
                          ),
                        ),
                      ),
                    ),
                  ),
                  SizedBox(width: responsive.scaleWidth(16)),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: _saveChanges,
                      style: ElevatedButton.styleFrom(
                        padding: responsive.padding(vertical: 16),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(
                            responsive.defaultRadius,
                          ),
                        ),
                      ),
                      child: Text(
                        l10n.saveChanges,
                        style: AppTextStyle.medium(
                          context,
                          size: responsive.adaptiveValue<double>(
                            mobile: 14,
                            tablet: 16,
                            mobileLandscape: 15,
                            tabletLandscape: 17,
                          ),
                          color: Colors.white,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionHeader(String title, IconData icon) {
    final responsive = context.responsive;

    return Row(
      children: [
        Icon(
          icon,
          size: responsive.adaptiveValue<double>(
            mobile: 18,
            tablet: 20,
            mobileLandscape: 19,
            tabletLandscape: 22,
          ),
          color: Colors.grey[700],
        ),
        SizedBox(width: responsive.scaleWidth(8)),
        Text(
          title,
          style: AppTextStyle.bold(context, size: 16, color: Colors.black87),
        ),
      ],
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required IconData icon,
    TextInputType? keyboardType,
    String? Function(String?)? validator,
  }) {
    final responsive = context.responsive;

    return TextFormField(
      controller: controller,
      keyboardType: keyboardType,
      validator: validator,
      style: AppTextStyle.regular(context, size: 14),
      decoration: InputDecoration(
        labelText: label,
        labelStyle: AppTextStyle.regular(context, size: 12),
        prefixIcon: Icon(
          icon,
          size: responsive.adaptiveValue<double>(
            mobile: 20,
            tablet: 22,
            mobileLandscape: 21,
            tabletLandscape: 24,
          ),
        ),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(responsive.defaultRadius),
        ),
        filled: true,
        fillColor: Colors.grey.shade50,
        contentPadding: responsive.padding(horizontal: 16, vertical: 12),
      ),
    );
  }

  void _saveChanges() {
    if (_formKey.currentState!.validate()) {
      final params = UpdateUserParams(
        userId: widget.user.id,
        fullname: _fullnameController.text.trim().isEmpty
            ? null
            : _fullnameController.text.trim(),
        email: _emailController.text.trim().isEmpty
            ? null
            : _emailController.text.trim(),
        phone: _phoneController.text.trim().isEmpty
            ? null
            : _phoneController.text.trim(),
        department: _selectedDepartment != widget.user.department
            ? _selectedDepartment
            : null,
        position: _positionController.text.trim().isEmpty
            ? null
            : _positionController.text.trim(),
        role: _selectedRole != widget.user.role.name ? _selectedRole : null,
        isdisable: _isDisabled != widget.user.isdisable ? _isDisabled : null,
      );

      context.read<AdminUserCubit>().updateUser(params);
    }
  }

  IconData _getRoleIcon(String role) {
    switch (role.toLowerCase()) {
      case 'admin':
        return Icons.admin_panel_settings;
      case 'hr':
        return Icons.people;
      case 'manager':
        return Icons.supervisor_account;
      case 'user':
        return Icons.person;
      default:
        return Icons.person;
    }
  }

  Color _getRoleColor(String role) {
    switch (role.toLowerCase()) {
      case 'admin':
        return Colors.red;
      case 'hr':
        return Colors.purple;
      case 'manager':
        return Colors.blue;
      case 'user':
        return Colors.green;
      default:
        return Colors.grey;
    }
  }

  String _getRoleDisplayName(String role, BuildContext context) {
    final l10n = context.l10n;
    switch (role.toLowerCase()) {
      case 'admin':
        return l10n.roleAdmin;
      case 'hr':
        return l10n.roleHR;
      case 'manager':
        return l10n.roleManager;
      case 'user':
        return l10n.roleEmployee;
      default:
        return role;
    }
  }
}
