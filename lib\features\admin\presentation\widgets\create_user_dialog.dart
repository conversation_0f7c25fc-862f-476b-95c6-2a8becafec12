import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:iconsax/iconsax.dart';
import '../../../../shared/utils/validators.dart';
import '../../../../shared/widgets/show_custom_snackBar.dart';
import '../../../../core/extensions/l10n_extension.dart';
import '../../../../core/extensions/responsive_extension.dart';
import '../../../../shared/widgets/responsive_layout.dart';
import '../../../../shared/theme/app_text_styles.dart';
import '../cubit/admin_user_cubit.dart';
import '../../../role/presentation/cubit/role_cubit.dart';
import '../../../department/presentation/cubit/department_cubit.dart';
import '../../domain/usecases/create_user_usecase.dart';

import '../../../role/domain/entities/role_entity.dart';
import '../../../department/domain/entities/department_entity.dart';

class CreateUserDialog extends StatefulWidget {
  final VoidCallback? onUserCreated;

  const CreateUserDialog({super.key, this.onUserCreated});

  @override
  State<CreateUserDialog> createState() => _CreateUserDialogState();
}

class _CreateUserDialogState extends State<CreateUserDialog> {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  final _firstNameController = TextEditingController();
  final _lastNameController = TextEditingController();
  final _phoneController = TextEditingController();

  String? _selectedRole;
  String? _selectedDepartment;
  bool _isPasswordVisible = false;
  bool _isLoading = false;

  List<RoleEntity> _roles = [];
  List<DepartmentEntity> _departments = [];
  bool _isLoadingRoles = true;
  bool _isLoadingDepartments = true;

  @override
  void initState() {
    super.initState();
    _loadInitialData();
  }

  void _loadInitialData() {
    // Load roles
    context.read<RoleCubit>().loadRolesForDropdown();
    // Load departments
    context.read<DepartmentCubit>().loadDepartments();
  }

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    _firstNameController.dispose();
    _lastNameController.dispose();
    _phoneController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final l10n = context.l10n;
    final responsive = context.responsive;

    return MultiBlocListener(
      listeners: [
        BlocListener<RoleCubit, RoleState>(
          listener: (context, state) {
            if (state.dropdownRoles.isNotEmpty) {
              setState(() {
                _roles = state.dropdownRoles;
                _isLoadingRoles = false;
                if (_selectedRole == null && _roles.isNotEmpty) {
                  _selectedRole = _roles.first.id;
                }
              });
            }
          },
        ),
        BlocListener<DepartmentCubit, DepartmentState>(
          listener: (context, state) {
            if (state.departments.isNotEmpty) {
              setState(() {
                _departments = state.departments;
                _isLoadingDepartments = false;
                if (_selectedDepartment == null && _departments.isNotEmpty) {
                  _selectedDepartment = _departments.first.id;
                }
              });
            }
          },
        ),
      ],
      child: Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(responsive.defaultRadius),
        ),
        child: Container(
          width: responsive.adaptiveValue<double>(
            mobile: responsive.widthPercentage(90),
            tablet: responsive.widthPercentage(70),
            mobileLandscape: responsive.widthPercentage(80),
            tabletLandscape: responsive.widthPercentage(60),
          ),
          constraints: BoxConstraints(
            maxWidth: responsive.adaptiveValue<double>(
              mobile: 400,
              tablet: 600,
              mobileLandscape: 500,
              tabletLandscape: 700,
            ),
            maxHeight: responsive.heightPercentage(70),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Header
              Container(
                padding: responsive.padding(all: 20),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [Colors.green.shade600, Colors.green.shade400],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(responsive.defaultRadius),
                    topRight: Radius.circular(responsive.defaultRadius),
                  ),
                ),
                child: Row(
                  children: [
                    Container(
                      padding: responsive.padding(all: 12),
                      decoration: BoxDecoration(
                        color: Colors.white.withValues(alpha: 0.2),
                        borderRadius: BorderRadius.circular(
                          responsive.scaleRadius(12),
                        ),
                      ),
                      child: Icon(
                        Icons.person_add,
                        color: Colors.white,
                        size: responsive.adaptiveValue<double>(
                          mobile: 24,
                          tablet: 28,
                          mobileLandscape: 26,
                          tabletLandscape: 32,
                        ),
                      ),
                    ),
                    SizedBox(width: responsive.scaleWidth(16)),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            l10n.addNewUser,
                            style: AppTextStyle.bold(
                              context,
                              size: 18,
                              color: Colors.white,
                            ),
                          ),
                          SizedBox(height: responsive.scaleHeight(4)),
                          Text(
                            l10n.createNewUserAccount,
                            style: AppTextStyle.regular(
                              context,
                              size: 14,
                              color: Colors.white.withValues(alpha: 0.9),
                            ),
                          ),
                        ],
                      ),
                    ),
                    IconButton(
                      onPressed: () => Navigator.of(context).pop(),
                      icon: const Icon(Icons.close, color: Colors.white),
                      style: IconButton.styleFrom(
                        backgroundColor: Colors.white.withValues(alpha: 0.2),
                      ),
                    ),
                  ],
                ),
              ),

              // Form
              Expanded(
                child: Form(
                  key: _formKey,
                  child: SingleChildScrollView(
                    padding: responsive.responsivePadding,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Email
                        TextFormField(
                          controller: _emailController,
                          decoration: InputDecoration(
                            labelText: '${l10n.email} *',
                            prefixIcon: const Icon(Iconsax.sms),
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(
                                responsive.defaultRadius,
                              ),
                            ),
                          ),
                          validator: (value) =>
                              Validators.validateEmail(value, context),
                          keyboardType: TextInputType.emailAddress,
                        ),

                        SizedBox(height: responsive.defaultSpacing),

                        // Password
                        TextFormField(
                          controller: _passwordController,
                          decoration: InputDecoration(
                            labelText: '${l10n.password} *',
                            prefixIcon: const Icon(Iconsax.lock),
                            suffixIcon: IconButton(
                              onPressed: () {
                                setState(() {
                                  _isPasswordVisible = !_isPasswordVisible;
                                });
                              },
                              icon: Icon(
                                _isPasswordVisible
                                    ? Iconsax.eye_slash
                                    : Iconsax.eye,
                              ),
                            ),
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(
                                responsive.defaultRadius,
                              ),
                            ),
                          ),
                          validator: (value) =>
                              Validators.validateStrongPassword(value, context),
                          obscureText: !_isPasswordVisible,
                        ),

                        SizedBox(height: responsive.defaultSpacing),

                        // First Name & Last Name
                        ResponsiveLayout(
                          mobile: Column(
                            children: [
                              TextFormField(
                                controller: _firstNameController,
                                decoration: InputDecoration(
                                  labelText: '${l10n.firstName} *',
                                  prefixIcon: const Icon(Iconsax.user),
                                  border: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(
                                      responsive.defaultRadius,
                                    ),
                                  ),
                                  contentPadding: responsive.padding(
                                    horizontal: 12,
                                    vertical: 12,
                                  ),
                                ),
                                validator: (value) =>
                                    Validators.validateFirstName(
                                      value,
                                      context,
                                    ),
                              ),
                              SizedBox(height: responsive.defaultSpacing / 2),
                              TextFormField(
                                controller: _lastNameController,
                                decoration: InputDecoration(
                                  labelText: '${l10n.lastName} *',
                                  prefixIcon: const Icon(Iconsax.user),
                                  border: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(
                                      responsive.defaultRadius,
                                    ),
                                  ),
                                  contentPadding: responsive.padding(
                                    horizontal: 12,
                                    vertical: 12,
                                  ),
                                ),
                                validator: (value) =>
                                    Validators.validateLastName(value, context),
                              ),
                            ],
                          ),
                          tablet: Row(
                            children: [
                              Flexible(
                                flex: 1,
                                child: TextFormField(
                                  controller: _firstNameController,
                                  decoration: InputDecoration(
                                    labelText: '${l10n.firstName} *',
                                    prefixIcon: const Icon(Iconsax.user),
                                    border: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(
                                        responsive.defaultRadius,
                                      ),
                                    ),
                                    contentPadding: responsive.padding(
                                      horizontal: 12,
                                      vertical: 12,
                                    ),
                                  ),
                                  validator: (value) =>
                                      Validators.validateFirstName(
                                        value,
                                        context,
                                      ),
                                ),
                              ),
                              SizedBox(width: responsive.defaultSpacing / 2),
                              Flexible(
                                flex: 1,
                                child: TextFormField(
                                  controller: _lastNameController,
                                  decoration: InputDecoration(
                                    labelText: '${l10n.lastName} *',
                                    prefixIcon: const Icon(Iconsax.user),
                                    border: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(
                                        responsive.defaultRadius,
                                      ),
                                    ),
                                    contentPadding: responsive.padding(
                                      horizontal: 12,
                                      vertical: 12,
                                    ),
                                  ),
                                  validator: (value) =>
                                      Validators.validateLastName(
                                        value,
                                        context,
                                      ),
                                ),
                              ),
                            ],
                          ),
                        ),

                        SizedBox(height: responsive.defaultSpacing),

                        // Phone
                        TextFormField(
                          controller: _phoneController,
                          decoration: InputDecoration(
                            labelText: l10n.phoneNumber,
                            prefixIcon: const Icon(Iconsax.call),
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(
                                responsive.defaultRadius,
                              ),
                            ),
                          ),
                          validator: (value) =>
                              Validators.validatePhoneNumber(value, context),
                          keyboardType: TextInputType.phone,
                        ),

                        SizedBox(height: responsive.defaultSpacing),

                        // Role & Department
                        ResponsiveLayout(
                          mobile: Column(
                            children: [
                              _isLoadingRoles
                                  ? Container(
                                      height: 56,
                                      decoration: BoxDecoration(
                                        border: Border.all(color: Colors.grey),
                                        borderRadius: BorderRadius.circular(
                                          responsive.defaultRadius,
                                        ),
                                      ),
                                      child: const Center(
                                        child: CircularProgressIndicator(),
                                      ),
                                    )
                                  : DropdownButtonFormField<String>(
                                      value: _selectedRole,
                                      style: AppTextStyle.regular(
                                        context,
                                        size: 14,
                                      ),
                                      decoration: InputDecoration(
                                        labelText: '${l10n.role} *',
                                        labelStyle: AppTextStyle.regular(
                                          context,
                                          size: 12,
                                        ),
                                        prefixIcon: Icon(
                                          Iconsax.security_user,
                                          size: responsive.scaleRadius(18),
                                        ),
                                        border: OutlineInputBorder(
                                          borderRadius: BorderRadius.circular(
                                            responsive.defaultRadius,
                                          ),
                                        ),
                                        contentPadding: responsive.padding(
                                          horizontal: 8,
                                          vertical: 10,
                                        ),
                                        isDense: true,
                                      ),
                                      items: _roles.map((role) {
                                        return DropdownMenuItem<String>(
                                          value: role.id,
                                          child: Text(
                                            role.name.toUpperCase(),
                                            style: AppTextStyle.medium(
                                              context,
                                              size: 14,
                                              color: Colors.black87,
                                            ),
                                          ),
                                        );
                                      }).toList(),
                                      onChanged: (value) {
                                        setState(() {
                                          _selectedRole = value!;
                                        });
                                      },
                                    ),
                              SizedBox(height: responsive.defaultSpacing / 2),
                              _isLoadingDepartments
                                  ? Container(
                                      height: 56,
                                      decoration: BoxDecoration(
                                        border: Border.all(color: Colors.grey),
                                        borderRadius: BorderRadius.circular(
                                          responsive.defaultRadius,
                                        ),
                                      ),
                                      child: const Center(
                                        child: CircularProgressIndicator(),
                                      ),
                                    )
                                  : DropdownButtonFormField<String>(
                                      value: _selectedDepartment,
                                      style: AppTextStyle.medium(
                                        context,
                                        size: 14,
                                        color: Colors.black87,
                                      ),
                                      decoration: InputDecoration(
                                        labelText: '${l10n.department} *',
                                        labelStyle: AppTextStyle.regular(
                                          context,
                                          size: 12,
                                        ),
                                        prefixIcon: Icon(
                                          Iconsax.building,
                                          size: responsive.scaleRadius(18),
                                        ),
                                        border: OutlineInputBorder(
                                          borderRadius: BorderRadius.circular(
                                            responsive.defaultRadius,
                                          ),
                                        ),
                                        contentPadding: responsive.padding(
                                          horizontal: 8,
                                          vertical: 10,
                                        ),
                                        isDense: true,
                                      ),
                                      items: _departments.map((dept) {
                                        return DropdownMenuItem<String>(
                                          value: dept.id,
                                          child: Text(
                                            dept.name,
                                            style: AppTextStyle.medium(
                                              context,
                                              size: 14,
                                              color: Colors.black87,
                                            ),
                                          ),
                                        );
                                      }).toList(),
                                      onChanged: (value) {
                                        setState(() {
                                          _selectedDepartment = value!;
                                        });
                                      },
                                    ),
                            ],
                          ),
                          tablet: Row(
                            children: [
                              Flexible(
                                flex: 1,
                                child: DropdownButtonFormField<String>(
                                  value: _selectedRole,
                                  style: AppTextStyle.regular(
                                    context,
                                    size: 14,
                                  ),
                                  decoration: InputDecoration(
                                    labelText: '${l10n.role} *',
                                    labelStyle: AppTextStyle.regular(
                                      context,
                                      size: 12,
                                    ),
                                    prefixIcon: Icon(
                                      Iconsax.security_user,
                                      size: responsive.scaleRadius(18),
                                    ),
                                    border: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(
                                        responsive.defaultRadius,
                                      ),
                                    ),
                                    contentPadding: responsive.padding(
                                      horizontal: 8,
                                      vertical: 10,
                                    ),
                                    isDense: true,
                                  ),
                                  items: _roles.map((role) {
                                    return DropdownMenuItem<String>(
                                      value: role.id,
                                      child: Text(
                                        role.name.toUpperCase(),
                                        style: AppTextStyle.medium(
                                          context,
                                          size: 14,
                                          color: Colors.black87,
                                        ),
                                      ),
                                    );
                                  }).toList(),
                                  onChanged: (value) {
                                    setState(() {
                                      _selectedRole = value!;
                                    });
                                  },
                                ),
                              ),
                              SizedBox(width: responsive.defaultSpacing / 2),
                              Flexible(
                                flex: 1,
                                child: DropdownButtonFormField<String>(
                                  value: _selectedDepartment,
                                  style: AppTextStyle.medium(
                                    context,
                                    size: 14,
                                    color: Colors.black87,
                                  ),
                                  decoration: InputDecoration(
                                    labelText: '${l10n.department} *',
                                    labelStyle: AppTextStyle.regular(
                                      context,
                                      size: 12,
                                    ),
                                    prefixIcon: Icon(
                                      Iconsax.building,
                                      size: responsive.scaleRadius(18),
                                    ),
                                    border: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(
                                        responsive.defaultRadius,
                                      ),
                                    ),
                                    contentPadding: responsive.padding(
                                      horizontal: 8,
                                      vertical: 10,
                                    ),
                                    isDense: true,
                                  ),
                                  items: _departments.map((dept) {
                                    return DropdownMenuItem<String>(
                                      value: dept.id,
                                      child: Text(
                                        dept.name,
                                        style: AppTextStyle.medium(
                                          context,
                                          size: 14,
                                          color: Colors.black87,
                                        ),
                                      ),
                                    );
                                  }).toList(),
                                  onChanged: (value) {
                                    setState(() {
                                      _selectedDepartment = value!;
                                    });
                                  },
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),

              // Action Buttons
              Container(
                padding: responsive.padding(all: 20),
                decoration: BoxDecoration(
                  color: Colors.grey.shade50,
                  borderRadius: BorderRadius.only(
                    bottomLeft: Radius.circular(responsive.defaultRadius),
                    bottomRight: Radius.circular(responsive.defaultRadius),
                  ),
                ),
                child: Row(
                  children: [
                    Expanded(
                      child: OutlinedButton(
                        onPressed: _isLoading
                            ? null
                            : () => Navigator.of(context).pop(),
                        style: OutlinedButton.styleFrom(
                          padding: responsive.padding(vertical: 12),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(
                              responsive.defaultRadius,
                            ),
                          ),
                          side: BorderSide(color: Colors.grey.shade400),
                        ),
                        child: Text(
                          l10n.cancel,
                          style: AppTextStyle.medium(context, size: 16),
                        ),
                      ),
                    ),
                    SizedBox(width: responsive.scaleWidth(16)),
                    Expanded(
                      child: ElevatedButton(
                        onPressed: _isLoading ? null : _createUser,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.green.shade600,
                          foregroundColor: Colors.white,
                          padding: responsive.padding(vertical: 12),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(
                              responsive.defaultRadius,
                            ),
                          ),
                          elevation: 2,
                        ),
                        child: _isLoading
                            ? SizedBox(
                                height: responsive.scaleHeight(20),
                                width: responsive.scaleWidth(20),
                                child: const CircularProgressIndicator(
                                  strokeWidth: 2,
                                  valueColor: AlwaysStoppedAnimation<Color>(
                                    Colors.white,
                                  ),
                                ),
                              )
                            : Text(
                                l10n.createUser,
                                style: AppTextStyle.medium(
                                  context,
                                  size: 16,
                                  color: Colors.white,
                                ),
                              ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _createUser() async {
    if (!_formKey.currentState!.validate()) return;

    // Validate role and department selection
    if (_selectedRole == null) {
      showTopSnackBar(
        context,
        title: 'Error',
        message: 'Please select a role',
        isError: true,
      );
      return;
    }

    if (_selectedDepartment == null) {
      showTopSnackBar(
        context,
        title: 'Error',
        message: 'Please select a department',
        isError: true,
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final fullname =
          '${_firstNameController.text.trim()} ${_lastNameController.text.trim()}';

      await context.read<AdminUserCubit>().createUser(
        CreateUserParams(
          fullname: fullname,
          email: _emailController.text.trim(),
          password: _passwordController.text,
          phone: _phoneController.text.trim().isEmpty
              ? null
              : _phoneController.text.trim(),
          role: _selectedRole!,
          department: _selectedDepartment!,
        ),
      );

      if (mounted) {
        Navigator.of(context).pop();
        showTopSnackBar(
          context,
          title: context.l10n.success,
          message: context.l10n.userCreatedSuccessfully,
          isError: false,
        );
        widget.onUserCreated?.call();
      }
    } catch (e) {
      if (mounted) {
        showTopSnackBar(
          context,
          title: context.l10n.error,
          message: context.l10n.failedToCreateUser(e.toString()),
          isError: true,
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}
