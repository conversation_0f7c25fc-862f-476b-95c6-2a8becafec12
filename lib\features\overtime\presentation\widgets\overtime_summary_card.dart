import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:golderhr/core/extensions/l10n_extension.dart';
import 'package:golderhr/core/extensions/responsive_extension.dart';
import '../../../../core/responsive/responsive.dart';
import '../../../../shared/theme/app_colors.dart';
import '../cubit/overtime_cubit.dart';
import '../cubit/overtime_state.dart';

class OvertimeSummaryCard extends StatelessWidget {
  const OvertimeSummaryCard({super.key});

  @override
  Widget build(BuildContext context) {
    final responsive = context.responsive;
    final theme = Theme.of(context);
    final l10n = context.l10n;

    return Container(
      margin: responsive.padding(horizontal: 16, vertical: 8),
      padding: responsive.padding(all: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: AppColors.primaryBlue.withValues(alpha: 0.08),
            blurRadius: 20,
            offset: const Offset(0, 4),
            spreadRadius: 0,
          ),
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
        border: Border.all(
          color: AppColors.primaryBlue.withValues(alpha: 0.1),
          width: 1,
        ),
      ),
      child: BlocBuilder<OvertimeCubit, OvertimeState>(
        builder: (context, state) {
          final summary = state.summary;

          return Column(
            children: [
              // Header with title and view details button
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: AppColors.primaryBlue.withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Icon(
                            Icons.analytics_rounded,
                            color: AppColors.primaryBlue,
                            size: 20,
                          ),
                        ),
                        SizedBox(width: responsive.widthPercentage(2)),
                        Flexible(
                          child: Text(
                            l10n.overtimeSummary,
                            style: theme.textTheme.titleMedium?.copyWith(
                              color: AppColors.textPrimary,
                              fontWeight: FontWeight.bold,
                            ),
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ],
                    ),
                  ),
                  TextButton(
                    onPressed: () {
                      // TODO: Navigate to detailed summary page
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text('Detailed view coming soon'),
                        ),
                      );
                    },
                    child: Text(
                      'Details',
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: AppColors.primaryBlue,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ],
              ),
              SizedBox(height: responsive.heightPercentage(1.5)),

              // Compact summary row
              Row(
                children: [
                  Expanded(
                    child: _buildCompactSummaryItem(
                      responsive,
                      theme,
                      'This Month',
                      '${summary?.thisMonthHours.toStringAsFixed(1) ?? '0.0'}h',
                      Icons.calendar_month_rounded,
                      AppColors.primaryBlue,
                    ),
                  ),
                  Container(
                    width: 1,
                    height: 40,
                    color: AppColors.textSecondary.withValues(alpha: 0.2),
                  ),
                  Expanded(
                    child: _buildCompactSummaryItem(
                      responsive,
                      theme,
                      'Pending',
                      '${summary?.pendingRequests ?? 0}',
                      Icons.pending_actions_rounded,
                      AppColors.warning,
                    ),
                  ),
                  Container(
                    width: 1,
                    height: 40,
                    color: AppColors.textSecondary.withValues(alpha: 0.2),
                  ),
                  Expanded(
                    child: _buildCompactSummaryItem(
                      responsive,
                      theme,
                      'Approved',
                      '${summary?.approvedRequests ?? 0}',
                      Icons.check_circle_rounded,
                      AppColors.success,
                    ),
                  ),
                ],
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildCompactSummaryItem(
    Responsive responsive,
    ThemeData theme,
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Padding(
      padding: responsive.padding(horizontal: 8, vertical: 4),
      child: Column(
        children: [
          Icon(icon, color: color, size: 20),
          SizedBox(height: responsive.heightPercentage(0.5)),
          Text(
            value,
            style: theme.textTheme.titleSmall?.copyWith(
              color: color,
              fontWeight: FontWeight.bold,
            ),
          ),
          Text(
            title,
            style: theme.textTheme.bodySmall?.copyWith(
              color: AppColors.textSecondary,
              fontSize: responsive.fontSize(10),
            ),
            textAlign: TextAlign.center,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }
}
