// Path: lib/features/auth/presentation/cubit/auth_cubit.dart

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_storage/get_storage.dart';
import 'package:golderhr/core/logger/app_logger.dart';
import 'package:golderhr/core/network/dio_client.dart';
import 'package:golderhr/core/services/biometric_service.dart';
import 'package:golderhr/core/services/firebase_service.dart';
import 'package:golderhr/core/services/flutter_secure_storage.dart';
import 'package:golderhr/core/usecases/usecase.dart';
import 'package:golderhr/features/auth/data/datasources/locals/auth_local_data_source.dart';
import 'package:golderhr/features/auth/domain/usecases/forgot_password.dart';
import 'package:golderhr/features/auth/domain/usecases/get_auth_status.dart';
import 'package:golderhr/features/auth/domain/usecases/login.dart';
import 'package:golderhr/features/auth/domain/usecases/logout.dart';
import 'package:golderhr/features/auth/domain/usecases/register.dart';
import 'package:golderhr/features/auth/domain/usecases/resend_otp.dart';
import 'package:golderhr/features/auth/domain/usecases/reset_password.dart';
import 'package:golderhr/features/auth/domain/usecases/verify_otp.dart';
import 'package:golderhr/features/cubit/user_cubit.dart';

import 'auth_state.dart';

/// Quản lý trạng thái xác thực của toàn bộ ứng dụng.
/// Chịu trách nhiệm gọi các UseCase và xử lý logic luồng xác thực.
class AuthCubit extends Cubit<AuthState> {
  final LoginUseCase _loginUseCase;
  final LogoutUseCase _logoutUseCase;
  final RegisterUseCase _registerUseCase;
  final GetAuthStatusUseCase _getAuthStatusUseCase;
  final ForgotPasswordUseCase _forgotPasswordUseCase;
  final VerifyOtpUseCase _verifyOtpUseCase;
  final ResendOtpUseCase _resendOtpUseCase;
  final ResetPasswordUseCase _resetPasswordUseCase;

  final UserCubit _userCubit;
  final AuthLocalDataSource _localDataSource;
  final DioClient _dioClient;
  final BiometricService _biometricService;
  final SecureStorageService _secureStorage;
  final _storage = GetStorage();

  String? _resetToken;

  AuthCubit({
    required LoginUseCase loginUseCase,
    required LogoutUseCase logoutUseCase,
    required RegisterUseCase registerUseCase,
    required GetAuthStatusUseCase getAuthStatusUseCase,
    required ForgotPasswordUseCase forgotPasswordUseCase,
    required VerifyOtpUseCase verifyOtpUseCase,
    required ResendOtpUseCase resendOtpUseCase,
    required ResetPasswordUseCase resetPasswordUseCase,
    required UserCubit userCubit,
    required AuthLocalDataSource localDataSource,
    required DioClient dioClient,
    required BiometricService biometricService,
    required SecureStorageService secureStorage,
  }) : _loginUseCase = loginUseCase,
       _logoutUseCase = logoutUseCase,
       _getAuthStatusUseCase = getAuthStatusUseCase,
       _registerUseCase = registerUseCase,
       _forgotPasswordUseCase = forgotPasswordUseCase,
       _verifyOtpUseCase = verifyOtpUseCase,
       _resendOtpUseCase = resendOtpUseCase,
       _resetPasswordUseCase = resetPasswordUseCase,
       _userCubit = userCubit,
       _localDataSource = localDataSource,
       _dioClient = dioClient,
       _biometricService = biometricService,
       _secureStorage = secureStorage,
       super(AuthInitial());

  Future<void> login(
    String email,
    String password, {
    bool rememberMe = false,
  }) async {
    emit(AuthLoading());

    final result = await _loginUseCase(
      LoginParams(email: email, password: password),
    );

    result.fold(
      (failure) => emit(AuthError('Login Failed: ${failure.message}')),
      (user) async {
        await _localDataSource.clearCache();
        await _localDataSource.cacheUser(user as dynamic);

        if (rememberMe) {
          await _secureStorage.write('remembered_email', email);
          await _secureStorage.write('remembered_password', password);
        } else {
          await _secureStorage.delete('remembered_email');
          await _secureStorage.delete('remembered_password');
        }
        await _storage.write('is_remember_me', rememberMe);

        _userCubit.updateUser(user);

        // Đăng ký FCM token với backend sau khi login thành công
        try {
          await FirebaseService().retryFCMRegistration();
        } catch (e) {
          AppLogger.error('Failed to register FCM token after login', e);
        }

        emit(Authenticated(user: user));
      },
    );
  }

  Future<void> logout() async {
    emit(AuthLoading());

    // Cố gắng đăng xuất khỏi server, nhưng không để lỗi server chặn việc logout ở client.
    final result = await _logoutUseCase(NoParams());
    result.fold(
      (failure) => AppLogger.error(
        'Server logout failed, but clearing local session anyway: ${failure.message}',
      ),
      (_) => AppLogger.info('Server logout successful.'),
    );

    // Dọn dẹp phía client BẤT KỂ kết quả từ server
    await _localDataSource.clearCache();
    await _dioClient.clearToken();
    _userCubit.clearUser();

    // Thông báo cho UI rằng đã đăng xuất
    emit(Unauthenticated());
  }

  // --- Các hàm còn lại ---
  Future<void> checkAuthStatus() async {
    final result = await _getAuthStatusUseCase(NoParams());
    result.fold((failure) => emit(Unauthenticated()), (user) async {
      if (user != null) {
        _userCubit.updateUser(user);

        // Đăng ký FCM token với backend nếu user đã login từ trước
        try {
          await FirebaseService().retryFCMRegistration();
        } catch (e) {
          AppLogger.error('Failed to register FCM token on auth check', e);
        }

        emit(Authenticated(user: user));
      } else {
        emit(Unauthenticated());
      }
    });
  }

  Future<void> register({
    required String fullName,
    required String email,
    required String password,
  }) async {
    // Tương tự hàm login, đảm bảo thứ tự
    emit(AuthLoading());
    final result = await _registerUseCase(
      RegisterParams(fullName: fullName, email: email, password: password),
    );
    result.fold(
      (failure) => emit(AuthError('Registration Failed: ${failure.message}')),
      (user) async {
        await _localDataSource.cacheUser(user as dynamic);
        _userCubit.updateUser(user);
        emit(Authenticated(user: user));
      },
    );
  }

  Future<void> loginWithBiometric() async {
    emit(AuthLoading());
    try {
      final isAuthenticated = await _biometricService.authenticate();
      if (!isAuthenticated) {
        emit(Unauthenticated());
        return;
      }
      final isRememberMe = _storage.read('is_remember_me') ?? false;
      if (!isRememberMe) {
        emit(AuthError('Please enable "Remember Me" to use biometrics.'));
        return;
      }
      final email = await _secureStorage.read('remembered_email');
      final password = await _secureStorage.read('remembered_password');
      if (email != null && password != null) {
        await login(email, password, rememberMe: true);
      } else {
        emit(AuthError('No saved credentials for biometric login.'));
      }
    } catch (e) {
      emit(AuthError('Biometric Login Failed: $e'));
    }
  }

  Future<void> forgotPassword(String email) async {
    emit(AuthLoading());
    final result = await _forgotPasswordUseCase(
      ForgotPasswordParams(email: email),
    );
    result.fold(
      (failure) =>
          emit(AuthError('Forgot Password Failed: ${failure.message}')),
      (_) => emit(ForgotPasswordOtpSent()),
    );
  }

  Future<void> verifyOtp(String otp) async {
    emit(AuthLoading());
    final result = await _verifyOtpUseCase(VerifyOtpParams(otp: otp));
    result.fold(
      (failure) =>
          emit(AuthError('OTP Verification Failed: ${failure.message}')),
      (resetToken) {
        _resetToken = resetToken;
        emit(const ForgotPasswordOtpVerified());
      },
    );
  }

  Future<void> resendOtp() async {
    final result = await _resendOtpUseCase(NoParams());
    result.fold(
      (failure) => emit(AuthError('Resend OTP Failed: ${failure.message}')),
      (_) => emit(AuthSuccess()),
    );
  }

  Future<void> resetPassword(String newPassword) async {
    if (_resetToken == null) {
      emit(const AuthError("No reset token. Please verify OTP again."));
      return;
    }
    emit(AuthLoading());
    final result = await _resetPasswordUseCase(
      ResetPasswordParams(resetToken: _resetToken!, newPassword: newPassword),
    );
    result.fold(
      (failure) => emit(AuthError('Reset Password Failed: ${failure.message}')),
      (_) {
        _resetToken = null;
        emit(PasswordResetSuccess());
      },
    );
  }
}
