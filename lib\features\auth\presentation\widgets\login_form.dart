import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:get_storage/get_storage.dart';
import 'package:go_router/go_router.dart';
import 'package:golderhr/core/extensions/l10n_extension.dart';
import 'package:golderhr/shared/widgets/show_custom_snackBar.dart';

import '../../../../core/responsive/responsive.dart';
import '../../../../core/routes/app_routes.dart';
import '../../../../core/services/biometric_service.dart';
import '../../../../core/services/flutter_secure_storage.dart';
import '../../../../shared/theme/app_colors.dart';
import '../../../../shared/utils/validators.dart';
import '../../../../shared/widgets/button_custom.dart';
import '../../../../shared/widgets/text_field_custom.dart';
import '../cubit/auth_cubit.dart';
import '../cubit/auth_state.dart';
import 'field_label.dart';

class LoginForm extends StatefulWidget {
  const LoginForm({super.key});

  @override
  State<LoginForm> createState() => _LoginFormState();
}

class _LoginFormState extends State<LoginForm> {
  final formKey = GlobalKey<FormState>();
  final emailController = TextEditingController();
  final passwordController = TextEditingController();
  final emailFocusNode = FocusNode();
  final passwordFocusNode = FocusNode();
  bool isPasswordObscured = true;
  bool isRememberMe = false;
  bool _canUseBiometrics = false;

  @override
  void initState() {
    super.initState();
    _initializeFields();
    _checkBiometrics();
  }

  Future<void> _initializeFields() async {
    final storage = GetStorage();
    final secureStorage = GetIt.I<SecureStorageService>();
    final isRememberMe = storage.read('is_remember_me') ?? false;
    if (isRememberMe) {
      final email = await secureStorage.read('remembered_email');
      final password = await secureStorage.read('remembered_password');
      emailController.text = email ?? '';
      passwordController.text = password ?? '';
      setState(() => this.isRememberMe = true);
    }
  }

  void _checkBiometrics() async {
    final biometricService = GetIt.I<BiometricService>();
    _canUseBiometrics = await biometricService.canCheckBiometrics();
    if (mounted) setState(() {});
  }

  @override
  void dispose() {
    emailController.dispose();
    passwordController.dispose();
    emailFocusNode.dispose();
    passwordFocusNode.dispose();
    super.dispose();
  }

  void _onLoginPressed() {
    if (formKey.currentState!.validate()) {
      context.read<AuthCubit>().login(
        emailController.text.trim(),
        passwordController.text,
        rememberMe: isRememberMe,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final responsive = Responsive.of(context);
    return BlocListener<AuthCubit, AuthState>(
      listener: (context, state) {
        if (state is AuthSuccess || state is Authenticated) {
          context.go(AppRoutes.home);
          showTopSnackBar(
            context,
            title: context.l10n.success,
            message: context.l10n.loginSuccess,
          );
        } else if (state is AuthError) {
          showTopSnackBar(
            context,
            title: context.l10n.error,
            message: state.message,
            isError: true,
          );
        }
      },
      child: Form(
        key: formKey,
        autovalidateMode: AutovalidateMode.onUserInteraction,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            FieldLabel(label: context.l10n.loginEmail),
            SizedBox(height: responsive.scaleHeight(8)),
            _buildEmailField(),
            SizedBox(height: responsive.scaleHeight(10)),
            FieldLabel(label: context.l10n.loginPassword),
            SizedBox(height: responsive.scaleHeight(8)),
            _buildPasswordField(),
            SizedBox(height: responsive.scaleHeight(5)),
            _buildRememberMeRow(),
            SizedBox(height: responsive.scaleHeight(10)),
            Row(
              children: [
                Expanded(child: _buildSignInButton()),
                if (_canUseBiometrics) ...[
                  SizedBox(width: responsive.scaleWidth(10)),
                  BiometricLoginButton(),
                ],
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmailField() {
    final responsive = Responsive.of(context);
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(responsive.scaleRadius(12)),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha((0.04 * 255).round()),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: TextFieldCustom(
        hintText: context.l10n.loginHintEmail,
        controller: emailController,
        prefixIcon: Icons.email_outlined,
        validator: (value) => Validators.validateEmail(value, context),
        focusNode: emailFocusNode,
      ),
    );
  }

  Widget _buildPasswordField() {
    final responsive = Responsive.of(context);
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(responsive.scaleRadius(12)),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha((0.04 * 255).round()),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: TextFieldCustom(
        hintText: context.l10n.loginHintPassword,
        controller: passwordController,
        prefixIcon: Icons.lock_outline_rounded,
        suffixIcon: isPasswordObscured
            ? Icons.visibility_off_outlined
            : Icons.visibility_outlined,
        obscureText: isPasswordObscured,
        validator: (value) => Validators.validatePassword(value, context),
        focusNode: passwordFocusNode,
        onSuffixIconPressed: () =>
            setState(() => isPasswordObscured = !isPasswordObscured),
      ),
    );
  }

  Widget _buildRememberMeRow() {
    final responsive = Responsive.of(context);
    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        SizedBox(
          width: responsive.scaleWidth(20),
          height: responsive.scaleWidth(20),
          child: Transform.scale(
            scale: responsive.scaleWidth(0.8, landscapeScaleFactor: 1),
            alignment: Alignment.center,
            child: Checkbox(
              value: isRememberMe,
              onChanged: (value) =>
                  setState(() => isRememberMe = value ?? false),
              activeColor: AppColors.primary,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(4),
              ),
            ),
          ),
        ),
        SizedBox(width: responsive.scaleWidth(4)),
        Text(
          context.l10n.loginRememberMe,
          style: TextStyle(
            fontSize: responsive.fontSize(13),
            color: const Color(0xFF4A5568),
            fontWeight: FontWeight.w500,
          ),
        ),
        const Spacer(),
        TextButton(
          onPressed: () {
            context.push(AppRoutes.forgotPassword);
          },
          child: Text(
            context.l10n.loginForgotPassword,
            style: TextStyle(
              fontSize: responsive.fontSize(13),
              color: AppColors.primary,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildSignInButton() {
    final responsive = Responsive.of(context);
    final buttonHeight = responsive.adaptiveValue(
      mobile: responsive.scaleHeight(52),
      tablet: responsive.scaleHeight(60),
      mobileLandscape: responsive.scaleHeight(52, landscapeScaleFactor: 1.7),
      tabletLandscape: responsive.scaleHeight(60),
    );

    return BlocBuilder<AuthCubit, AuthState>(
      builder: (context, state) {
        final isLoading = state is AuthLoading;
        return Container(
          height: buttonHeight,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(responsive.scaleRadius(12)),
            boxShadow: [
              if (!isLoading)
                BoxShadow(
                  color: AppColors.primary.withAlpha((0.3 * 255).round()),
                  blurRadius: responsive.scaleRadius(12),
                  offset: Offset(0, responsive.scaleRadius(6)),
                ),
            ],
          ),
          child: ButtonCustom(
            text: context.l10n.loginSignIn,
            isLoading: isLoading,
            onPressed: isLoading ? null : _onLoginPressed,
            borderRadius: responsive.scaleRadius(12),
          ),
        );
      },
    );
  }
}

class BiometricLoginButton extends StatelessWidget {
  const BiometricLoginButton({super.key});

  @override
  Widget build(BuildContext context) {
    final responsive = Responsive.of(context);
    final buttonHeight = responsive.adaptiveValue(
      mobile: responsive.scaleHeight(52),
      tablet: responsive.scaleHeight(60),
      mobileLandscape: responsive.scaleHeight(52, landscapeScaleFactor: 1.7),
      tabletLandscape: responsive.scaleHeight(60),
    );

    return BlocBuilder<AuthCubit, AuthState>(
      builder: (context, state) {
        final isLoading = state is AuthLoading;
        return Container(
          height: buttonHeight,
          width: buttonHeight,
          decoration: BoxDecoration(
            color: isLoading ? Colors.grey : AppColors.primary,
            borderRadius: BorderRadius.circular(responsive.scaleRadius(12)),
            boxShadow: [
              BoxShadow(
                color: AppColors.primary.withAlpha((0.3 * 255).round()),
                blurRadius: responsive.scaleRadius(12),
                offset: Offset(0, responsive.scaleRadius(6)),
              ),
            ],
          ),
          child: InkWell(
            onTap: isLoading
                ? null
                : () => context.read<AuthCubit>().loginWithBiometric(),
            borderRadius: BorderRadius.circular(responsive.scaleRadius(12)),
            child: Icon(
              Icons.fingerprint,
              color: Colors.white,
              size: responsive.scaleWidth(24),
            ),
          ),
        );
      },
    );
  }
}
