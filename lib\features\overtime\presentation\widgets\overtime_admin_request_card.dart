import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:golderhr/core/extensions/l10n_extension.dart';
import 'package:golderhr/core/extensions/responsive_extension.dart';
import 'package:intl/intl.dart';
import '../../../../../shared/theme/app_colors.dart';
import '../../domain/entities/overtime_request_entity.dart';
import '../cubit/overtime_admin_cubit.dart';
import 'overtime_rejection_dialog.dart';

class OvertimeAdminRequestCard extends StatefulWidget {
  final OvertimeRequestEntity request;
  final bool isProcessing;

  const OvertimeAdminRequestCard({
    super.key,
    required this.request,
    required this.isProcessing,
  });

  @override
  State<OvertimeAdminRequestCard> createState() =>
      _OvertimeAdminRequestCardState();
}

class _OvertimeAdminRequestCardState extends State<OvertimeAdminRequestCard>
    with TickerProviderStateMixin {
  late AnimationController _fadeController;
  late AnimationController _scaleController;
  late Animation<double> _fadeAnimation;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );
    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 400),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _fadeController, curve: Curves.easeInOut),
    );

    _scaleAnimation = Tween<double>(begin: 0.95, end: 1.0).animate(
      CurvedAnimation(parent: _scaleController, curve: Curves.easeOutBack),
    );

    // Start animations
    _fadeController.forward();
    _scaleController.forward();
  }

  @override
  void dispose() {
    _fadeController.dispose();
    _scaleController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final responsive = context.responsive;
    final l10n = context.l10n;
    final theme = Theme.of(context);

    return FadeTransition(
      opacity: _fadeAnimation,
      child: ScaleTransition(
        scale: _scaleAnimation,
        child: Container(
          margin: responsive.padding(vertical: 8, horizontal: 6),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [Colors.white, Colors.grey.shade50],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            borderRadius: BorderRadius.circular(20),
            boxShadow: [
              BoxShadow(
                color: _getStatusColor(
                  widget.request.status,
                ).withValues(alpha: 0.1),
                blurRadius: 20,
                offset: const Offset(0, 8),
                spreadRadius: 0,
              ),
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.05),
                blurRadius: 10,
                offset: const Offset(0, 2),
              ),
            ],
            border: Border.all(
              color: _getStatusColor(
                widget.request.status,
              ).withValues(alpha: 0.2),
              width: 1,
            ),
          ),
          child: Padding(
            padding: responsive.padding(all: 10),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header with employee name and status
                Row(
                  children: [
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            widget.request.employeeName,
                            style: theme.textTheme.headlineSmall?.copyWith(
                              fontWeight: FontWeight.w700,
                              color: AppColors.textPrimary,
                              fontSize: 20,
                            ),
                          ),
                          SizedBox(height: responsive.heightPercentage(0.5)),
                          Container(
                            padding: responsive.padding(
                              horizontal: 8,
                              vertical: 4,
                            ),
                            decoration: BoxDecoration(
                              color: AppColors.primaryBlue.withValues(
                                alpha: 0.1,
                              ),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Text(
                              _formatDate(widget.request.date),
                              style: theme.textTheme.bodySmall?.copyWith(
                                color: AppColors.primaryBlue,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                    _StatusChip(status: widget.request.status.name),
                  ],
                ),

                SizedBox(height: responsive.heightPercentage(2)),

                // Time and duration
                Row(
                  children: [
                    Expanded(
                      flex: 3,
                      child: _InfoItem(
                        icon: Icons.access_time_rounded,
                        label: l10n.time,
                        value: _formatTimeRange(
                          widget.request.startTime,
                          widget.request.endTime,
                        ),
                      ),
                    ),
                    SizedBox(width: responsive.widthPercentage(2)),
                    Expanded(
                      flex: 2,
                      child: _InfoItem(
                        icon: Icons.schedule_rounded,
                        label: l10n.duration,
                        value: '${widget.request.hours.toStringAsFixed(1)}h',
                      ),
                    ),
                  ],
                ),

                SizedBox(height: responsive.heightPercentage(2)),

                // Reason
                _InfoItem(
                  icon: Icons.description_rounded,
                  label: l10n.reason,
                  value: widget.request.reason,
                  maxLines: 3,
                ),

                if (widget.request.rejectionReason != null) ...[
                  SizedBox(height: responsive.heightPercentage(1.5)),
                  _InfoItem(
                    icon: Icons.cancel_rounded,
                    label: l10n.rejectionReason,
                    value: widget.request.rejectionReason!,
                    maxLines: 3,
                    valueColor: Colors.red[700],
                  ),
                ],

                const SizedBox(height: 16),

                // Action buttons (only for pending requests)
                if (widget.request.status == OvertimeStatus.pending) ...[
                  SizedBox(height: responsive.heightPercentage(2)),
                  Row(
                    children: [
                      Expanded(
                        child: AnimatedContainer(
                          duration: const Duration(milliseconds: 200),
                          child: OutlinedButton.icon(
                            onPressed: widget.isProcessing
                                ? null
                                : () => _showRejectDialog(context),
                            icon: const Icon(Icons.close_rounded, size: 18),
                            label: Text(l10n.reject),
                            style: OutlinedButton.styleFrom(
                              foregroundColor: Colors.red,
                              side: const BorderSide(
                                color: Colors.red,
                                width: 2,
                              ),
                              padding: responsive.padding(vertical: 16),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                              textStyle: const TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ),
                        ),
                      ),
                      SizedBox(width: responsive.widthPercentage(3)),
                      Expanded(
                        child: AnimatedContainer(
                          duration: const Duration(milliseconds: 200),
                          child: ElevatedButton.icon(
                            onPressed: widget.isProcessing
                                ? null
                                : () => _approveRequest(context),
                            icon: const Icon(Icons.check_rounded, size: 18),
                            label: Text(l10n.approve),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.green,
                              foregroundColor: Colors.white,
                              padding: responsive.padding(vertical: 16),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                              elevation: 2,
                              textStyle: const TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ],

                // Approval info for processed requests
                if (widget.request.status != OvertimeStatus.pending &&
                    widget.request.approvedAt != null) ...[
                  Divider(height: responsive.heightPercentage(3)),
                  Container(
                    padding: responsive.padding(all: 12),
                    decoration: BoxDecoration(
                      color: widget.request.status == OvertimeStatus.approved
                          ? Colors.green.withValues(alpha: 0.1)
                          : Colors.red.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: widget.request.status == OvertimeStatus.approved
                            ? Colors.green.withValues(alpha: 0.3)
                            : Colors.red.withValues(alpha: 0.3),
                      ),
                    ),
                    child: Row(
                      children: [
                        Icon(
                          widget.request.status == OvertimeStatus.approved
                              ? Icons.check_circle_rounded
                              : Icons.cancel_rounded,
                          size: 18,
                          color:
                              widget.request.status == OvertimeStatus.approved
                              ? Colors.green
                              : Colors.red,
                        ),
                        SizedBox(width: responsive.widthPercentage(2)),
                        Expanded(
                          child: Text(
                            '${widget.request.status == OvertimeStatus.approved ? l10n.approvedBy : l10n.rejectedBy} • ${_formatDateTime(widget.request.approvedAt!)}',
                            style: theme.textTheme.bodySmall?.copyWith(
                              color:
                                  widget.request.status ==
                                      OvertimeStatus.approved
                                  ? Colors.green.shade700
                                  : Colors.red.shade700,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _approveRequest(BuildContext context) {
    context.read<OvertimeAdminCubit>().approveRequest(widget.request.id);
  }

  void _showRejectDialog(BuildContext context) {
    // Get cubit reference before opening dialog
    final cubit = context.read<OvertimeAdminCubit>();

    showDialog(
      context: context,
      builder: (dialogContext) => OvertimeRejectionDialog(
        onReject: (reason) {
          // Use the cubit reference instead of trying to read from dialog context
          cubit.rejectRequest(widget.request.id, reason);
        },
      ),
    );
  }

  // Helper methods
  Color _getStatusColor(OvertimeStatus status) {
    switch (status) {
      case OvertimeStatus.pending:
        return Colors.orange;
      case OvertimeStatus.approved:
        return Colors.green;
      case OvertimeStatus.rejected:
        return Colors.red;
    }
  }

  String _formatDate(DateTime date) {
    return DateFormat('dd/MM/yyyy').format(date);
  }

  String _formatTimeRange(DateTime startTime, DateTime endTime) {
    // Fix timezone issue - add 6 hours to correct the offset (UTC+7 Vietnam time)
    final correctedStart = startTime.add(const Duration(hours: 7));
    final correctedEnd = endTime.add(const Duration(hours: 7));

    final start = DateFormat('HH:mm').format(correctedStart);
    final end = DateFormat('HH:mm').format(correctedEnd);
    return '$start - $end';
  }

  String _formatDateTime(DateTime dateTime) {
    return DateFormat('dd/MM/yyyy HH:mm').format(dateTime);
  }
}

class _StatusChip extends StatelessWidget {
  final String status;

  const _StatusChip({required this.status});

  @override
  Widget build(BuildContext context) {
    final l10n = context.l10n;
    Color color;
    IconData icon;
    String statusText;

    switch (status) {
      case 'pending':
        color = Colors.orange;
        icon = Icons.schedule_rounded;
        statusText = l10n.pending;
        break;
      case 'approved':
        color = Colors.green;
        icon = Icons.check_circle_rounded;
        statusText = l10n.approved;
        break;
      case 'rejected':
        color = Colors.red;
        icon = Icons.cancel_rounded;
        statusText = l10n.rejected;
        break;
      default:
        color = Colors.grey;
        icon = Icons.help_rounded;
        statusText = status;
    }

    return AnimatedContainer(
      duration: const Duration(milliseconds: 200),
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [color.withValues(alpha: 0.1), color.withValues(alpha: 0.05)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(color: color.withValues(alpha: 0.3), width: 1.5),
        boxShadow: [
          BoxShadow(
            color: color.withValues(alpha: 0.2),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 16, color: color),
          const SizedBox(width: 6),
          Text(
            statusText,
            style: TextStyle(
              color: color,
              fontSize: 12,
              fontWeight: FontWeight.w700,
            ),
          ),
        ],
      ),
    );
  }
}

class _InfoItem extends StatelessWidget {
  final IconData icon;
  final String label;
  final String value;
  final int maxLines;
  final Color? valueColor;

  const _InfoItem({
    required this.icon,
    required this.label,
    required this.value,
    this.maxLines = 1,
    this.valueColor,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Container(
      padding: const EdgeInsets.all(10),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: AppColors.primaryBlue.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(10),
            ),
            child: Icon(icon, size: 20, color: AppColors.primaryBlue),
          ),
          const SizedBox(width: 10),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: AppColors.textSecondary,
                    fontWeight: FontWeight.w600,
                    fontSize: 13,
                  ),
                ),
                const SizedBox(height: 6),
                Text(
                  value,
                  style: theme.textTheme.bodyLarge?.copyWith(
                    color: valueColor ?? AppColors.textPrimary,
                    fontWeight: FontWeight.w700,
                    fontSize: 16,
                  ),
                  maxLines: maxLines,
                  overflow: TextOverflow.visible,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
