import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import '../../domain/entities/department_entity.dart';
import '../../domain/usecases/department_usecases.dart';
import '../../data/models/department_model.dart';
import '../../../../core/usecases/usecase.dart';

// Department State
class DepartmentState extends Equatable {
  final bool isLoading;
  final bool isProcessing;
  final List<DepartmentEntity> departments;
  final List<DepartmentEntity> dropdownDepartments;
  final List<DepartmentEntity> hierarchyDepartments;
  final DepartmentListResult? departmentListResult;
  final DepartmentEntity? selectedDepartment;
  final String? error;
  final String? successMessage;
  final int currentPage;
  final bool hasReachedMax;
  final bool includeDeleted;

  const DepartmentState({
    this.isLoading = false,
    this.isProcessing = false,
    this.departments = const [],
    this.dropdownDepartments = const [],
    this.hierarchyDepartments = const [],
    this.departmentListResult,
    this.selectedDepartment,
    this.error,
    this.successMessage,
    this.currentPage = 1,
    this.hasReachedMax = false,
    this.includeDeleted = false,
  });

  DepartmentState copyWith({
    bool? isLoading,
    bool? isProcessing,
    List<DepartmentEntity>? departments,
    List<DepartmentEntity>? dropdownDepartments,
    List<DepartmentEntity>? hierarchyDepartments,
    DepartmentListResult? departmentListResult,
    DepartmentEntity? selectedDepartment,
    String? error,
    String? successMessage,
    int? currentPage,
    bool? hasReachedMax,
    bool? includeDeleted,
  }) {
    return DepartmentState(
      isLoading: isLoading ?? this.isLoading,
      isProcessing: isProcessing ?? this.isProcessing,
      departments: departments ?? this.departments,
      dropdownDepartments: dropdownDepartments ?? this.dropdownDepartments,
      hierarchyDepartments: hierarchyDepartments ?? this.hierarchyDepartments,
      departmentListResult: departmentListResult ?? this.departmentListResult,
      selectedDepartment: selectedDepartment ?? this.selectedDepartment,
      error: error,
      successMessage: successMessage,
      currentPage: currentPage ?? this.currentPage,
      hasReachedMax: hasReachedMax ?? this.hasReachedMax,
      includeDeleted: includeDeleted ?? this.includeDeleted,
    );
  }

  bool get hasError => error != null;
  bool get hasSuccess => successMessage != null;

  @override
  List<Object?> get props => [
    isLoading,
    isProcessing,
    departments,
    dropdownDepartments,
    hierarchyDepartments,
    departmentListResult,
    selectedDepartment,
    error,
    successMessage,
    currentPage,
    hasReachedMax,
    includeDeleted,
  ];
}

// Department Cubit
class DepartmentCubit extends Cubit<DepartmentState> {
  final GetAllDepartments getAllDepartments;
  final GetDepartmentById getDepartmentById;
  final CreateDepartment createDepartment;
  final UpdateDepartment updateDepartment;
  final DeleteDepartment deleteDepartment;
  final RestoreDepartment restoreDepartment;
  final ToggleDepartmentStatus toggleDepartmentStatus;
  final GetDepartmentsForDropdown getDepartmentsForDropdown;
  final GetDepartmentHierarchy getDepartmentHierarchy;

  DepartmentCubit({
    required this.getAllDepartments,
    required this.getDepartmentById,
    required this.createDepartment,
    required this.updateDepartment,
    required this.deleteDepartment,
    required this.restoreDepartment,
    required this.toggleDepartmentStatus,
    required this.getDepartmentsForDropdown,
    required this.getDepartmentHierarchy,
  }) : super(const DepartmentState());

  // Get all departments with pagination
  Future<void> loadDepartments({
    int page = 1,
    int limit = 10,
    String? search,
    bool includeDeleted = false,
    bool refresh = false,
  }) async {
    if (refresh) {
      emit(
        state.copyWith(
          isLoading: true,
          error: null,
          currentPage: 1,
          hasReachedMax: false,
          includeDeleted: includeDeleted,
        ),
      );
    } else if (page == 1) {
      emit(
        state.copyWith(
          isLoading: true,
          error: null,
          includeDeleted: includeDeleted,
        ),
      );
    }

    final result = await getAllDepartments(
      GetAllDepartmentsParams(
        page: page,
        limit: limit,
        search: search,
        includeDeleted: includeDeleted,
      ),
    );

    result.fold(
      (failure) =>
          emit(state.copyWith(isLoading: false, error: failure.message)),
      (departmentListResult) {
        final newDepartments = page == 1
            ? departmentListResult.departments.cast<DepartmentEntity>()
            : [
                ...state.departments,
                ...departmentListResult.departments.cast<DepartmentEntity>(),
              ];

        emit(
          state.copyWith(
            isLoading: false,
            departments: newDepartments,
            departmentListResult: departmentListResult,
            currentPage: page,
            hasReachedMax: departmentListResult.departments.length < limit,
            error: null,
          ),
        );
      },
    );
  }

  // Load more departments for pagination
  Future<void> loadMoreDepartments({String? search}) async {
    if (state.hasReachedMax || state.isLoading) return;

    await loadDepartments(
      page: state.currentPage + 1,
      search: search,
      includeDeleted: state.includeDeleted,
    );
  }

  // Get department by ID
  Future<void> loadDepartmentById(String departmentId) async {
    emit(state.copyWith(isLoading: true, error: null));

    final result = await getDepartmentById(departmentId);

    result.fold(
      (failure) =>
          emit(state.copyWith(isLoading: false, error: failure.message)),
      (department) => emit(
        state.copyWith(
          isLoading: false,
          selectedDepartment: department,
          error: null,
        ),
      ),
    );
  }

  // Create new department
  Future<void> createNewDepartment({
    required String name,
    String? description,
    String? code,
    String? parentId,
  }) async {
    emit(state.copyWith(isProcessing: true, error: null));

    final result = await createDepartment(
      CreateDepartmentParams(
        name: name,
        description: description,
        code: code,
        parentId: parentId,
      ),
    );

    result.fold(
      (failure) =>
          emit(state.copyWith(isProcessing: false, error: failure.message)),
      (department) {
        final updatedDepartments = [department, ...state.departments];
        emit(
          state.copyWith(
            isProcessing: false,
            departments: updatedDepartments,
            successMessage: 'Department created successfully',
            error: null,
          ),
        );
      },
    );
  }

  // Update department
  Future<void> updateExistingDepartment({
    required String departmentId,
    String? name,
    String? description,
    String? code,
    bool? isActive,
    bool? isDisabled,
    String? parentId,
  }) async {
    emit(state.copyWith(isProcessing: true, error: null));

    final result = await updateDepartment(
      UpdateDepartmentParams(
        departmentId: departmentId,
        name: name,
        description: description,
        code: code,
        isActive: isActive,
        isDisabled: isDisabled,
        parentId: parentId,
      ),
    );

    result.fold(
      (failure) =>
          emit(state.copyWith(isProcessing: false, error: failure.message)),
      (updatedDepartment) {
        final updatedDepartments = state.departments.map((dept) {
          return dept.id == departmentId ? updatedDepartment : dept;
        }).toList();

        emit(
          state.copyWith(
            isProcessing: false,
            departments: updatedDepartments,
            selectedDepartment: updatedDepartment,
            successMessage: 'Department updated successfully',
            error: null,
          ),
        );
      },
    );
  }

  // Delete department
  Future<void> deleteExistingDepartment(String departmentId) async {
    emit(state.copyWith(isProcessing: true, error: null));

    final result = await deleteDepartment(departmentId);

    result.fold(
      (failure) =>
          emit(state.copyWith(isProcessing: false, error: failure.message)),
      (_) {
        final updatedDepartments = state.departments.map((dept) {
          return dept.id == departmentId
              ? DepartmentModel(
                  id: dept.id,
                  name: dept.name,
                  description: dept.description,
                  code: dept.code,
                  isActive: dept.isActive,
                  isDeleted: true, // Mark as deleted
                  isDisabled: dept.isDisabled,
                  parentId: dept.parentId,
                  idMapper: dept.idMapper,
                  codeMapper: dept.codeMapper,
                  createdAt: dept.createdAt,
                  updatedAt: dept.updatedAt,
                )
              : dept;
        }).toList();

        emit(
          state.copyWith(
            isProcessing: false,
            departments: updatedDepartments,
            successMessage: 'Department deleted successfully',
            error: null,
          ),
        );
      },
    );
  }

  // Restore department
  Future<void> restoreExistingDepartment(String departmentId) async {
    emit(state.copyWith(isProcessing: true, error: null));

    final result = await restoreDepartment(departmentId);

    result.fold(
      (failure) =>
          emit(state.copyWith(isProcessing: false, error: failure.message)),
      (restoredDepartment) {
        final updatedDepartments = state.departments.map((dept) {
          return dept.id == departmentId ? restoredDepartment : dept;
        }).toList();

        emit(
          state.copyWith(
            isProcessing: false,
            departments: updatedDepartments,
            successMessage: 'Department restored successfully',
            error: null,
          ),
        );
      },
    );
  }

  // Toggle department status
  Future<void> toggleExistingDepartmentStatus(String departmentId) async {
    emit(state.copyWith(isProcessing: true, error: null));

    final result = await toggleDepartmentStatus(departmentId);

    result.fold(
      (failure) =>
          emit(state.copyWith(isProcessing: false, error: failure.message)),
      (updatedDepartment) {
        final updatedDepartments = state.departments.map((dept) {
          return dept.id == departmentId ? updatedDepartment : dept;
        }).toList();

        emit(
          state.copyWith(
            isProcessing: false,
            departments: updatedDepartments,
            successMessage: 'Department status updated successfully',
            error: null,
          ),
        );
      },
    );
  }

  // Load departments for dropdown
  Future<void> loadDepartmentsForDropdown() async {
    final result = await getDepartmentsForDropdown(NoParams());

    result.fold(
      (failure) => emit(state.copyWith(error: failure.message)),
      (departments) =>
          emit(state.copyWith(dropdownDepartments: departments, error: null)),
    );
  }

  // Load department hierarchy
  Future<void> loadDepartmentHierarchy() async {
    emit(state.copyWith(isLoading: true, error: null));

    final result = await getDepartmentHierarchy(NoParams());

    result.fold(
      (failure) =>
          emit(state.copyWith(isLoading: false, error: failure.message)),
      (departments) => emit(
        state.copyWith(
          isLoading: false,
          hierarchyDepartments: departments,
          error: null,
        ),
      ),
    );
  }

  // Toggle include deleted filter
  void toggleIncludeDeleted() {
    final newIncludeDeleted = !state.includeDeleted;
    refreshDepartments(includeDeleted: newIncludeDeleted);
  }

  // Clear messages
  void clearMessages() {
    emit(state.copyWith(error: null, successMessage: null));
  }

  // Clear selected department
  void clearSelectedDepartment() {
    emit(state.copyWith(selectedDepartment: null));
  }

  // Refresh departments
  Future<void> refreshDepartments({
    String? search,
    bool? includeDeleted,
  }) async {
    await loadDepartments(
      page: 1,
      search: search,
      includeDeleted: includeDeleted ?? state.includeDeleted,
      refresh: true,
    );
  }
}
