import 'dart:convert';
import 'package:flutter/material.dart';
import '../../domain/entities/calendar_event.dart';

/// Model cho CalendarEvent, kế thừa từ entity và thêm các phương thức serialization
class CalendarEventModel extends CalendarEvent {
  const CalendarEventModel({
    required super.id,
    required super.title,
    required super.description,
    required super.date,
    required super.time,
    required super.type,
    required super.color,
    super.isAllDay,
    super.startTime,
    super.endTime,
    super.location,
    super.attendees,
    super.isRecurring,
    super.recurrenceRule,
    required super.createdAt,
    required super.updatedAt,
  });

  /// Tạo CalendarEventModel từ JSON
  factory CalendarEventModel.fromJson(Map<String, dynamic> json) {
    return CalendarEventModel(
      id: json['id'] as String,
      title: json['title'] as String,
      description: json['description'] as String? ?? '',
      date: DateTime.parse(json['date'] as String),
      time: json['time'] as String,
      type: EventType.values.firstWhere(
        (e) => e.name == json['type'],
        orElse: () => EventType.event,
      ),
      color: Color(json['color'] as int),
      isAllDay: json['isAllDay'] as bool? ?? false,
      startTime: json['startTime'] != null
          ? DateTime.parse(json['startTime'] as String)
          : null,
      endTime: json['endTime'] != null
          ? DateTime.parse(json['endTime'] as String)
          : null,
      location: json['location'] as String?,
      attendees:
          (json['attendees'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          [],
      isRecurring: json['isRecurring'] as bool? ?? false,
      recurrenceRule: json['recurrenceRule'] as String?,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
    );
  }

  /// Chuyển đổi CalendarEventModel thành JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'date': date.toIso8601String(),
      'time': time,
      'type': type.name,
      'color': color.value,
      'isAllDay': isAllDay,
      'startTime': startTime?.toIso8601String(),
      'endTime': endTime?.toIso8601String(),
      'location': location,
      'attendees': attendees,
      'isRecurring': isRecurring,
      'recurrenceRule': recurrenceRule,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  /// Tạo CalendarEventModel từ CalendarEvent entity
  factory CalendarEventModel.fromEntity(CalendarEvent event) {
    return CalendarEventModel(
      id: event.id,
      title: event.title,
      description: event.description,
      date: event.date,
      time: event.time,
      type: event.type,
      color: event.color,
      isAllDay: event.isAllDay,
      startTime: event.startTime,
      endTime: event.endTime,
      location: event.location,
      attendees: event.attendees,
      isRecurring: event.isRecurring,
      recurrenceRule: event.recurrenceRule,
      createdAt: event.createdAt,
      updatedAt: event.updatedAt,
    );
  }

  /// Chuyển đổi thành CalendarEvent entity
  CalendarEvent toEntity() {
    return CalendarEvent(
      id: id,
      title: title,
      description: description,
      date: date,
      time: time,
      type: type,
      color: color,
      isAllDay: isAllDay,
      startTime: startTime,
      endTime: endTime,
      location: location,
      attendees: attendees,
      isRecurring: isRecurring,
      recurrenceRule: recurrenceRule,
      createdAt: createdAt,
      updatedAt: updatedAt,
    );
  }

  /// Copy với các thay đổi, trả về CalendarEventModel
  @override
  CalendarEventModel copyWith({
    String? id,
    String? title,
    String? description,
    DateTime? date,
    String? time,
    EventType? type,
    Color? color,
    bool? isAllDay,
    DateTime? startTime,
    DateTime? endTime,
    String? location,
    List<String>? attendees,
    bool? isRecurring,
    String? recurrenceRule,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return CalendarEventModel(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      date: date ?? this.date,
      time: time ?? this.time,
      type: type ?? this.type,
      color: color ?? this.color,
      isAllDay: isAllDay ?? this.isAllDay,
      startTime: startTime ?? this.startTime,
      endTime: endTime ?? this.endTime,
      location: location ?? this.location,
      attendees: attendees ?? this.attendees,
      isRecurring: isRecurring ?? this.isRecurring,
      recurrenceRule: recurrenceRule ?? this.recurrenceRule,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? DateTime.now(),
    );
  }

  /// Chuyển đổi từ JSON string
  factory CalendarEventModel.fromJsonString(String jsonString) {
    return CalendarEventModel.fromJson(json.decode(jsonString));
  }

  /// Chuyển đổi thành JSON string
  String toJsonString() {
    return json.encode(toJson());
  }
}
