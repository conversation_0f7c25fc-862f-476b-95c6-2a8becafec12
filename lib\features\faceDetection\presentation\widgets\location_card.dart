import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../core/responsive/responsive.dart';
import '../../../../l10n/app_localizations.dart';
import '../cubit/face_checkin_cubit.dart';
import '../cubit/face_checkin_state.dart';

class LocationCard extends StatelessWidget {
  const LocationCard({super.key});

  @override
  Widget build(BuildContext context) {
    final responsive = Responsive.of(context);
    final l10n = AppLocalizations.of(context)!;
    return BlocBuilder<FaceDetectionCubit, FaceDetectionState>(
      builder: (context, state) {
        return Container(
          padding: responsive.padding(all: 16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(responsive.fontSize(16)),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withAlpha(12),
                blurRadius: 10,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Row(
            children: [
              Container(
                padding: responsive.padding(all: 8),
                decoration: BoxDecoration(
                  color: Colors.green.withAlpha(25),
                  borderRadius: BorderRadius.circular(responsive.fontSize(8)),
                ),
                child: Icon(
                  Icons.location_on,
                  color: Colors.green.shade600,
                  size: responsive.fontSize(20),
                ),
              ),
              SizedBox(width: responsive.scaleWidth(12)),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      l10n.detectFaceCurrentLocation,
                      style: TextStyle(
                        fontSize: responsive.fontSize(12),
                        color: Colors.grey,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    SizedBox(height: responsive.scaleHeight(2)),
                    Text(
                      state.locationStatus.isEmpty
                          ? l10n.detectFaceGettingLocation
                          : state.locationStatus,
                      style: TextStyle(
                        fontSize: responsive.fontSize(13),
                        fontWeight: FontWeight.w600,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
